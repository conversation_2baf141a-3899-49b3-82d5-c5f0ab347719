---
description:
globs:
alwaysApply: false
---
# Règle : Classes et Fonctions Olist

## Objectif
Assurer la clarté, la maintenabilité et la robustesse du code Python.

## Règles
- Documenter toutes les classes avec des docstrings détaillés (style Google ou NumPy recommandé).
  ```python
  class CustomerSegmentation:
      """Gère la segmentation des clients Olist.

      Cette classe implémente les algorithmes de clustering pour segmenter
      les clients selon leurs comportements d'achat.

      Attributes:
          n_clusters (int): Nombre de clusters à créer
          random_state (int): Seed pour la reproductibilité
      """
  ```
- Utiliser le typage statique pour toutes les fonctions et méthodes :
  ```python
  def calculate_rfm_scores(self, df: pd.DataFrame) -> pd.DataFrame:
      """Calcule les scores RFM."""
  ```
