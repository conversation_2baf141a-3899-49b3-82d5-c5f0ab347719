---
description:
globs:
alwaysApply: false
---
# Règle Cursor : Export harmonisé des artefacts et figures

## Objectif
<PERSON>tir la traçabilité, la reproductibilité et la clarté des livrables du projet OPC5 en imposant une convention stricte d'export des artefacts (figures, modèles, résultats, cartes, etc.).

---

## 📦 Structure des dossiers d'export
- Tous les artefacts doivent être exportés dans le dossier `reports/` selon la structure suivante :

```
reports/
├── figures/         # Graphiques matplotlib, seaborn, etc.
├── maps/            # Cartes Folium, Plotly, etc.
├── models/          # Modèles entraînés (joblib, pkl)
├── metrics/         # Fichiers de métriques, logs
├── predictions/     # Prédictions, résultats de scoring
├── configs/         # Fichiers de configuration JSON
└── ...
```

---

## 🏷️ Convention de nommage harmonisée
- Tous les fichiers exportés doivent suivre la convention :

```
<notebook_name>_<export_number>_<base_name>.<ext>
```

- `notebook_number` : numéro du notebook ou script source (ex : `3`pour le notebook `3_clustering_segmentation`)
- `export_number` : numéro d'export dans le notebook (ex : `01`, `02`, ...)
- `base_name` : nom court et explicite du contenu (ex : `elbow_curve`, `rfm_profiles`)
- `ext` : extension du fichier (`png`, `csv`, `joblib`, ...)

**Exemple** : `3_04_elbow_curve.png`

---

## 🚦 Fonction d'export centralisée
- Utiliser systématiquement la fonction suivante pour toute figure :

```python
from utils.clustering_visualization import export_figure

fig = plot_elbow_curve(k_range, inertias, silhouette_scores)
export_figure(fig, notebook_name="3_clustering_segmentation", export_number=4, base_name="elbow_curve")
```

- Pour les modèles, résultats, etc., utiliser les fonctions du module `save_load.py` qui appliquent la même convention :

```python
from utils.save_load import save_model, save_results

save_model(model, notebook_name="3_clustering_segmentation", export_number=5, base_name="kmeans_model")
save_results(df_results, notebook_name="3_clustering_segmentation", export_number=6, base_name="cluster_metrics")
```

---

## Bonnes pratiques
- Toujours renseigner les arguments `notebook_number`, `export_number`, `base_name` pour chaque export.
- Pas d'espaces ni de caractères spéciaux dans les noms.
- Exporter dans le bon sous-dossier selon le type d'artefact.
- Documenter dans le notebook à quoi correspond chaque export (cellule markdown ou commentaire).
- Vérifier la présence du fichier exporté dans le dossier attendu.

---

## Références
- Voir la docstring de `export_figure` dans `utils/clustering_visualization.py`
- Voir les fonctions d'export dans `utils/save_load.py`
- Structure détaillée : `utils/data_tools.py` (fonction `generate_export_filename`)

---

_Cette règle est obligatoire pour tout livrable du projet OPC5._
