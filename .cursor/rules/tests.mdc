---
description:
globs:
alwaysApply: false
---
# Règle : Tests Olist

## Objectif
<PERSON>tir la fiabilité et la robustesse du code via des tests unitaires systématiques.

## Règles
- Créer des tests pour chaque fonction principale, en particulier dans `utils/`.
- Utiliser des jeux de données minimaux et contrôlés pour les tests.
- Les tests doivent être reproductibles et automatisables.

  **Exemple :**
  ```python
  def test_calculate_rfm_scores():
      """Test le calcul des scores RFM."""
      # Arrange
      test_data = pd.DataFrame({
          'customer_id': ['A', 'B'],
          'order_date': ['2023-01-01', '2023-02-01'],
          'amount': [100, 200]
      })
      # Act
      result = calculate_rfm_scores(test_data)
      # Assert
      assert 'rfm_score' in result.columns
  ```
