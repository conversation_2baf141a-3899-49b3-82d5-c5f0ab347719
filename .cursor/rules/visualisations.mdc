---
description:
globs:
alwaysApply: false
---
# Règle : Visualisations Olist

## Objectif
Assurer la cohérence, la lisibilité et l'impact des visualisations produites.

## Règles
- Utiliser des styles cohérents pour toutes les visualisations (ex : seaborn, couleurs harmonisées).
  ```python
  plt.style.use('seaborn')
  plt.rcParams['figure.figsize'] = (12, 8)
  plt.rcParams['font.size'] = 12
  ```
- Inclure des titres, labels et légendes explicites sur tous les graphiques.
  ```python
  plt.title('Distribution des Clients par Segment', fontsize=14)
  plt.xlabel('Score RFM', fontsize=12)
  plt.ylabel('Nombre de Clients', fontsize=12)
  ```
- Les visualisations doivent être de qualité professionnelle (soin des couleurs, lisibilité, annotations si besoin).
- Toutes les figures générées dans les notebooks doivent être exportées dans `reports/figures/` avec un nommage structuré (ex : `2_exploration_01.png`).
