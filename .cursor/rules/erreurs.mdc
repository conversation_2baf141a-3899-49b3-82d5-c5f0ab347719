---
description:
globs:
alwaysApply: false
---
# Règle : Gestion des Erreurs Olist

## Objectif
Assurer une gestion des erreurs explicite, informative et maintenable.

## Règles
- Utiliser des exceptions personnalisées pour les erreurs spécifiques au domaine Olist.
  ```python
  class OlistDataError(Exception):
      """Exception levée pour les erreurs de données Olist."""
      pass
  ```
- Gérer les erreurs de manière appropriée et explicite, avec journalisation si nécessaire.
  ```python
  try:
      result = process_data(df)
  except OlistDataError as e:
      logger.error(f"Erreur de données: {e}")
      raise
  ```
