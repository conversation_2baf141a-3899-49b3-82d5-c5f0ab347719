---
description:
globs:
alwaysApply: false
---
# Règle : Standards Notebooks Olist

## Objectif
<PERSON>r la lisibilité, la reproductibilité et la pédagogie des notebooks.

## Règles
- Structurer les notebooks avec des sections standardisées :
  1. Configuration et Imports
  2. Chargement des Données
  3. Préparation des Données
  4. Analyse Exploratoire
  5. Feature Engineering
  6. Modélisation
  7. Évaluation
  8. Visualisation
  9. Conclusions
- Inclure des cellules markdown explicatives avant chaque section majeure.
- Documenter les objectifs, hypothèses et conclusions dans des cellules markdown.
- Illustrer les notebooks avec des visualisations pertinentes et de qualité professionnelle dès que cela est utile à la compréhension.
- Exporter tous les éléments générés (figures, métriques, features, etc.) dans les dossiers appropriés sous `reports/`.
- Utiliser un nommage précis et clair pour chaque élément exporté, incluant le numéro du notebook et l'ordre de création (ex : `3_clusters_01.png`).
