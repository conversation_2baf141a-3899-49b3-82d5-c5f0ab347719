---
description:
globs:
alwaysApply: false
---
# Règle : Standards Données Olist

## Objectif
Assurer l'intégrité, la traçabilité et la reproductibilité des manipulations de données.

## Règles
- Ne jamais modifier les données brutes dans `data/raw/`.
- Toujours travailler sur une copie des données pour le prétraitement et l'analyse.
  ```python
  processed_df = raw_df.copy()
  ```
- Utiliser des chemins relatifs pour accéder aux données.
  ```python
  DATA_PATH = Path('data/raw/')
  ```
- Placer les données nettoyées dans `data/clean/` ou `data/processed/` selon le niveau de transformation.
- Tout fichier exporté (csv, pickle, etc.) doit avoir un nom explicite, incluant le numéro du notebook et l'ordre de création, par exemple : `2_cleaned_features_01.csv`.
