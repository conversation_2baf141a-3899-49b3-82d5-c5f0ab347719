---
description:
globs:
alwaysApply: false
---
# Règle : Standards SQL Olist

## Objectif
G<PERSON>tir la clarté, la maintenabilité et la performance des requêtes SQL.

## Règles
- Commenter toutes les requêtes complexes pour expliquer la logique métier.
  ```sql
  -- Calcul du score RFM par client
  SELECT ...
  ```
- Utiliser des CTEs (Common Table Expressions) pour structurer les requêtes longues ou imbriquées.
  ```sql
  WITH rfm_scores AS (
      SELECT ...
  )
  SELECT * FROM rfm_scores;
  ```
