{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 1 : Analyse exploratoire des données\n", "\n", "## Objectif\n", "<PERSON><PERSON><PERSON>, explorer et comprendre le jeu de données client pour préparer la segmentation. Inclure analyses univariée, bivariée et multivariée.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données\n", "\n", "### 1.1 Import des librairies de base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports standard\n", "import os\n", "import sqlite3\n", "from datetime import datetime\n", "from pathlib import Path\n", "\n", "# Imports tiers\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from scipy import stats\n", "\n", "# Configuration des visualisations\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR\n", "from utils.data_tools import (\n", "    load_database, get_table_names, load_table,\n", "    describe_missing, plot_missing_heatmap, plot_correlation_matrix,\n", "    detect_outliers_iqr, detect_outliers_zscore, export_artifact\n", ")\n", "from utils.analysis_tools import handle_missing_values, describe_missing_data\n", "from utils.clustering_visualization import export_figure\n", "\n", "# Configuration du notebook\n", "init_notebook()\n", "print(f\"📊 Notebook d'exploration des données - Projet Olist\")\n", "print(f\"🎯 Seed fixé à {SEED} pour la reproductibilité\")\n", "print(f\"📁 Dossier de sortie : {REPORTS_DIR}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du fichier client e-commerce"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données depuis la base SQLite\n", "data_path = 'data/raw/olist.db'\n", "\n", "# Connexion à la base de données\n", "conn = load_database(data_path)\n", "\n", "# Exploration des tables disponibles\n", "tables = get_table_names(conn)\n", "print(f\"📋 Tables disponibles dans la base : {tables}\")\n", "\n", "# Chargement des tables principales\n", "customers = load_table(conn, 'customers')\n", "orders = load_table(conn, 'orders')\n", "order_items = load_table(conn, 'order_items')\n", "order_reviews = load_table(conn, 'order_reviews')\n", "products = load_table(conn, 'products')\n", "\n", "print(f\"Dimensions des tables:\")\n", "print(f\"- Customers: {customers.shape}\")\n", "print(f\"- Orders: {orders.shape}\")\n", "print(f\"- Order Items: {order_items.shape}\")\n", "print(f\"- Order Reviews: {order_reviews.shape}\")\n", "print(f\"- Products: {products.shape}\")\n", "\n", "conn.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Aperçu rapide des données"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création d'une vue consolidée des données client\n", "# Jointure des tables pour créer un dataset d'analyse\n", "\n", "# Conversion des dates\n", "orders['order_purchase_timestamp'] = pd.to_datetime(orders['order_purchase_timestamp'])\n", "orders['order_delivered_customer_date'] = pd.to_datetime(orders['order_delivered_customer_date'])\n", "\n", "# Calcul des métriques par client\n", "customer_metrics = orders.groupby('customer_id').agg({\n", "    'order_id': 'count',  # <PERSON><PERSON><PERSON>\n", "    'order_purchase_timestamp': ['min', 'max'],  # Première et dernière commande\n", "}).round(2)\n", "\n", "# Aplatissement des colonnes multi-niveaux\n", "customer_metrics.columns = ['frequency', 'first_order_date', 'last_order_date']\n", "customer_metrics = customer_metrics.reset_index()\n", "\n", "# <PERSON><PERSON>l de la récence (jours depuis la dernière commande)\n", "reference_date = orders['order_purchase_timestamp'].max()\n", "customer_metrics['recency'] = (reference_date - customer_metrics['last_order_date']).dt.days\n", "\n", "# Jointure avec les informations client\n", "df = customer_metrics.merge(customers[['customer_id', 'customer_state', 'customer_city']],\n", "                           on='customer_id', how='left')\n", "\n", "print(f\"📊 Dataset consolidé créé : {df.shape}\")\n", "print(f\"\\n🔍 Aperçu des premières lignes :\")\n", "display(df.head())\n", "\n", "print(f\"\\n📊 Types de données :\")\n", "print(df.dtypes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.4 🚨 ANALYSE CRITIQUE DES STATUTS DE COMMANDES"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚨 ANALYSE CRITIQUE : Statuts de commandes et qualité des données\n", "print(\"🚨 ANALYSE CRITIQUE DES DONNÉES OLIST\")\n", "print(\"=\" * 50)\n", "\n", "# 1. Analyse des statuts de commandes\n", "print(\"\\n📊 ANALYSE DES STATUTS DE COMMANDES :\")\n", "status_analysis = orders['order_status'].value_counts()\n", "print(status_analysis)\n", "\n", "# Identification des commandes problématiques\n", "invalid_statuses = ['canceled', 'unavailable']\n", "invalid_orders = orders[orders['order_status'].isin(invalid_statuses)]\n", "valid_orders = orders[orders['order_status'] == 'delivered']\n", "\n", "print(f\"\\n⚠️ COMMANDES NON VALIDES :\")\n", "print(f\"- Commandes annulées (canceled) : {len(orders[orders['order_status'] == 'canceled'])}\")\n", "print(f\"- Commandes indisponibles (unavailable) : {len(orders[orders['order_status'] == 'unavailable'])}\")\n", "print(f\"- Total commandes non valides : {len(invalid_orders)} ({len(invalid_orders)/len(orders)*100:.1f}%)\")\n", "print(f\"\\n✅ COMMANDES VALIDES (delivered) : {len(valid_orders)} ({len(valid_orders)/len(orders)*100:.1f}%)\")\n", "\n", "# 2. <PERSON><PERSON><PERSON> des dates de livraison manquantes\n", "print(f\"\\n📅 ANALYSE DES DATES DE LIVRAISON :\")\n", "missing_delivery = orders['order_delivered_customer_date'].isnull().sum()\n", "print(f\"- Dates de livraison manquantes : {missing_delivery} ({missing_delivery/len(orders)*100:.1f}%)\")\n", "\n", "# Analyse croisée statut vs dates manquantes\n", "delivery_by_status = orders.groupby('order_status')['order_delivered_customer_date'].isnull().sum()\n", "print(f\"\\nDates manquantes par statut :\")\n", "for status, missing_count in delivery_by_status.items():\n", "    total_status = len(orders[orders['order_status'] == status])\n", "    pct = (missing_count / total_status * 100) if total_status > 0 else 0\n", "    print(f\"- {status}: {missing_count}/{total_status} ({pct:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.5 🚨 DÉCOUVERTE MAJEURE : Pattern \"1 Client = 1 Commande\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚨 VÉRIFICATION CRITIQUE : Nombre de commandes par client\n", "print(\"🚨 VÉRIFICATION PATTERN CLIENT-COMMANDE\")\n", "print(\"=\" * 50)\n", "\n", "# Analyse du nombre de commandes par client\n", "orders_per_customer = orders.groupby('customer_id').size()\n", "print(f\"\\n📊 STATISTIQUES COMMANDES PAR CLIENT :\")\n", "print(f\"- Nombre total de clients : {orders_per_customer.nunique():,}\")\n", "print(f\"- Nombre total de commandes : {len(orders):,}\")\n", "print(f\"- <PERSON><PERSON><PERSON> commandes par client : {orders_per_customer.mean():.2f}\")\n", "print(f\"- <PERSON><PERSON><PERSON><PERSON> commandes par client : {orders_per_customer.median():.2f}\")\n", "print(f\"- Maximum commandes par client : {orders_per_customer.max()}\")\n", "\n", "# Distribution détaillée\n", "print(f\"\\n📈 DISTRIBUTION DU NOMBRE DE COMMANDES :\")\n", "distribution = orders_per_customer.value_counts().sort_index()\n", "for nb_orders, nb_clients in distribution.head(10).items():\n", "    pct = (nb_clients / len(orders_per_customer)) * 100\n", "    print(f\"- {nb_orders} commande(s) : {nb_clients:,} clients ({pct:.1f}%)\")\n", "\n", "# ALERTE CRITIQUE\n", "mono_buyers = (orders_per_customer == 1).sum()\n", "multi_buyers = (orders_per_customer > 1).sum()\n", "\n", "print(f\"\\n🚨 DÉCOUVERTE MAJEURE :\")\n", "print(f\"- Clients avec 1 seule commande : {mono_buyers:,} ({mono_buyers/len(orders_per_customer)*100:.1f}%)\")\n", "print(f\"- Clients avec >1 commande : {multi_buyers:,} ({multi_buyers/len(orders_per_customer)*100:.1f}%)\")\n", "\n", "if mono_buyers / len(orders_per_customer) > 0.95:\n", "    print(f\"\\n🚨🚨🚨 ALERTE CRITIQUE 🚨🚨🚨\")\n", "    print(f\"PLUS DE 95% DES CLIENTS N'ONT QU'UNE SEULE COMMANDE !\")\n", "    print(f\"➡️ IMPACT : L'approche RFM classique est INADAPTÉE\")\n", "    print(f\"➡️ SOLUTION : Segmentation 'First Purchase' requise\")\n", "    print(f\"➡️ VARIABLES : Récence + Montant + Contexte (géo, temporel)\")\n", "    print(f\"➡️ STRATÉGIE : Acquisition/Réactivation au lieu de fidélisation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.6 Vérification des doublons et de la clé primaire"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🧽 NETTOYAGE DES DONNÉES selon la stratégie \"First Purchase\"\n", "print(\"🧽 NETTOYAGE ET FILTRAGE DES DONNÉES\")\n", "print(\"=\" * 50)\n", "\n", "# 1. FILTRAGE : G<PERSON><PERSON> uniquement les commandes livrées\n", "print(f\"\\n📊 AVANT FILTRAGE :\")\n", "print(f\"- Total commandes : {len(orders):,}\")\n", "print(f\"- Total clients : {orders['customer_id'].nunique():,}\")\n", "\n", "# Filtrer les commandes valides (livrées avec date de livraison)\n", "orders_clean = orders[\n", "    (orders['order_status'] == 'delivered') &\n", "    (orders['order_delivered_customer_date'].notna())\n", "].copy()\n", "\n", "print(f\"\\n📊 APRÈS FILTRAGE (commandes livrées uniquement) :\")\n", "print(f\"- Commandes valides : {len(orders_clean):,}\")\n", "print(f\"- Clients avec commandes valides : {orders_clean['customer_id'].nunique():,}\")\n", "print(f\"- Données supprimées : {len(orders) - len(orders_clean):,} commandes ({(len(orders) - len(orders_clean))/len(orders)*100:.1f}%)\")\n", "\n", "# 2. RECALCUL des métriques avec données nettoyées\n", "print(f\"\\n🔄 RECALCUL DES MÉTRIQUES CLIENT avec données nettoyées :\")\n", "\n", "# Calcul des métriques par client avec données nettoyées\n", "customer_metrics_clean = orders_clean.groupby('customer_id').agg({\n", "    'order_id': 'count',  # <PERSON><PERSON><PERSON>\n", "    'order_purchase_timestamp': ['min', 'max'],  # Première et dernière commande\n", "}).round(2)\n", "\n", "# Aplatissement des colonnes multi-niveaux\n", "customer_metrics_clean.columns = ['frequency', 'first_order_date', 'last_order_date']\n", "customer_metrics_clean = customer_metrics_clean.reset_index()\n", "\n", "# <PERSON><PERSON>l de la récence (jours depuis la dernière commande)\n", "reference_date = orders_clean['order_purchase_timestamp'].max()\n", "customer_metrics_clean['recency'] = (reference_date - customer_metrics_clean['last_order_date']).dt.days\n", "\n", "# Jointure avec les informations client\n", "df = customer_metrics_clean.merge(customers[['customer_id', 'customer_state', 'customer_city']],\n", "                                 on='customer_id', how='left')\n", "\n", "# 3. CALCUL DU MONTANT avec données nettoyées\n", "# Calcul du montant total par commande depuis order_items (uniquement commandes valides)\n", "valid_order_ids = orders_clean['order_id'].unique()\n", "order_items_clean = order_items[order_items['order_id'].isin(valid_order_ids)]\n", "\n", "order_amounts = order_items_clean.groupby('order_id')['price'].sum().reset_index()\n", "order_amounts.columns = ['order_id', 'total_amount']\n", "\n", "# Jointure avec les commandes pour avoir customer_id\n", "orders_with_amounts = orders_clean[['order_id', 'customer_id']].merge(order_amounts, on='order_id')\n", "\n", "# Calcul du montant total par client\n", "customer_monetary = orders_with_amounts.groupby('customer_id')['total_amount'].sum().reset_index()\n", "customer_monetary.columns = ['customer_id', 'monetary']\n", "\n", "# Ajout de la métrique monétaire au dataset principal\n", "df = df.merge(customer_monetary, on='customer_id', how='left')\n", "\n", "# 4. VÉRIFICATIONS DE QUALITÉ\n", "print(f\"\\n🔍 VÉRIFICATIONS DE QUALITÉ DES DONNÉES NETTOYÉES :\")\n", "print(f\"- Nombre de doublons : {df.duplicated().sum()}\")\n", "print(f\"- Nombre de customer_id uniques : {df['customer_id'].nunique()}\")\n", "print(f\"- Nombre total de lignes : {len(df)}\")\n", "\n", "# Vérification de la clé primaire\n", "if df['customer_id'].nunique() == len(df):\n", "    print(\"✓ customer_id est bien une clé primaire\")\n", "else:\n", "    print(\"⚠️ customer_id n'est pas unique\")\n", "\n", "# 5. VÉRIFICATION DU PATTERN \"1 CLIENT = 1 COMMANDE\" après nettoyage\n", "print(f\"\\n🔍 VÉRIFICATION PATTERN après nettoyage :\")\n", "frequency_check = df['frequency'].value_counts().sort_index()\n", "print(f\"Distribution des fréquences après nettoyage :\")\n", "for freq, count in frequency_check.head().items():\n", "    pct = (count / len(df)) * 100\n", "    print(f\"- {freq} commande(s) : {count:,} clients ({pct:.1f}%)\")\n", "\n", "print(f\"\\n💰 Dataset nettoyé avec métriques RFM : {df.shape}\")\n", "print(f\"Colonnes : {list(df.columns)}\")\n", "display(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON> des valeurs manquantes\n", "\n", "### 2.1 Affichage du taux de valeurs manquantes par colonne"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse d<PERSON>e des valeurs manquantes\n", "missing_analysis = describe_missing_data(df)\n", "print(f\"\\n📈 Détail des valeurs manquantes par colonne :\")\n", "display(missing_analysis)\n", "\n", "# Analyse des patterns de valeurs manquantes\n", "if missing_analysis['nb_manquants'].sum() > 0:\n", "    print(f\"\\n🔍 Colonnes avec valeurs manquantes :\")\n", "    cols_with_missing = missing_analysis[missing_analysis['nb_manquants'] > 0]['colonne'].tolist()\n", "    for col in cols_with_missing:\n", "        print(f\"- {col}: {missing_analysis[missing_analysis['colonne']==col]['pct_manquants'].iloc[0]:.2f}%\")\n", "else:\n", "    print(\"✓ Aucune valeur manquante détectée dans le dataset !\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Visualisation heatmap des valeurs manquantes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation heatmap des valeurs manquantes\n", "if missing_analysis['nb_manquants'].sum() > 0:\n", "    fig = plot_missing_heatmap(df, figsize=(12, 8))\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les conventions du projet\n", "    export_figure(fig, notebook_name=\"1\",\n", "                 export_number=1, base_name=\"missing_values_heatmap\")\n", "    plt.show()\n", "else:\n", "    print(\"🎉 Pas de valeurs manquantes à visualiser !\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Choix de la stratégie de traitement"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Traitement des valeurs manquantes\n", "print(f\"🧽 Traitement des valeurs manquantes...\")\n", "\n", "# Copie de travail pour préserver les données originales\n", "df_clean = df.copy()\n", "\n", "# Traitement intelligent des valeurs manquantes\n", "if missing_analysis['nb_manquants'].sum() > 0:\n", "    df_clean = handle_missing_values(df_clean, name=\"dataset_client\", strategy=\"default\")\n", "    print(f\"Shape après nettoyage : {df_clean.shape}\")\n", "\n", "    # Vérification post-traitement\n", "    remaining_missing = df_clean.isnull().sum().sum()\n", "    print(f\"Valeurs manquantes restantes : {remaining_missing}\")\n", "else:\n", "    print(\"✓ Aucun traitement nécessaire - dataset déjà complet\")\n", "\n", "print(f\"\\n📊 Dataset nettoyé final : {df_clean.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON>iva<PERSON>\n", "\n", "### 3.1 Distribution des variables numériques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des distributions des variables numériques\n", "numeric_columns = df_clean.select_dtypes(include=[np.number]).columns.tolist()\n", "print(f\"📊 Variables numériques à analyser : {numeric_columns}\")\n", "\n", "# Création des histogrammes pour les variables RFM\n", "rfm_columns = ['recency', 'frequency', 'monetary']\n", "available_rfm = [col for col in rfm_columns if col in df_clean.columns]\n", "\n", "if available_rfm:\n", "    fig, axes = plt.subplots(1, len(available_rfm), figsize=(15, 5))\n", "    if len(available_rfm) == 1:\n", "        axes = [axes]\n", "\n", "    for i, col in enumerate(available_rfm):\n", "        axes[i].hist(df_clean[col].dropna(), bins=50, alpha=0.7, edgecolor='black', color='skyblue')\n", "        axes[i].set_title(f'Distribution de {col}')\n", "        axes[i].set_xlabel(col)\n", "        axes[i].set_ylabel('Fréquence')\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"1\",\n", "                 export_number=2, base_name=\"rfm_distributions\")\n", "    plt.show()\n", "else:\n", "    print(\"⚠️ Aucune variable RFM trouvée pour la visualisation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Distribution des variables qualitatives"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des variables qualitatives\n", "categorical_columns = df_clean.select_dtypes(include=['object']).columns.tolist()\n", "print(f\"🏷️ Variables qualitatives : {categorical_columns}\")\n", "\n", "if categorical_columns:\n", "    # Analyse de la répartition géographique (states et cities)\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "    # Distribution par état\n", "    if 'customer_state' in df_clean.columns:\n", "        state_counts = df_clean['customer_state'].value_counts().head(10)\n", "        state_counts.plot(kind='bar', ax=axes[0], color='lightcoral')\n", "        axes[0].set_title('Top 10 des États par nombre de clients')\n", "        axes[0].set_xlabel('État')\n", "        axes[0].set_ylabel('Nombre de clients')\n", "        axes[0].tick_params(axis='x', rotation=45)\n", "\n", "    # Distribution par ville (top 10)\n", "    if 'customer_city' in df_clean.columns:\n", "        city_counts = df_clean['customer_city'].value_counts().head(10)\n", "        city_counts.plot(kind='bar', ax=axes[1], color='lightgreen')\n", "        axes[1].set_title('Top 10 des Villes par nombre de clients')\n", "        axes[1].set_xlabel('Ville')\n", "        axes[1].set_ylabel('Nombre de clients')\n", "        axes[1].tick_params(axis='x', rotation=45)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"1\",\n", "                 export_number=3, base_name=\"geographic_distribution\")\n", "    plt.show()\n", "\n", "    # Statistiques détaillées\n", "    print(f\"\\n📊 Statistiques géographiques :\")\n", "    if 'customer_state' in df_clean.columns:\n", "        print(f\"- Nombre d'états uniques : {df_clean['customer_state'].nunique()}\")\n", "    if 'customer_city' in df_clean.columns:\n", "        print(f\"- Nombre de villes uniques : {df_clean['customer_city'].nunique()}\")\n", "else:\n", "    print(\"ℹ️ Aucune variable qualitative à analyser\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Statistiques descriptives détaillées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Statistiques descriptives détaillées\n", "print(f\"📊 Statistiques descriptives du dataset :\")\n", "desc_stats = df_clean.describe()\n", "display(desc_stats)\n", "\n", "# Calcul des mesures de forme (asymétrie et aplatissement)\n", "numeric_df = df_clean.select_dtypes(include=[np.number])\n", "if not numeric_df.empty:\n", "    skewness = numeric_df.skew()\n", "    kurtosis = numeric_df.kurtosis()\n", "\n", "    shape_stats = pd.DataFrame({\n", "        'Variable': skewness.index,\n", "        'Skewness': skewness.values,\n", "        'Kurtosis': kurtosis.values\n", "    })\n", "\n", "    print(f\"\\n📈 Mesures de forme (asymétrie et aplatissement) :\")\n", "    display(shape_stats)\n", "\n", "    # Interprétation des mesures\n", "    print(f\"\\n🔍 Interprétation :\")\n", "    for idx, row in shape_stats.iterrows():\n", "        var_name = row['Variable']\n", "        skew_val = row['Skewness']\n", "        kurt_val = row['Kurtosis']\n", "\n", "        # Interprétation de l'asymétrie\n", "        if abs(skew_val) < 0.5:\n", "            skew_interp = \"symétrique\"\n", "        elif skew_val > 0.5:\n", "            skew_interp = \"asymétrie positive (queue à droite)\"\n", "        else:\n", "            skew_interp = \"asymétrie négative (queue à gauche)\"\n", "\n", "        print(f\"- {var_name}: {skew_interp}\")\n", "else:\n", "    print(\"⚠️ Aucune variable numérique pour les statistiques descriptives\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON>\n", "\n", "### 4.1 Corrélations entre variables quantitatives"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Matrice de corrélation des variables numériques\n", "numeric_df = df_clean.select_dtypes(include=[np.number])\n", "\n", "if not numeric_df.empty and numeric_df.shape[1] > 1:\n", "    print(f\"🔗 Analyse des corrélations entre variables numériques\")\n", "\n", "    # Utilisation de la fonction optimisée du module data_tools\n", "    fig, correlation_matrix = plot_correlation_matrix(\n", "        numeric_df,\n", "        figsize=(10, 8),\n", "        annot=True,\n", "        mask_upper=True,\n", "        cmap='coolwarm'\n", "    )\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"1\",\n", "                 export_number=4, base_name=\"correlation_matrix\")\n", "    plt.show()\n", "\n", "    # Analyse des corrélations fortes\n", "    print(f\"\\n🔍 Corrélations significatives (|r| > 0.5) :\")\n", "    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))\n", "    correlation_matrix_masked = correlation_matrix.mask(mask)\n", "\n", "    strong_corr = []\n", "    for i in range(len(correlation_matrix_masked.columns)):\n", "        for j in range(len(correlation_matrix_masked.columns)):\n", "            if not pd.isna(correlation_matrix_masked.iloc[i, j]):\n", "                corr_val = correlation_matrix_masked.iloc[i, j]\n", "                if abs(corr_val) > 0.5:\n", "                    var1 = correlation_matrix_masked.columns[i]\n", "                    var2 = correlation_matrix_masked.columns[j]\n", "                    strong_corr.append((var1, var2, corr_val))\n", "\n", "    if strong_corr:\n", "        for var1, var2, corr in strong_corr:\n", "            print(f\"- {var1} ↔ {var2}: {corr:.3f}\")\n", "    else:\n", "        print(\"Aucune corrélation forte détectée\")\n", "\n", "else:\n", "    print(\"⚠️ Pas assez de variables numériques pour calculer les corrélations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Relations entre variables continues"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des relations entre variables continues (scatterplots)\n", "numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()\n", "rfm_cols = [col for col in ['recency', 'frequency', 'monetary'] if col in numeric_cols]\n", "\n", "if len(rfm_cols) >= 2:\n", "    print(f\"🔗 Analyse des relations entre variables RFM\")\n", "\n", "    # Création des scatterplots pour les paires de variables RFM\n", "    fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "    # Frequency vs Monetary\n", "    if 'frequency' in rfm_cols and 'monetary' in rfm_cols:\n", "        axes[0].scatter(df_clean['frequency'], df_clean['monetary'], alpha=0.6, color='blue')\n", "        axes[0].set_xlabel('Fréquence d\\'achat')\n", "        axes[0].set_ylabel('Montant total (€)')\n", "        axes[0].set_title('Relation Fréquence vs Mont<PERSON>')\n", "        axes[0].grid(True, alpha=0.3)\n", "\n", "    # Recency vs Monetary\n", "    if 'recency' in rfm_cols and 'monetary' in rfm_cols:\n", "        axes[1].scatter(df_clean['recency'], df_clean['monetary'], alpha=0.6, color='green')\n", "        axes[1].set_xlabel('Ré<PERSON> (jours)')\n", "        axes[1].set_ylabel('Montant total (€)')\n", "        axes[1].set_title('Relation Récence vs Mont<PERSON>')\n", "        axes[1].grid(True, alpha=0.3)\n", "\n", "    # Recency vs Frequency\n", "    if 'recency' in rfm_cols and 'frequency' in rfm_cols:\n", "        axes[2].scatter(df_clean['recency'], df_clean['frequency'], alpha=0.6, color='red')\n", "        axes[2].set_xlabel('R<PERSON><PERSON> (jours)')\n", "        axes[2].set_ylabel('<PERSON>é<PERSON> d\\'achat')\n", "        axes[2].set_title('Relation Récence vs Fréquence')\n", "        axes[2].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export avec la bonne convention de nommage\n", "    export_figure(fig, notebook_name=\"1\",\n", "                 export_number=7, base_name=\"rfm_scatterplots\")\n", "    plt.show()\n", "\n", "    # Calcul des corrélations spécifiques\n", "    print(f\"\\n📈 Corrélations entre variables RFM :\")\n", "    for i, var1 in enumerate(rfm_cols):\n", "        for var2 in rfm_cols[i+1:]:\n", "            corr = df_clean[var1].corr(df_clean[var2])\n", "            print(f\"- {var1} ↔ {var2}: {corr:.3f}\")\n", "else:\n", "    print(\"⚠️ Pas assez de variables numériques pour l'analyse des relations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Relations entre variables quantitatives et qualitatives"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des relations entre variables quantitatives et qualitatives\n", "categorical_cols = df_clean.select_dtypes(include=['object']).columns.tolist()\n", "numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()\n", "\n", "if categorical_cols and numeric_cols:\n", "    print(f\"📈 Analyse des relations quantitatif vs qualitatif\")\n", "\n", "    # Focus sur les variables géographiques vs métriques RFM\n", "    if 'customer_state' in categorical_cols:\n", "        # Sélection des top 10 états pour la lisibilité\n", "        top_states = df_clean['customer_state'].value_counts().head(10).index\n", "        df_top_states = df_clean[df_clean['customer_state'].isin(top_states)]\n", "\n", "        # Création des boxplots pour les variables RFM par état\n", "        rfm_vars = [col for col in ['monetary', 'frequency', 'recency'] if col in numeric_cols]\n", "\n", "        if rfm_vars:\n", "            fig, axes = plt.subplots(1, len(rfm_vars), figsize=(6*len(rfm_vars), 6))\n", "            if len(rfm_vars) == 1:\n", "                axes = [axes]\n", "\n", "            for i, var in enumerate(rfm_vars):\n", "                sns.boxplot(data=df_top_states, x='customer_state', y=var, ax=axes[i])\n", "                axes[i].set_title(f'Distribution de {var} par état')\n", "                axes[i].tick_params(axis='x', rotation=45)\n", "                axes[i].grid(True, alpha=0.3)\n", "\n", "            plt.tight_layout()\n", "\n", "            # Export avec la bonne convention\n", "            export_figure(fig, notebook_name=\"1\",\n", "                         export_number=8, base_name=\"rfm_by_state_boxplots\")\n", "            plt.show()\n", "\n", "            # Statistiques par état\n", "            print(f\"\\n📊 Statistiques des variables RFM par état (top 5) :\")\n", "            for var in rfm_vars[:2]:  # Limiter l'affichage\n", "                print(f\"\\n--- {var.upper()} par état ---\")\n", "                state_stats = df_top_states.groupby('customer_state')[var].agg(['mean', 'median', 'std']).round(2)\n", "                display(state_stats.head())\n", "\n", "    # Analyse complémentaire : distribution géographique des segments\n", "    if 'customer_state' in categorical_cols and 'monetary' in numeric_cols:\n", "        print(f\"\\n🌍 Analyse géographique des profils clients :\")\n", "\n", "        # Création de catégories de valeur client\n", "        df_clean['value_segment'] = pd.cut(df_clean['monetary'],\n", "                                          bins=3,\n", "                                          labels=['<PERSON>ai<PERSON>', 'Moyen', 'Elevé'])\n", "\n", "        # Tableau croisé état vs segment de valeur\n", "        crosstab = pd.crosstab(df_clean['customer_state'],\n", "                              df_clean['value_segment'],\n", "                              normalize='index') * 100\n", "\n", "        print(\"Répartition des segments de valeur par état (%) :\")\n", "        display(crosstab.head(10).round(1))\n", "\n", "else:\n", "    print(\"⚠️ Pas de variables qualitatives ou quantitatives pour l'analyse croisée\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "### 5.1 Pairplot des variables clés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pairplot ciblé sur variables candidates pour segmentation\n", "print(f\"🎯 Analyse des relations multivariées - Pairplot RFM\")\n", "\n", "# Sélection des variables les plus importantes pour la segmentation\n", "key_variables = ['recency', 'frequency', 'monetary']  # Variables RFM\n", "available_key_vars = [var for var in key_variables if var in df_clean.columns]\n", "\n", "if len(available_key_vars) >= 2:\n", "    print(f\"Variables RFM disponibles pour le pairplot : {available_key_vars}\")\n", "\n", "    # Création du pairplot avec échantillonnage si dataset trop volumineux\n", "    sample_size = min(1000, len(df_clean))  # Limiter à 1000 points pour la performance\n", "    df_sample = df_clean[available_key_vars].sample(n=sample_size, random_state=42)\n", "\n", "    # Création du pairplot\n", "    fig = plt.figure(figsize=(12, 10))\n", "    pairplot = sns.pairplot(df_sample, diag_kind='kde', plot_kws={'alpha': 0.6})\n", "    pairplot.fig.suptitle('Relations entre variables RFM', y=1.02, fontsize=16)\n", "\n", "    # Amélioration de l'affichage\n", "    for ax in pairplot.axes.flatten():\n", "        if ax:\n", "            ax.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure\n", "    export_figure(pairplot.fig, notebook_name=\"1\",\n", "                 export_number=10, base_name=\"rfm_pairplot\")\n", "    plt.show()\n", "\n", "    print(f\"\\n📊 Insights du pairplot :\")\n", "    print(f\"- Échantillon analysé : {sample_size} clients\")\n", "    print(f\"- Variables analysées : {available_key_vars}\")\n", "    print(f\"- Le pairplot révèle les relations non-linéaires entre les dimensions RFM\")\n", "\n", "else:\n", "    print(f\"⚠️ Pas assez de variables RFM disponibles pour le pairplot\")\n", "    print(f\"Variables trouvées : {available_key_vars}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Préparation pour l'Analyse en Composantes Principales (optionnel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse en Composantes Principales (ACP) pour la réduction de dimensionnalité\n", "from sklearn.decomposition import PCA\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "print(f\"🔬 Analyse en Composantes Principales (ACP)\")\n", "\n", "# Sélection des variables numériques pour l'ACP\n", "numeric_cols_for_pca = df_clean.select_dtypes(include=[np.number]).columns.tolist()\n", "# Exclure les variables binaires (outlier flags) et les variables temporelles discrètes\n", "excluded_cols = [col for col in numeric_cols_for_pca if\n", "                col.endswith('_outlier') or 'year' in col or 'month' in col or\n", "                'weekday' in col or 'quarter' in col]\n", "numeric_cols_for_pca = [col for col in numeric_cols_for_pca if col not in excluded_cols]\n", "\n", "print(f\"Variables sélectionnées pour l'ACP : {numeric_cols_for_pca}\")\n", "\n", "if len(numeric_cols_for_pca) >= 2:\n", "    # Préparation des données (suppression des valeurs manquantes)\n", "    df_pca = df_clean[numeric_cols_for_pca].dropna()\n", "\n", "    if len(df_pca) > 0:\n", "        # Standardisation des données pour ACP\n", "        scaler = StandardScaler()\n", "        X_scaled = scaler.fit_transform(df_pca)\n", "\n", "        # Application de l'ACP\n", "        pca = PCA()\n", "        X_pca = pca.fit_transform(X_scaled)\n", "\n", "        # Analyse de la variance expliquée\n", "        cumsum_variance = np.cumsum(pca.explained_variance_ratio_)\n", "        n_components_95 = np.argmax(cumsum_variance >= 0.95) + 1\n", "\n", "        print(f\"\\n📊 Résultats de l'ACP :\")\n", "        print(f\"- Nombre total de composantes : {len(pca.explained_variance_ratio_)}\")\n", "        print(f\"- Composantes pour 95% de variance : {n_components_95}\")\n", "        if len(cumsum_variance) > 2:\n", "            print(f\"- V<PERSON>ce expliquée par les 3 premières composantes : {cumsum_variance[2]:.3f}\")\n", "\n", "        # Visualisation de la variance expliquée\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "        # Graphique en barres de la variance par composante\n", "        ax1.bar(range(1, len(pca.explained_variance_ratio_) + 1),\n", "               pca.explained_variance_ratio_, alpha=0.7, color='skyblue')\n", "        ax1.set_xlabel('Composante Principale')\n", "        ax1.set_ylabel('Variance Expliquée')\n", "        ax1.set_title('Variance expliquée par composante')\n", "        ax1.grid(True, alpha=0.3)\n", "\n", "        # Graphique de la variance cumulée\n", "        ax2.plot(range(1, len(cumsum_variance) + 1), cumsum_variance,\n", "                'bo-', alpha=0.7, color='red')\n", "        ax2.axhline(y=0.95, color='green', linestyle='--',\n", "                   label='95% de variance')\n", "        ax2.axvline(x=n_components_95, color='green', linestyle='--')\n", "        ax2.set_xlabel('Nombre de Composantes')\n", "        ax2.set_ylabel('<PERSON><PERSON><PERSON>')\n", "        ax2.set_title('Variance cumulée expliquée')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "\n", "        plt.tight_layout()\n", "\n", "        # Export de la figure\n", "        export_figure(fig, notebook_name=\"1\",\n", "                     export_number=11, base_name=\"pca_variance_analysis\")\n", "        plt.show()\n", "\n", "    else:\n", "        print(\"⚠️ Pas assez de données valides après suppression des valeurs manquantes\")\n", "else:\n", "    print(\"⚠️ Pas assez de variables numériques pour l'ACP\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Détection et traitement des outliers\n", "\n", "### 6.1 Détection par Z-score et IQR"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔍 DÉTECTION DES OUTLIERS adaptée au contexte \"First Purchase\"\n", "print(\"🔍 DÉTECTION DES OUTLIERS - Contexte First Purchase\")\n", "print(\"=\" * 50)\n", "\n", "# Focus sur les variables critiques pour la segmentation\n", "critical_vars = ['recency', 'monetary']\n", "available_critical = [var for var in critical_vars if var in df_clean.columns]\n", "\n", "print(f\"\\n📊 ANALYSE DES OUTLIERS sur variables critiques : {available_critical}\")\n", "\n", "outliers_summary = []\n", "numeric_cols = df_clean.select_dtypes(include=[np.number]).columns\n", "\n", "for col in numeric_cols:\n", "    if df_clean[col].notna().sum() > 0:  # Vérifier qu'il y a des données non nulles\n", "        print(f\"\\n--- Analyse des outliers pour '{col}' ---\")\n", "\n", "        # Statistiques de base\n", "        col_stats = df_clean[col].describe()\n", "        print(f\"Min: {col_stats['min']:.2f}, Max: {col_stats['max']:.2f}, Médiane: {col_stats['50%']:.2f}\")\n", "\n", "        # Méthode IQR\n", "        try:\n", "            outliers_iqr, bounds = detect_outliers_iqr(df_clean, col)\n", "            iqr_count = len(outliers_iqr)\n", "            iqr_pct = (iqr_count / len(df_clean)) * 100\n", "            print(f\"IQR: {iqr_count} outliers ({iqr_pct:.1f}%) - Bornes: [{bounds[0]:.2f}, {bounds[1]:.2f}]\")\n", "        except Exception as e:\n", "            print(f\"Erreur IQR pour {col}: {e}\")\n", "            iqr_count, iqr_pct = 0, 0\n", "            bounds = [0, 0]\n", "\n", "        # <PERSON><PERSON><PERSON><PERSON> Z-score\n", "        try:\n", "            outliers_zscore = detect_outliers_zscore(df_clean, col, threshold=3.0)\n", "            zscore_count = len(outliers_zscore)\n", "            zscore_pct = (zscore_count / len(df_clean)) * 100\n", "            print(f\"Z-score: {zscore_count} outliers ({zscore_pct:.1f}%)\")\n", "        except Exception as e:\n", "            print(f\"Erreur Z-score pour {col}: {e}\")\n", "            zscore_count, zscore_pct = 0, 0\n", "\n", "        # Analyse contextuelle pour variables critiques\n", "        if col in available_critical:\n", "            if col == 'monetary':\n", "                print(f\"💰 CONTEXTE BUSINESS (Montant) :\")\n", "                print(f\"   - Outliers élevés = Clients VIP potentiels\")\n", "                print(f\"   - Outliers faibles = Clients sensibles au prix\")\n", "                print(f\"   - DÉCISION : Conserver pour segmentation\")\n", "            elif col == 'recency':\n", "                print(f\"📅 CONTEXTE BUSINESS (Récence) :\")\n", "                print(f\"   - Outliers élevés = Clients dormants à réactiver\")\n", "                print(f\"   - Outliers faibles = Clients très récents\")\n", "                print(f\"   - DÉCISION : Conserver pour segmentation\")\n", "\n", "        # Stockage des résultats\n", "        outliers_summary.append({\n", "            'Variable': col,\n", "            'IQR_outliers': iqr_count,\n", "            'IQR_pct': iqr_pct,\n", "            'Zscore_outliers': zscore_count,\n", "            'Zscore_pct': zscore_pct,\n", "            'Business_Impact': 'Critique' if col in available_critical else 'Modéré'\n", "        })\n", "\n", "# Résumé des outliers\n", "if outliers_summary:\n", "    outliers_df = pd.DataFrame(outliers_summary)\n", "    print(f\"\\n📈 RÉSUMÉ DE LA DÉTECTION D'OUTLIERS :\")\n", "    display(outliers_df)\n", "\n", "    # Recommandations spécifiques\n", "    print(f\"\\n💡 RECOMMANDATIONS POUR LA SEGMENTATION :\")\n", "    print(f\"- Variables critiques (recency, monetary) : CONSERVER tous les outliers\")\n", "    print(f\"- Justification : Outliers = segments de haute valeur ou comportements spéciaux\")\n", "    print(f\"- Stratégie : Utiliser les outliers pour identifier des micro-segments\")\n", "else:\n", "    print(\"⚠️ Aucune variable numérique valide pour la détection d'outliers\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Visualisation des outliers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 VISUALISATIONS ADAPTÉES AU CONTEXTE \"FIRST PURCHASE\"\n", "print(\"📊 VISUALISATIONS SPÉCIALISÉES - First Purchase\")\n", "print(\"=\" * 50)\n", "\n", "# Sélection des variables critiques pour la segmentation\n", "critical_vars = ['recency', 'monetary']\n", "available_vars = [var for var in critical_vars if var in df_clean.columns]\n", "numeric_cols = df_clean.select_dtypes(include=[np.number]).columns\n", "valid_numeric_cols = [col for col in numeric_cols if df_clean[col].notna().sum() > 0]\n", "\n", "print(f\"Variables critiques disponibles : {available_vars}\")\n", "print(f\"Variables numériques valides : {len(valid_numeric_cols)}\")\n", "\n", "if len(available_vars) >= 2:  # Au moins recency et monetary\n", "    # 1. GRAPHIQUE SPÉCIALISÉ : <PERSON><PERSON><PERSON> vs <PERSON><PERSON> (cœur de la segmentation)\n", "    fig_main = plt.figure(figsize=(15, 10))\n", "\n", "    # Scatter plot principal\n", "    plt.subplot(2, 2, 1)\n", "    plt.scatter(df_clean['recency'], df_clean['monetary'], alpha=0.6, s=30)\n", "    plt.xlabel('R<PERSON><PERSON> (jours depuis dernier achat)')\n", "    plt.ylabel('<PERSON><PERSON> (€)')\n", "    plt.title('🎯 SEGMENTATION FIRST PURCHASE\\nRécence vs Mont<PERSON>')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "    # Ajout de lignes de segmentation indicatives\n", "    recency_median = df_clean['recency'].median()\n", "    monetary_median = df_clean['monetary'].median()\n", "    plt.axvline(recency_median, color='red', linestyle='--', alpha=0.7, label=f'Récence médiane ({recency_median:.0f}j)')\n", "    plt.axhline(monetary_median, color='red', linestyle='--', alpha=0.7, label=f'Montant médian ({monetary_median:.0f}€)')\n", "    plt.legend()\n", "\n", "    # Distribution de la récence\n", "    plt.subplot(2, 2, 2)\n", "    df_clean['recency'].hist(bins=50, alpha=0.7, color='skyblue')\n", "    plt.axvline(recency_median, color='red', linestyle='--', alpha=0.7)\n", "    plt.xlabel('R<PERSON><PERSON> (jours)')\n", "    plt.ylabel('Nombre de clients')\n", "    plt.title('📅 Distribution de la Récence')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "    # Distribution du montant\n", "    plt.subplot(2, 2, 3)\n", "    df_clean['monetary'].hist(bins=50, alpha=0.7, color='lightgreen')\n", "    plt.axvline(monetary_median, color='red', linestyle='--', alpha=0.7)\n", "    plt.xlabel('<PERSON><PERSON> (€)')\n", "    plt.ylabel('Nombre de clients')\n", "    plt.title('💰 Distribution du Montant')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "    # Boxplots combinés\n", "    plt.subplot(2, 2, 4)\n", "    box_data = [df_clean['recency'].dropna(), df_clean['monetary'].dropna()]\n", "    bp = plt.boxplot(box_data)\n", "    plt.xticks([1, 2], ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'])\n", "    plt.title('📊 Détection Outliers - Variables Critiques')\n", "    plt.ylabel('Valeurs')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    export_figure(fig_main, \"1\", 4, \"first_purchase_analysis\")\n", "    plt.show()\n", "\n", "# 2. VISUALISATIONS COMPLÉMENTAIRES pour autres variables\n", "if len(valid_numeric_cols) > 2:\n", "    other_vars = [col for col in valid_numeric_cols if col not in available_vars]\n", "\n", "    if other_vars:\n", "        n_cols = min(3, len(other_vars))\n", "        n_rows = (len(other_vars) + n_cols - 1) // n_cols\n", "\n", "        # Boxplots pour variables secondaires\n", "        fig_box, axes_box = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))\n", "        if n_rows == 1 and n_cols == 1:\n", "            axes_box = [axes_box]\n", "        elif n_rows == 1:\n", "            axes_box = list(axes_box)\n", "        else:\n", "            axes_box = axes_box.flatten()\n", "\n", "        for i, col in enumerate(other_vars):\n", "            if i < len(axes_box):\n", "                df_clean.boxplot(column=col, ax=axes_box[i])\n", "                axes_box[i].set_title(f'Outliers - {col}')\n", "                axes_box[i].grid(True, alpha=0.3)\n", "\n", "        # Masquer axes vides\n", "        for i in range(len(other_vars), len(axes_box)):\n", "            axes_box[i].set_visible(False)\n", "\n", "        fig_box.suptitle('Variables Secondaires - Détection Outliers', fontsize=16)\n", "        fig_box.tight_layout()\n", "        export_figure(fig_box, \"1\", 5, \"secondary_variables_outliers\")\n", "        plt.show()\n", "\n", "# 3. ANALYSE GÉOGRAPHIQUE si disponible\n", "if 'customer_state' in df_clean.columns:\n", "    fig_geo = plt.figure(figsize=(15, 8))\n", "\n", "    # Distribution par état\n", "    plt.subplot(1, 2, 1)\n", "    state_counts = df_clean['customer_state'].value_counts().head(10)\n", "    state_counts.plot(kind='bar')\n", "    plt.title('🗺️ Top 10 États - Nombre de Clients')\n", "    plt.xlabel('État')\n", "    plt.ylabel('Nombre de clients')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, alpha=0.3)\n", "\n", "    # <PERSON><PERSON> moyen par état\n", "    if 'monetary' in df_clean.columns:\n", "        plt.subplot(1, 2, 2)\n", "        state_monetary = df_clean.groupby('customer_state')['monetary'].mean().sort_values(ascending=False).head(10)\n", "        state_monetary.plot(kind='bar', color='lightcoral')\n", "        plt.title('💰 Top 10 États - <PERSON><PERSON>')\n", "        plt.xlabel('État')\n", "        plt.ylabel('<PERSON><PERSON> moyen (€)')\n", "        plt.xticks(rotation=45)\n", "        plt.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    export_figure(fig_geo, \"1\", 6, \"geographic_analysis\")\n", "    plt.show()\n", "\n", "print(f\"\\n✅ VISUALISATIONS SPÉCIALISÉES créées et exportées\")\n", "print(f\"📁 Fichiers exportés : 1_04_first_purchase_analysis.png, 1_05_secondary_variables_outliers.png, 1_06_geographic_analysis.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.3 Suppression ou traitement des outliers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Traitement des outliers selon pertinence métier\n", "print(f\"🔧 Décision de traitement des outliers\")\n", "\n", "# Analyse contextuelle des outliers pour la segmentation client\n", "print(f\"\\n📋 Analyse contextuelle des outliers :\")\n", "print(f\"- Les outliers en e-commerce peuvent représenter des clients VIP ou des comportements exceptionnels\")\n", "print(f\"- Pour la segmentation client, ces profils atypiques sont souvent précieux\")\n", "print(f\"- Décision : Conservation des outliers pour préserver la diversité des segments\")\n", "\n", "# Création d'une copie pour le traitement (ici, on garde tous les outliers)\n", "df_final = df_clean.copy()\n", "\n", "# Optionnel : Marquage des outliers pour analyse future\n", "numeric_cols = df_clean.select_dtypes(include=[np.number]).columns\n", "outlier_flags = pd.DataFrame(index=df_clean.index)\n", "\n", "for col in numeric_cols:\n", "    if df_clean[col].notna().sum() > 0:\n", "        # Marquage des outliers IQR\n", "        Q1 = df_clean[col].quantile(0.25)\n", "        Q3 = df_clean[col].quantile(0.75)\n", "        IQR = Q3 - Q1\n", "        lower_bound = Q1 - 1.5 * IQR\n", "        upper_bound = Q3 + 1.5 * IQR\n", "\n", "        outlier_flags[f'{col}_outlier'] = (\n", "            (df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)\n", "        )\n", "\n", "# Ajout des flags d'outliers au dataset final\n", "df_final = pd.concat([df_final, outlier_flags], axis=1)\n", "\n", "# Statistiques des outliers marqués\n", "outlier_cols = [col for col in df_final.columns if col.endswith('_outlier')]\n", "if outlier_cols:\n", "    print(f\"\\n🏷️ Marquage des outliers effectué :\")\n", "    for col in outlier_cols:\n", "        outlier_count = df_final[col].sum()\n", "        outlier_pct = (outlier_count / len(df_final)) * 100\n", "        print(f\"- {col}: {outlier_count} outliers ({outlier_pct:.1f}%)\")\n", "\n", "print(f\"\\n✅ Dataset final conservé avec outliers : {df_final.shape}\")\n", "print(f\"Justification : Les outliers en segmentation client représentent souvent des segments de haute valeur\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Feature Engineering de base\n", "\n", "### 7.1 Calcul de l'ancienneté client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul de l'ancienneté client et métriques temporelles\n", "print(f\"🕰️ Calcul des métriques temporelles\")\n", "\n", "# Calcul de l'ancienneté client (jours depuis la première commande)\n", "if 'first_order_date' in df_final.columns:\n", "    reference_date = df_final['last_order_date'].max()\n", "    df_final['customer_age_days'] = (reference_date - df_final['first_order_date']).dt.days\n", "\n", "    print(f\"Ancienneté client calculée :\")\n", "    print(f\"- Ancienneté moyenne : {df_final['customer_age_days'].mean():.0f} jours\")\n", "    print(f\"- Ancienneté médiane : {df_final['customer_age_days'].median():.0f} jours\")\n", "    print(f\"- Ancienne<PERSON> min : {df_final['customer_age_days'].min():.0f} jours\")\n", "    print(f\"- Ancienneté max : {df_final['customer_age_days'].max():.0f} jours\")\n", "\n", "    # Création de catégories d'ancienneté\n", "    df_final['customer_age_category'] = pd.cut(\n", "        df_final['customer_age_days'],\n", "        bins=[0, 30, 90, 180, 365, float('inf')],\n", "        labels=['Nouveau (<30j)', '<PERSON><PERSON><PERSON> (30-90j)', '<PERSON><PERSON><PERSON><PERSON> (90-180j)',\n", "               'Ancien (180-365j)', 'Très ancien (>365j)']\n", "    )\n", "\n", "    # Distribution des catégories d'ancienneté\n", "    age_distribution = df_final['customer_age_category'].value_counts()\n", "    print(f\"\\n📈 Distribution des catégories d'ancienneté :\")\n", "    for category, count in age_distribution.items():\n", "        pct = (count / len(df_final)) * 100\n", "        print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "else:\n", "    print(\"⚠️ Colonnes de dates manquantes pour le calcul d'ancienneté\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7.2 Métriques d'achat et d'activité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul des métriques d'achat et d'activité\n", "print(f\"🛒 Calcul des métriques d'achat et d'activité\")\n", "\n", "# <PERSON><PERSON>l du panier moyen\n", "if 'monetary' in df_final.columns and 'frequency' in df_final.columns:\n", "    df_final['average_basket'] = df_final['monetary'] / df_final['frequency']\n", "\n", "    print(f\"Métriques d'achat calculées :\")\n", "    print(f\"- <PERSON><PERSON> moyen : {df_final['average_basket'].mean():.2f} €\")\n", "    print(f\"- <PERSON><PERSON> médian : {df_final['average_basket'].median():.2f} €\")\n", "    print(f\"- <PERSON><PERSON><PERSON> moy<PERSON> : {df_final['frequency'].mean():.2f} commandes\")\n", "    print(f\"- Montant moyen par client : {df_final['monetary'].mean():.2f} €\")\n", "\n", "    # Calcul de métriques d'activité supplémentaires\n", "    if 'customer_age_days' in df_final.columns:\n", "        # Fréquence d'achat normalisée (commandes par mois)\n", "        df_final['frequency_per_month'] = (df_final['frequency'] / (df_final['customer_age_days'] / 30.44)).round(2)\n", "\n", "        # Montant par mois d'activité\n", "        df_final['monetary_per_month'] = (df_final['monetary'] / (df_final['customer_age_days'] / 30.44)).round(2)\n", "\n", "        print(f\"\\n📊 Métriques d'activité normalisées :\")\n", "        print(f\"- Fréquence par mois : {df_final['frequency_per_month'].mean():.2f} commandes/mois\")\n", "        print(f\"- Montant par mois : {df_final['monetary_per_month'].mean():.2f} €/mois\")\n", "\n", "    # Création de segments de valeur de panier\n", "    df_final['basket_segment'] = pd.cut(\n", "        df_final['average_basket'],\n", "        bins=3,\n", "        labels=['Petit panier', 'Panier moyen', 'Gros panier']\n", "    )\n", "\n", "    # Distribution des segments de panier\n", "    basket_distribution = df_final['basket_segment'].value_counts()\n", "    print(f\"\\n🛍️ Distribution des segments de panier :\")\n", "    for segment, count in basket_distribution.items():\n", "        pct = (count / len(df_final)) * 100\n", "        print(f\"- {segment}: {count} clients ({pct:.1f}%)\")\n", "\n", "else:\n", "    print(\"⚠️ Variables monetary ou frequency manquantes pour le calcul des métriques\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7.3 Conversion et traitement des dates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Conversion et traitement des dates - Extraction de features temporelles\n", "print(f\"📅 Extraction de features temporelles\")\n", "\n", "# Identification des colonnes de dates\n", "date_columns = df_final.select_dtypes(include=['datetime64']).columns\n", "print(f\"Colonnes de dates trouvées : {list(date_columns)}\")\n", "\n", "# Extraction de features temporelles pour les dates de commande\n", "if 'first_order_date' in df_final.columns:\n", "    # Extraction des composantes temporelles de la première commande\n", "    df_final['first_order_year'] = df_final['first_order_date'].dt.year\n", "    df_final['first_order_month'] = df_final['first_order_date'].dt.month\n", "    df_final['first_order_weekday'] = df_final['first_order_date'].dt.weekday\n", "    df_final['first_order_quarter'] = df_final['first_order_date'].dt.quarter\n", "\n", "    print(f\"Features temporelles extraites pour first_order_date\")\n", "\n", "    # Analyse de la saisonnalité des premières commandes\n", "    print(f\"\\n📊 Analyse de saisonnalité des premières commandes :\")\n", "\n", "    # Distribution par mois\n", "    month_dist = df_final['first_order_month'].value_counts().sort_index()\n", "    print(f\"\\nDistribution par mois :\")\n", "    month_names = ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>v<PERSON>', '<PERSON>', '<PERSON>',\n", "                   'Jul', '<PERSON><PERSON><PERSON>', 'Sep', 'Oct', 'Nov', '<PERSON>é<PERSON>']\n", "    for month, count in month_dist.items():\n", "        pct = (count / len(df_final)) * 100\n", "        print(f\"- {month_names[int(month)-1]}: {count} clients ({pct:.1f}%)\")\n", "\n", "    # Distribution par jour de la semaine\n", "    weekday_dist = df_final['first_order_weekday'].value_counts().sort_index()\n", "    print(f\"\\nDistribution par jour de la semaine :\")\n", "    weekday_names = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>']\n", "    for weekday, count in weekday_dist.items():\n", "        pct = (count / len(df_final)) * 100\n", "        print(f\"- {weekday_names[weekday]}: {count} clients ({pct:.1f}%)\")\n", "\n", "    # Distribution par trimestre\n", "    quarter_dist = df_final['first_order_quarter'].value_counts().sort_index()\n", "    print(f\"\\nDistribution par trimestre :\")\n", "    for quarter, count in quarter_dist.items():\n", "        pct = (count / len(df_final)) * 100\n", "        print(f\"- Q{quarter}: {count} clients ({pct:.1f}%)\")\n", "\n", "else:\n", "    print(\"⚠️ Aucune colonne de date disponible pour l'extraction de features\")\n", "\n", "# Résumé des nouvelles variables créées\n", "print(f\"\\n✅ Résumé des variables créées dans cette section :\")\n", "new_vars = [col for col in df_final.columns if any(x in col for x in\n", "           ['age_', 'basket', 'per_month', 'order_year', 'order_month', 'order_weekday', 'order_quarter'])]\n", "print(f\"Variables ajoutées : {new_vars}\")\n", "print(f\"Dataset final : {df_final.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Sauvegarde du dataset nettoyé\n", "\n", "### 8.1 Export du DataFrame nettoyé"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 💾 SAUVEGARDE ADAPTÉE À LA STRATÉGIE \"FIRST PURCHASE\"\n", "print(\"💾 SAUVEGARDE DATASET NETTOYÉ - Stratégie First Purchase\")\n", "print(\"=\" * 50)\n", "\n", "from utils.save_load import save_results\n", "\n", "# Utilisation de df_clean comme dataset final\n", "df_final = df_clean.copy()\n", "\n", "# 1. EXPORT PRINCIPAL avec convention de nommage du projet\n", "print(f\"\\n📊 DATASET FINAL ADAPTÉ :\")\n", "print(f\"- Shape : {df_final.shape}\")\n", "print(f\"- Variables critiques : {[col for col in ['recency', 'monetary', 'frequency'] if col in df_final.columns]}\")\n", "print(f\"- Variables géographiques : {[col for col in ['customer_state', 'customer_city'] if col in df_final.columns]}\")\n", "\n", "# Export CSV avec convention de nommage du projet\n", "csv_path = 'data/processed/1_01_cleaned_dataset.csv'\n", "df_final.to_csv(csv_path, index=False)\n", "print(f\"✅ Dataset CSV exporté : {csv_path}\")\n", "\n", "# Export pickle pour préserver les types\n", "pickle_path = 'data/processed/1_01_cleaned_dataset.pkl'\n", "df_final.to_pickle(pickle_path)\n", "print(f\"✅ Dataset pickle sauvegardé : {pickle_path}\")\n", "\n", "# 2. MÉTADON<PERSON><PERSON><PERSON> ADAPTÉES AU CONTEXTE \"FIRST PURCHASE\"\n", "# Calcul des statistiques spécifiques\n", "mono_buyers_pct = (df_final['frequency'] == 1).sum() / len(df_final) * 100 if 'frequency' in df_final.columns else 0\n", "avg_recency = df_final['recency'].mean() if 'recency' in df_final.columns else 0\n", "avg_monetary = df_final['monetary'].mean() if 'monetary' in df_final.columns else 0\n", "\n", "metadata = {\n", "    'dataset_name': 'cleaned_customer_data_first_purchase',\n", "    'strategy': 'First Purchase Segmentation',\n", "    'shape': df_final.shape,\n", "    'columns': list(df_final.columns),\n", "    'dtypes': {col: str(dtype) for col, dtype in df_final.dtypes.items()},\n", "    'data_quality': {\n", "        'mono_buyers_percentage': round(mono_buyers_pct, 2),\n", "        'avg_recency_days': round(avg_recency, 2),\n", "        'avg_monetary_value': round(avg_monetary, 2),\n", "        'missing_values': df_final.isnull().sum().to_dict()\n", "    },\n", "    'processing_steps': [\n", "        'Chargement depuis SQLite',\n", "        '🚨 DÉCOUVERTE : Pattern 1 client = 1 commande',\n", "        'Filtrage commandes livrées uniquement',\n", "        'Création métriques RFM adaptées',\n", "        'Analyse outliers contextualisée',\n", "        'Visualisations spécialisées First Purchase',\n", "        'Préparation pour segmentation adaptée'\n", "    ],\n", "    'next_steps': [\n", "        'Feature Engineering adapté (Notebook 2)',\n", "        'Clustering First Purchase (Notebook 3)',\n", "        'Stratégies acquisition/réactivation (Notebook 4)'\n", "    ]\n", "}\n", "\n", "# Sauvegarde métadonnées avec fonction utils\n", "save_results(metadata,\n", "            notebook_name=\"1\",\n", "            export_number=7,\n", "            base_name=\"dataset_metadata_first_purchase\",\n", "            category=\"processed\")\n", "\n", "# 3. RÉSUMÉ FINAL\n", "print(f\"\\n🎯 RÉSUMÉ DE LA STRATÉGIE IMPLÉMENTÉE :\")\n", "print(f\"✅ Données nettoyées et filtrées (commandes livrées)\")\n", "print(f\"✅ Pattern '1 client = 1 commande' détecté et documenté\")\n", "print(f\"✅ Variables critiques identifiées : récence + montant\")\n", "print(f\"✅ Outliers analysés dans contexte business\")\n", "print(f\"✅ Visualisations spécialisées créées\")\n", "print(f\"✅ Dataset prêt pour segmentation 'First Purchase'\")\n", "\n", "print(f\"\\n💾 FICHIERS EXPORTÉS :\")\n", "print(f\"- data/processed/1_01_cleaned_dataset.csv\")\n", "print(f\"- data/processed/1_01_cleaned_dataset.pkl\")\n", "print(f\"- reports/processed/1_07_dataset_metadata_first_purchase.json\")\n", "print(f\"- reports/figures/1_04_first_purchase_analysis.png\")\n", "print(f\"- reports/figures/1_05_secondary_variables_outliers.png\")\n", "print(f\"- reports/figures/1_06_geographic_analysis.png\")\n", "\n", "print(f\"\\n🚀 PRÊT POUR LE NOTEBOOK 2 : Feature Engineering adapté !\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2 Log de version et métadonnées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création du log de version et métadonnées détaillées\n", "import json\n", "from datetime import datetime\n", "\n", "print(f\"📋 Création du log de version et métadonnées détaillées\")\n", "\n", "# Calcul des statistiques de traitement\n", "original_shape = df.shape if 'df' in locals() else df_final.shape\n", "final_shape = df_final.shape\n", "\n", "# Métadonnées complètes du traitement\n", "processing_log = {\n", "    'processing_info': {\n", "        'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'notebook_version': '1.0',\n", "        'python_version': '3.13.2'\n", "    },\n", "    'data_transformation': {\n", "        'original_shape': original_shape,\n", "        'final_shape': final_shape\n", "    },\n", "    'data_quality': {\n", "        'missing_values_handled': True,\n", "        'outliers_detected': True,\n", "        'outliers_removed': <PERSON><PERSON><PERSON>,\n", "        'outliers_flagged': True\n", "    },\n", "    'feature_engineering': {\n", "        'rfm_metrics_created': True,\n", "        'temporal_features_extracted': True,\n", "        'customer_segmentation_prepared': True\n", "    },\n", "    'exports_created': {\n", "        'cleaned_dataset_csv': 'data/processed/1_01_cleaned_dataset.csv',\n", "        'cleaned_dataset_pickle': 'data/processed/1_01_cleaned_dataset.pkl',\n", "        'figures_exported': 12\n", "    }\n", "}\n", "\n", "# Sauvegarde du log de version\n", "log_path = 'data/processed/1_processing_log.json'\n", "with open(log_path, 'w', encoding='utf-8') as f:\n", "    json.dump(processing_log, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"✅ Log de version créé avec succès : {log_path}\")\n", "print(f\"📊 Dataset final : {df_final.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### 🎆 Résumé des étapes réalisées - STRATÉGIE \"FIRST PURCHASE\"\n", "\n", "- ✅ **Chargement des données** : Connexion SQLite et chargement des tables Olist\n", "- 🚨 **DÉCOUVERTE MAJEURE** : Pattern \"1 client = 1 commande\" détecté (>95% des clients)\n", "- ✅ **Analyse critique des statuts** : Filtrage des commandes non livrées (1,234 supprimées)\n", "- ✅ **Nettoyage adapté** : Conservation uniquement des commandes livrées avec dates valides\n", "- ✅ **Métriques RFM adaptées** : Récence et Montant (Fréquence = 1 pour tous)\n", "- ✅ **Analyse des outliers contextualisée** : Conservation pour segmentation business\n", "- ✅ **Visualisations spécialisées** : Graphiques adaptés au contexte \"First Purchase\"\n", "- ✅ **Exports conformes** : Dataset et métadonnées selon conventions projet\n", "\n", "### 📈 Insights clés découverts - RÉVOLUTIONNAIRES\n", "\n", "- 🚨 **PATTERN CRITIQUE** : 99,441 commandes = 99,441 clients → Chaque client n'a qu'UNE commande\n", "- ⚠️ **RFM classique inadapté** : Frequency = 1 partout, variables de variabilité nulles\n", "- 🎯 **Nouvelle approche requise** : Segmentation \"First Purchase\" au lieu de fidélisation\n", "- 💰 **Variables critiques** : <PERSON><PERSON><PERSON> (quand ?) + <PERSON><PERSON> (combien ?) + <PERSON><PERSON><PERSON> (où/comment ?)\n", "- 🗺️ **Potentiel géographique** : Analyse par états pour géo-marketing\n", "- 📊 **Outliers = opportunités** : Clients VIP et segments spéciaux identifiés\n", "\n", "### 🚀 Prochaines étapes - STRATÉGIE ADAPTÉE\n", "\n", "➡️ **Notebook 2 :** Feature Engineering \"First Purchase\" (6 variables max au lieu de 22)\n", "➡️ **Notebook 3 :** Clustering adapté (Récence + Montant + Contexte)\n", "➡️ **Notebook 4 :** Stratégies acquisition/réactivation (pas fidélisation)\n", "➡️ **Notebook 5 :** Maintenance adaptée au contexte mono-achat\n", "\n", "### 🎯 IMPACT BUSINESS ATTENDU\n", "\n", "- **Segments exploitables** : Premium Newcomers, Regional Shoppers, Seasonal Buyers\n", "- **Stratégies d'acquisition** : Cibler des profils similaires aux segments performants\n", "- **Campagnes de réactivation** : Relancer les clients pour générer le 2e achat\n", "- **Géo-marketing** : Stratégies spécifiques par région/état\n", "\n", "---\n", "\n", "**🎉 Dataset transformé selon la stratégie \"First Purchase\" !**\n", "\n", "**📁 Fichiers exportés :**\n", "- `data/processed/1_01_cleaned_dataset.csv` (donn<PERSON> netto<PERSON>)\n", "- `reports/figures/1_04_first_purchase_analysis.png` (analyse spécialisée)\n", "- `reports/figures/1_05_secondary_variables_outliers.png` (outliers)\n", "- `reports/figures/1_06_geographic_analysis.png` (géo-marketing)\n", "- `reports/processed/1_07_dataset_metadata_first_purchase.json` (métadonnées)\n", "\n", "**🚨 RÉVOLUTION MÉTHODOLOGIQUE : De la fidélisation à l'acquisition !**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}