{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 1 : Analyse exploratoire des données\n", "\n", "## Objectif\n", "<PERSON><PERSON><PERSON>, explorer et comprendre le jeu de données client pour préparer la segmentation. Inclure analyses univariée, bivariée et multivariée.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données\n", "\n", "### 1.1 Import des librairies de base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import des librairies de base\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "\n", "# Configuration des visualisations\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import des utilitaires du projet\n", "from utils.notebook_init import setup_notebook\n", "from utils.data_tools import load_data, basic_info\n", "from utils.preprocessing import detect_outliers, handle_missing_values\n", "from utils.visualization_optimizer import create_distribution_plots\n", "\n", "# Configuration du notebook\n", "setup_notebook()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du fichier client e-commerce"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données depuis la base SQLite\n", "data_path = 'data/raw/olist.db'\n", "\n", "# TODO: Charger les données client depuis la base\n", "# df = load_data(data_path)\n", "\n", "print(f\"Données chargées depuis : {data_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Aperçu rapide des données"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Aperçu rapide des données\n", "# print(f\"Shape du dataset : {df.shape}\")\n", "# print(f\"\\nTypes de données :\")\n", "# print(df.dtypes)\n", "# print(f\"\\nPremières lignes :\")\n", "# display(df.head())\n", "\n", "# Utilisation de l'utilitaire\n", "# basic_info(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.4 Vérification des doublons et de la clé primaire"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Vérification des doublons\n", "# print(f\"Nombre de doublons : {df.duplicated().sum()}\")\n", "# print(f\"Nombre de customer_id uniques : {df['customer_id'].nunique()}\")\n", "# print(f\"Nombre total de lignes : {len(df)}\")\n", "\n", "# Vérification de la clé primaire\n", "# if df['customer_id'].nunique() == len(df):\n", "#     print(\"✓ customer_id est bien une clé primaire\")\n", "# else:\n", "#     print(\"⚠️ customer_id n'est pas unique\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON> des valeurs manquantes\n", "\n", "### 2.1 Affichage du taux de valeurs manquantes par colonne"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: <PERSON><PERSON><PERSON> des valeurs manquantes\n", "# missing_data = df.isnull().sum()\n", "# missing_percent = (missing_data / len(df)) * 100\n", "# missing_df = pd.DataFrame({\n", "#     'Colonne': missing_data.index,\n", "#     'Valeurs_manquantes': missing_data.values,\n", "#     'Pourcentage': missing_percent.values\n", "# })\n", "# missing_df = missing_df[missing_df['Valeurs_manquantes'] > 0].sort_values('Pourcentage', ascending=False)\n", "# print(missing_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Visualisation heatmap des valeurs manquantes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Heatmap des valeurs manquantes\n", "# plt.figure(figsize=(12, 8))\n", "# sns.heatmap(df.isnull(), cbar=True, cmap='viridis', yticklabels=False)\n", "# plt.title('Heatmap des valeurs manquantes')\n", "# plt.xticks(rotation=45)\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Choix de la stratégie de traitement"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Traitement des valeurs manquantes\n", "# df_clean = handle_missing_values(df, strategy='drop', threshold=0.5)\n", "# print(f\"Shape après nettoyage : {df_clean.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON>iva<PERSON>\n", "\n", "### 3.1 Distribution des variables numériques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Histogrammes + KDE pour variables numériques\n", "# numeric_columns = df_clean.select_dtypes(include=[np.number]).columns\n", "# create_distribution_plots(df_clean, numeric_columns)\n", "\n", "# Exemple manuel pour une variable\n", "# plt.figure(figsize=(15, 5))\n", "# for i, col in enumerate(numeric_columns[:3], 1):\n", "#     plt.subplot(1, 3, i)\n", "#     df_clean[col].hist(bins=50, alpha=0.7, edgecolor='black')\n", "#     plt.title(f'Distribution de {col}')\n", "#     plt.xlabel(col)\n", "#     plt.ylabel('Fréquence')\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Distribution des variables qualitatives"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Countplots pour variables qualitatives\n", "# categorical_columns = df_clean.select_dtypes(include=['object']).columns\n", "#\n", "# plt.figure(figsize=(15, 10))\n", "# for i, col in enumerate(categorical_columns, 1):\n", "#     plt.subplot(2, 3, i)\n", "#     df_clean[col].value_counts().plot(kind='bar')\n", "#     plt.title(f'Distribution de {col}')\n", "#     plt.xticks(rotation=45)\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Statistiques descriptives détaillées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Statistiques descriptives avancées\n", "# desc_stats = df_clean.describe()\n", "# print(\"Statistiques descriptives :\")\n", "# display(desc_stats)\n", "\n", "# Calcul des mesures de forme\n", "# from scipy import stats\n", "# skewness = df_clean.select_dtypes(include=[np.number]).skew()\n", "# kurtosis = df_clean.select_dtypes(include=[np.number]).kurtosis()\n", "#\n", "# shape_stats = pd.DataFrame({\n", "#     'Skewness': skewness,\n", "#     'Kurtosis': kurtosis\n", "# })\n", "# print(\"\\nMesures de forme (asymétrie et aplatissement) :\")\n", "# display(shape_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON>\n", "\n", "### 4.1 Corrélations entre variables quantitatives"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: <PERSON><PERSON>\n", "# numeric_df = df_clean.select_dtypes(include=[np.number])\n", "# correlation_matrix = numeric_df.corr()\n", "#\n", "# plt.figure(figsize=(12, 10))\n", "# sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,\n", "#             square=True, linewidths=0.5)\n", "# plt.title('Matrice de corrélation des variables numériques')\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Relations entre variables continues"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Scatterplots pour relations importantes\n", "# # Exemple : relation entre montant et fréquence\n", "# if 'total_amount' in df_clean.columns and 'frequency' in df_clean.columns:\n", "#     plt.figure(figsize=(10, 6))\n", "#     plt.scatter(df_clean['frequency'], df_clean['total_amount'], alpha=0.6)\n", "#     plt.xlabel('<PERSON>é<PERSON> d\\'achat')\n", "#     plt.ylabel('Montant total')\n", "#     plt.title('Relation entre fréquence et montant')\n", "#     plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Relations entre variables quantitatives et qualitatives"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Boxplots entre variables quantitatives et qualitatives\n", "# # Exemple : montant par catégorie\n", "# if 'category' in df_clean.columns and 'total_amount' in df_clean.columns:\n", "#     plt.figure(figsize=(12, 6))\n", "#     sns.boxplot(data=df_clean, x='category', y='total_amount')\n", "#     plt.title('Distribution du montant par catégorie')\n", "#     plt.xticks(rotation=45)\n", "#     plt.tight_layout()\n", "#     plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "### 5.1 Pairplot des variables clés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Pairplot ciblé sur variables candidates pour segmentation\n", "# # Sélection des variables les plus importantes pour la segmentation\n", "# key_variables = ['recency', 'frequency', 'monetary']  # Variables RFM\n", "#\n", "# if all(var in df_clean.columns for var in key_variables):\n", "#     sns.pairplot(df_clean[key_variables], diag_kind='kde')\n", "#     plt.suptitle('Relations entre variables RFM', y=1.02)\n", "#     plt.tight_layout()\n", "#     plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Préparation pour l'Analyse en Composantes Principales (optionnel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Préparation ACP si nécessaire\n", "# from sklearn.decomposition import PCA\n", "# from sklearn.preprocessing import StandardScaler\n", "#\n", "# # Standardisation des données pour ACP\n", "# scaler = StandardScaler()\n", "# X_scaled = scaler.fit_transform(numeric_df)\n", "#\n", "# # ACP exploratoire\n", "# pca = PCA()\n", "# pca.fit(X_scaled)\n", "#\n", "# # <PERSON><PERSON><PERSON> expliquée\n", "# plt.figure(figsize=(10, 6))\n", "# plt.bar(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_)\n", "# plt.xlabel('Composantes principales')\n", "# plt.ylabel('Variance expliquée')\n", "# plt.title('Variance expliquée par composante')\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Détection et traitement des outliers\n", "\n", "### 6.1 Détection par Z-score et IQR"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Détection des outliers\n", "# outliers_info = detect_outliers(df_clean, methods=['zscore', 'iqr'])\n", "# print(\"Outliers détectés :\")\n", "# for col, outliers in outliers_info.items():\n", "#     print(f\"{col}: {len(outliers)} outliers ({len(outliers)/len(df_clean)*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Visualisation des outliers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Visualisation des outliers avec boxplots\n", "# numeric_cols = df_clean.select_dtypes(include=[np.number]).columns\n", "#\n", "# plt.figure(figsize=(15, 10))\n", "# for i, col in enumerate(numeric_cols, 1):\n", "#     plt.subplot(2, 3, i)\n", "#     sns.boxplot(y=df_clean[col])\n", "#     plt.title(f'Boxplot de {col}')\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.3 Suppression ou traitement des outliers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Traitement des outliers selon pertinence métier\n", "# # Dé<PERSON>ir les seuils selon le contexte business\n", "# df_no_outliers = df_clean.copy()\n", "#\n", "# # Exemple : suppression des outliers extrêmes (Z-score > 3)\n", "# from scipy import stats\n", "# for col in numeric_cols:\n", "#     z_scores = np.abs(stats.zscore(df_clean[col].dropna()))\n", "#     df_no_outliers = df_no_outliers[z_scores < 3]\n", "#\n", "# print(f\"Dataset original : {df_clean.shape}\")\n", "# print(f\"Dataset sans outliers extrêmes : {df_no_outliers.shape}\")\n", "# print(f\"Suppression de {df_clean.shape[0] - df_no_outliers.shape[0]} lignes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Feature Engineering de base\n", "\n", "### 7.1 Calcul de l'ancienneté client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul de l'ancienneté client\n", "# from utils.feature_engineering import calculate_customer_age\n", "#\n", "# # Si on a une colonne de date d'inscription\n", "# if 'first_purchase_date' in df_no_outliers.columns:\n", "#     df_no_outliers['customer_age_days'] = calculate_customer_age(\n", "#         df_no_outliers['first_purchase_date']\n", "#     )\n", "#     print(f\"Ancienneté moyenne : {df_no_outliers['customer_age_days'].mean():.0f} jours\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7.2 Métriques d'achat et d'activité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul des métriques d'achat\n", "# # <PERSON><PERSON>'a<PERSON>, panier moyen, etc.\n", "# if 'total_amount' in df_no_outliers.columns and 'frequency' in df_no_outliers.columns:\n", "#     df_no_outliers['average_basket'] = df_no_outliers['total_amount'] / df_no_outliers['frequency']\n", "#\n", "#     print(\"Métriques calculées :\")\n", "#     print(f\"- <PERSON><PERSON> moyen : {df_no_outliers['average_basket'].mean():.2f}\")\n", "#     print(f\"- <PERSON><PERSON><PERSON> moyenne : {df_no_outliers['frequency'].mean():.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7.3 Conversion et traitement des dates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Conversion des colonnes de dates\n", "# date_columns = df_no_outliers.select_dtypes(include=['datetime64']).columns\n", "# print(f\"Colonnes de dates trouvées : {list(date_columns)}\")\n", "#\n", "# # Extraction de features temporelles si nécessaire\n", "# for col in date_columns:\n", "#     df_no_outliers[f'{col}_year'] = df_no_outliers[col].dt.year\n", "#     df_no_outliers[f'{col}_month'] = df_no_outliers[col].dt.month\n", "#     df_no_outliers[f'{col}_weekday'] = df_no_outliers[col].dt.weekday"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Sauvegarde du dataset nettoyé\n", "\n", "### 8.1 Export du DataFrame nettoyé"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Sauvegarde du dataset nettoyé\n", "# from utils.save_load import save_dataframe\n", "#\n", "# # Export CSV\n", "# output_path = 'data/processed/cleaned_data.csv'\n", "# df_no_outliers.to_csv(output_path, index=False)\n", "# print(f\"Dataset nettoyé sauvegardé : {output_path}\")\n", "#\n", "# # Export pickle pour préserver les types\n", "# pickle_path = 'data/processed/cleaned_data.pkl'\n", "# df_no_outliers.to_pickle(pickle_path)\n", "# print(f\"Dataset pickle sauvegardé : {pickle_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2 Log de version et métadonnées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Création du log de version\n", "# metadata = {\n", "#     'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "#     'original_shape': df.shape,\n", "#     'cleaned_shape': df_no_outliers.shape,\n", "#     'columns': list(df_no_outliers.columns),\n", "#     'missing_values_handled': True,\n", "#     'outliers_removed': True,\n", "#     'feature_engineering': 'basic'\n", "# }\n", "#\n", "# import json\n", "# with open('data/processed/cleaning_log.json', 'w') as f:\n", "#     json.dump(metadata, f, indent=2)\n", "#\n", "# print(\"Log de version créé avec succès!\")\n", "# print(f\"Dataset final : {df_no_outliers.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ Chargement et exploration initiale des données\n", "- ✅ Analyse et traitement des valeurs manquantes\n", "- ✅ Analyse univariée complète\n", "- ✅ Analyse bivariée et multivariée\n", "- ✅ Détection et traitement des outliers\n", "- ✅ Feature engineering de base\n", "- ✅ Sauvegarde du dataset nettoyé\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 2 :** Feature Engineering RFM et création des variables de segmentation\n", "\n", "---\n", "\n", "**Dataset nettoyé prêt pour la phase de feature engineering !**"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}