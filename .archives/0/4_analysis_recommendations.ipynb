{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 4 : Analyse des segments & Recommandations marketing\n", "\n", "## Objectif\n", "Analyser les segments obtenus via le clustering, identifier des profils clients clairs et formuler des recommandations concrètes et personnalisées pour chaque groupe.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données clusterisées\n", "\n", "### 1.1 Import des librairies d'analyse marketing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import des librairies\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "from scipy import stats\n", "\n", "# Configuration\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"Set1\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import des utilitaires du projet\n", "from utils.notebook_init import setup_notebook\n", "from utils.marketing_reco import (\n", "    create_customer_personas,\n", "    generate_marketing_recommendations,\n", "    calculate_clv_potential\n", ")\n", "from utils.analysis_tools import (\n", "    analyze_segment_behavior,\n", "    calculate_segment_metrics,\n", "    identify_opportunities\n", ")\n", "from utils.visualize_clusters import (\n", "    create_radar_charts,\n", "    plot_segment_comparison,\n", "    create_persona_dashboards\n", ")\n", "from utils.save_load import load_dataframe\n", "\n", "# Configuration du notebook\n", "setup_notebook()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du DataFrame enrichi avec labels de clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données clusterisées du Notebook 3\n", "data_path = 'data/processed/customers_clustered.pkl'\n", "summary_path = 'data/processed/customer_segments_summary.csv'\n", "analysis_path = 'data/processed/cluster_analysis_detailed.csv'\n", "\n", "# TODO: Charger les datasets\n", "# df_clustered = load_dataframe(data_path)\n", "# df_summary = pd.read_csv(summary_path)\n", "# cluster_stats = pd.read_csv(analysis_path, index_col=0)\n", "#\n", "# print(f\"Dataset clusterisé : {df_clustered.shape}\")\n", "# print(f\"Colonnes : {list(df_clustered.columns)}\")\n", "# print(f\"\\nNombre de clusters : {df_clustered['cluster'].nunique()}\")\n", "# print(f\"Clusters : {sorted(df_clustered['cluster'].unique())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérification du nombre et répartition des clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Vérification de la répartition des clusters\n", "# cluster_distribution = df_clustered.groupby(['cluster', 'cluster_profile']).size().reset_index(name='count')\n", "# cluster_distribution['percentage'] = (cluster_distribution['count'] / len(df_clustered) * 100).round(1)\n", "#\n", "# print(\"Répartition des clusters :\")\n", "# display(cluster_distribution)\n", "#\n", "# # Visualisation de la répartition\n", "# plt.figure(figsize=(12, 6))\n", "#\n", "# plt.subplot(1, 2, 1)\n", "# colors = plt.cm.Set1(np.linspace(0, 1, len(cluster_distribution)))\n", "# plt.pie(cluster_distribution['count'], labels=cluster_distribution['cluster_profile'],\n", "#         autopct='%1.1f%%', colors=colors, startangle=90)\n", "# plt.title('Répartition des clients par segment')\n", "#\n", "# plt.subplot(1, 2, 2)\n", "# plt.bar(cluster_distribution['cluster'], cluster_distribution['count'], color=colors)\n", "# plt.title('Nombre de clients par cluster')\n", "# plt.xlabel('Cluster ID')\n", "# plt.ylabel('Nombre de clients')\n", "#\n", "# for i, v in enumerate(cluster_distribution['count']):\n", "#     plt.text(i, v + 10, str(v), ha='center', va='bottom')\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Analyse descriptive des clusters\n", "\n", "### 2.1 <PERSON><PERSON><PERSON>, médianes et statistiques descriptives par cluster"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Analyse descriptive détaillée par cluster\n", "# # Variables clés pour l'analyse\n", "# key_vars = ['recency', 'frequency', 'monetary_total', 'monetary_avg',\n", "#            'customer_lifespan_days', 'days_since_first_order',\n", "#            'avg_days_between_orders']\n", "#\n", "# # Statistiques par cluster\n", "# desc_stats_by_cluster = df_clustered.groupby('cluster')[key_vars].describe()\n", "#\n", "# print(\"Statistiques descriptives par cluster :\")\n", "# display(desc_stats_by_cluster.round(2))\n", "#\n", "# # Focus sur les moyennes pour comparaison rapide\n", "# cluster_means = df_clustered.groupby(['cluster', 'cluster_profile'])[key_vars].mean().round(2)\n", "# print(\"\\nMoyennes par cluster :\")\n", "# display(cluster_means)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Comparaison des segments via graphiques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Visualisations comparatives entre clusters\n", "# # Boxplots pour comparer les distributions\n", "# fig, axes = plt.subplots(2, 4, figsize=(20, 10))\n", "# axes = axes.ravel()\n", "#\n", "# for i, var in enumerate(key_vars):\n", "#     if i < len(axes):\n", "#         sns.boxplot(data=df_clustered, x='cluster', y=var, ax=axes[i])\n", "#         axes[i].set_title(f'Distribution de {var} par cluster')\n", "#         axes[i].tick_params(axis='x', rotation=45)\n", "#\n", "# # Graphi<PERSON> de la répartition (dernière subplot)\n", "# if len(key_vars) < len(axes):\n", "#     axes[len(key_vars)].bar(cluster_distribution['cluster'], cluster_distribution['percentage'])\n", "#     axes[len(key_vars)].set_title('% de clients par cluster')\n", "#     axes[len(key_vars)].set_xlabel('Cluster')\n", "#     axes[len(key_vars)].set_ylabel('Pourcentage')\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Radar charts pour visualiser les profils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Création de radar charts pour chaque cluster\n", "# from math import pi\n", "#\n", "# # Normalisation des données pour le radar chart (0-1)\n", "# radar_vars = ['recency', 'frequency', 'monetary_total', 'monetary_avg', 'customer_lifespan_days']\n", "# radar_data = cluster_means[radar_vars].copy()\n", "#\n", "# # Inversion de la récence (plus c'est bas, mieux c'est)\n", "# radar_data['recency'] = 1 - (radar_data['recency'] - radar_data['recency'].min()) / (radar_data['recency'].max() - radar_data['recency'].min())\n", "#\n", "# # Normalisation des autres variables\n", "# for col in ['frequency', 'monetary_total', 'monetary_avg', 'customer_lifespan_days']:\n", "#     radar_data[col] = (radar_data[col] - radar_data[col].min()) / (radar_data[col].max() - radar_data[col].min())\n", "#\n", "# # Création du radar chart\n", "# n_clusters = len(radar_data)\n", "# n_vars = len(radar_vars)\n", "#\n", "# angles = [n / float(n_vars) * 2 * pi for n in range(n_vars)]\n", "# angles += angles[:1]  # <PERSON><PERSON><PERSON> le cercle\n", "#\n", "# fig, axes = plt.subplots(1, n_clusters, figsize=(5*n_clusters, 5), subplot_kw=dict(projection='polar'))\n", "# if n_clusters == 1:\n", "#     axes = [axes]\n", "#\n", "# colors = plt.cm.Set1(np.linspace(0, 1, n_clusters))\n", "#\n", "# for idx, (cluster_id, row) in enumerate(radar_data.iterrows()):\n", "#     values = row.tolist()\n", "#     values += values[:1]  # <PERSON><PERSON><PERSON> le polygone\n", "#\n", "#     ax = axes[idx]\n", "#     ax.plot(angles, values, 'o-', linewidth=2, color=colors[idx], label=f'Cluster {cluster_id[0]}')\n", "#     ax.fill(angles, values, alpha=0.25, color=colors[idx])\n", "#     ax.set_xticks(angles[:-1])\n", "#     ax.set_xticklabels(['<PERSON><PERSON><PERSON>\\n(inversée)', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>\\ntotal', '<PERSON><PERSON>\\nmoyen', 'Ancienne<PERSON>'])\n", "#     ax.set_ylim(0, 1)\n", "#     ax.set_title(f'Cluster {cluster_id[0]}: {cluster_id[1]}', pad=20)\n", "#     ax.grid(True)\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Définition des personas clients\n", "\n", "### 3.1 Création de fiches profils clients types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Création détaillée des personas\n", "# personas = {}\n", "#\n", "# for cluster_id in sorted(df_clustered['cluster'].unique()):\n", "#     cluster_data = df_clustered[df_clustered['cluster'] == cluster_id]\n", "#     cluster_profile = cluster_data['cluster_profile'].iloc[0]\n", "#\n", "#     # Calcul des métriques principales\n", "#     metrics = {\n", "#         'taille': len(cluster_data),\n", "#         'pourcentage': len(cluster_data) / len(df_clustered) * 100,\n", "#         'recency_mean': cluster_data['recency'].mean(),\n", "#         'recency_median': cluster_data['recency'].median(),\n", "#         'frequency_mean': cluster_data['frequency'].mean(),\n", "#         'frequency_median': cluster_data['frequency'].median(),\n", "#         'monetary_total_mean': cluster_data['monetary_total'].mean(),\n", "#         'monetary_total_median': cluster_data['monetary_total'].median(),\n", "#         'monetary_avg_mean': cluster_data['monetary_avg'].mean(),\n", "#         'lifespan_mean': cluster_data['customer_lifespan_days'].mean(),\n", "#         'days_since_first_mean': cluster_data['days_since_first_order'].mean()\n", "#     }\n", "#\n", "#     # Description comportementale\n", "#     if metrics['recency_mean'] <= 30:\n", "#         activite = \"Très actif (achat ré<PERSON>)\"\n", "#     elif metrics['recency_mean'] <= 90:\n", "#         activite = \"Actif\"\n", "#     elif metrics['recency_mean'] <= 180:\n", "#         activite = \"Moyennement actif\"\n", "#     else:\n", "#         activite = \"Inactif/Risque d'attrition\"\n", "#\n", "#     if metrics['frequency_mean'] >= 5:\n", "#         fidelite = \"Très fidèle\"\n", "#     elif metrics['frequency_mean'] >= 3:\n", "#         fidelite = \"Fid<PERSON><PERSON>\"\n", "#     elif metrics['frequency_mean'] >= 2:\n", "#         fidelite = \"Occasionnel\"\n", "#     else:\n", "#         fidelite = \"Acheteur unique\"\n", "#\n", "#     if metrics['monetary_total_mean'] >= 500:\n", "#         valeur = \"Haute valeur\"\n", "#     elif metrics['monetary_total_mean'] >= 200:\n", "#         valeur = \"Valeur moyenne\"\n", "#     else:\n", "#         valeur = \"Faible valeur\"\n", "#\n", "#     personas[cluster_id] = {\n", "#         'nom': cluster_profile,\n", "#         'metrics': metrics,\n", "#         'comportement': {\n", "#             'activite': activite,\n", "#             'fidelite': fidelite,\n", "#             'valeur': valeur\n", "#         }\n", "#     }\n", "#\n", "# # Affich<PERSON> des personas\n", "# print(\"=== PERSONAS CLIENTS ===\")\n", "# for cluster_id, persona in personas.items():\n", "#     print(f\"\\n👥 CLUSTER {cluster_id}: {persona['nom']}\")\n", "#     print(f\"   Taille: {persona['metrics']['taille']:,} clients ({persona['metrics']['pourcentage']:.1f}%)\")\n", "#     print(f\"   Activité: {persona['comportement']['activite']}\")\n", "#     print(f\"   Fidélité: {persona['comportement']['fidelite']}\")\n", "#     print(f\"   Valeur: {persona['comportement']['valeur']}\")\n", "#     print(f\"   \\n   📊 Métriques clés:\")\n", "#     print(f\"   - <PERSON><PERSON><PERSON> moyenne: {persona['metrics']['recency_mean']:.0f} jours\")\n", "#     print(f\"   - <PERSON><PERSON><PERSON> moyenne: {persona['metrics']['frequency_mean']:.1f} achats\")\n", "#     print(f\"   - Valeur totale moyenne: {persona['metrics']['monetary_total_mean']:.0f}€\")\n", "#     print(f\"   - <PERSON><PERSON> moyen: {persona['metrics']['monetary_avg_mean']:.0f}€\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Association de chaque segment à un comportement d'achat représentatif"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Analyse des patterns comportementaux spécifiques\n", "# import matplotlib.dates as mdates\n", "#\n", "# # Si on a des données temporelles, analyser la saisonnalité\n", "# if 'order_date' in df_clustered.columns:\n", "#     # Analyse de la saisonnalité par cluster\n", "#     df_clustered['order_month'] = pd.to_datetime(df_clustered['order_date']).dt.month\n", "#     df_clustered['order_quarter'] = pd.to_datetime(df_clustered['order_date']).dt.quarter\n", "#\n", "#     seasonal_analysis = df_clustered.groupby(['cluster', 'order_quarter']).size().unstack(fill_value=0)\n", "#     seasonal_analysis_pct = seasonal_analysis.div(seasonal_analysis.sum(axis=1), axis=0) * 100\n", "#\n", "#     print(\"\\nAnalyse saisonnière des achats par cluster (%) :\")\n", "#     display(seasonal_analysis_pct.round(1))\n", "#\n", "# # <PERSON><PERSON><PERSON> de <PERSON> entre commandes\n", "# if 'avg_days_between_orders' in df_clustered.columns:\n", "#     interval_analysis = df_clustered.groupby('cluster')['avg_days_between_orders'].describe()\n", "#     print(\"\\nAnalyse des délais entre commandes :\")\n", "#     display(interval_analysis.round(1))\n", "#\n", "# # <PERSON><PERSON><PERSON> de taille de panier\n", "# basket_analysis = df_clustered.groupby('cluster').agg({\n", "#     'monetary_avg': ['mean', 'std', 'min', 'max'],\n", "#     'frequency': ['mean', 'std']\n", "# }).round(2)\n", "#\n", "# print(\"\\nAnalyse des patterns de panier :\")\n", "# display(basket_analysis)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Recommandations marketing\n", "\n", "### 4.1 Déclinaison des leviers marketing selon les clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Génération des recommandations marketing par cluster\n", "# recommendations = {}\n", "#\n", "# for cluster_id, persona in personas.items():\n", "#     metrics = persona['metrics']\n", "#     behavior = persona['comportement']\n", "#\n", "#     # Détermination des stratégies selon le profil\n", "#     strategies = []\n", "#     channels = []\n", "#     content = []\n", "#     kpis = []\n", "#\n", "#     # Stratégies selon l'activité\n", "#     if \"Très actif\" in behavior['activite']:\n", "#         strategies.extend([\"Fidélisation premium\", \"Up-selling\", \"Cross-selling\"])\n", "#         channels.extend([\"Email personnalisé\", \"App mobile\", \"Programme VIP\"])\n", "#         content.extend([\"Offres exclusives\", \"Nouveautés en avant-première\", \"Contenu premium\"])\n", "#         kpis.extend([\"<PERSON>x de rétention\", \"Panier moyen\", \"<PERSON><PERSON><PERSON> d'achat\"])\n", "#\n", "#     elif \"Actif\" in behavior['activite']:\n", "#         strategies.extend([\"Maintien de l'engagement\", \"Stimulation fréquence\"])\n", "#         channels.extend([\"Email régulier\", \"Notifications push\", \"Réseaux sociaux\"])\n", "#         content.extend([\"Recommandations personnalisées\", \"Offres saisonnières\"])\n", "#         kpis.extend([\"Engagement email\", \"Fréquence d'achat\"])\n", "#\n", "#     elif \"Inactif\" in behavior['activite']:\n", "#         strategies.extend([\"Réactivation\", \"Win-back campaigns\"])\n", "#         channels.extend([\"Email de réactivation\", \"Publicité display\", \"Remarketing\"])\n", "#         content.extend([\"Offres de retour\", \"Rappel des bénéfices\", \"Témoignages\"])\n", "#         kpis.extend([\"Taux de réactivation\", \"Taux d'ouverture\", \"Conversion\"])\n", "#\n", "#     # Ajustements selon la valeur\n", "#     if \"Haute valeur\" in behavior['valeur']:\n", "#         strategies.append(\"Programme VIP\")\n", "#         channels.append(\"Service client dédié\")\n", "#         content.append(\"Expériences exclusives\")\n", "#\n", "#     elif \"Faible valeur\" in behavior['valeur']:\n", "#         strategies.append(\"Optimisation du panier\")\n", "#         channels.append(\"Publicité ciblée économique\")\n", "#         content.append(\"Offres de volume\")\n", "#\n", "#     # Ajustements selon la fidélité\n", "#     if \"Acheteur unique\" in behavior['fidelite']:\n", "#         strategies.append(\"Conversion en client répétant\")\n", "#         channels.append(\"Email de bienvenue séquencé\")\n", "#         content.append(\"Guide d'utilisation\")\n", "#\n", "#     recommendations[cluster_id] = {\n", "#         'persona': persona['nom'],\n", "#         'strategies': list(set(strategies)),\n", "#         'channels': list(set(channels)),\n", "#         'content': list(set(content)),\n", "#         'kpis': list(set(kpis)),\n", "#         'priority': 'Haute' if behavior['valeur'] == 'Haute valeur' else 'Moyenne' if behavior['activite'] != 'Inactif/Risque d\\'attrition' else 'Critique'\n", "#     }\n", "#\n", "# # Affichage des recommandations\n", "# print(\"=== RECOMMANDATIONS MARKETING ===\")\n", "# for cluster_id, reco in recommendations.items():\n", "#     print(f\"\\n🎯 CLUSTER {cluster_id}: {reco['persona']}\")\n", "#     print(f\"   Priorité: {reco['priority']}\")\n", "#     print(f\"   \\n   📊 Stratégies recommandées:\")\n", "#     for strategy in reco['strategies'][:3]:  # Top 3\n", "#         print(f\"   - {strategy}\")\n", "#     print(f\"   \\n   📡 Canaux privilégiés:\")\n", "#     for channel in reco['channels'][:3]:  # Top 3\n", "#         print(f\"   - {channel}\")\n", "#     print(f\"   \\n   📝 Contenu recommandé:\")\n", "#     for content_item in reco['content'][:3]:  # Top 3\n", "#         print(f\"   - {content_item}\")\n", "#     print(f\"   \\n   📈 KPIs à suivre:\")\n", "#     for kpi in reco['kpis'][:3]:  # Top 3\n", "#         print(f\"   - {kpi}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Mise en forme des recommandations (tableau synthétique)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Création d'un tableau de synthèse des recommandations\n", "# # Tableau récapitulatif pour présentation\n", "# reco_summary = []\n", "#\n", "# for cluster_id, reco in recommendations.items():\n", "#     reco_summary.append({\n", "#         'Cluster': cluster_id,\n", "#         'Persona': reco['persona'],\n", "#         'Taille': personas[cluster_id]['metrics']['taille'],\n", "#         'Priorité': reco['priority'],\n", "#         'Stratégie_principale': reco['strategies'][0] if reco['strategies'] else 'N/A',\n", "#         'Canal_principal': reco['channels'][0] if reco['channels'] else 'N/A',\n", "#         'KPI_principal': reco['kpis'][0] if reco['kpis'] else 'N/A'\n", "#     })\n", "#\n", "# reco_df = pd.DataFrame(reco_summary)\n", "# reco_df = reco_df.sort_values(['Priorité', '<PERSON>lle'], ascending=[True, False])\n", "#\n", "# print(\"Tableau de synthèse des recommandations marketing :\")\n", "# display(reco_df)\n", "#\n", "# # Export pour présentation\n", "# reco_df.to_csv('data/processed/marketing_recommendations_summary.csv', index=False)\n", "# print(\"\\n✅ Tableau de recommandations sauvegardé : marketing_recommendations_summary.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Carte des actions marketing par segment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Visualisation de la carte des actions\n", "# # <PERSON><PERSON> vs Activité pour prioriser les actions\n", "# action_matrix = []\n", "#\n", "# for cluster_id, persona in personas.items():\n", "#     metrics = persona['metrics']\n", "#     behavior = persona['comportement']\n", "#\n", "#     # Scores pour la matrice (0-100)\n", "#     if \"Haute valeur\" in behavior['valeur']:\n", "#         value_score = 100\n", "#     elif \"Valeur moyenne\" in behavior['valeur']:\n", "#         value_score = 60\n", "#     else:\n", "#         value_score = 30\n", "#\n", "#     if \"Très actif\" in behavior['activite']:\n", "#         activity_score = 100\n", "#     elif \"Actif\" in behavior['activite']:\n", "#         activity_score = 70\n", "#     elif \"Moyennement actif\" in behavior['activite']:\n", "#         activity_score = 40\n", "#     else:\n", "#         activity_score = 10\n", "#\n", "#     action_matrix.append({\n", "#         'cluster': cluster_id,\n", "#         'persona': persona['nom'],\n", "#         'value_score': value_score,\n", "#         'activity_score': activity_score,\n", "#         'size': metrics['taille']\n", "#     })\n", "#\n", "# matrix_df = pd.DataFrame(action_matrix)\n", "#\n", "# # Visualisation de la matrice\n", "# plt.figure(figsize=(12, 8))\n", "#\n", "# # <PERSON><PERSON> des bulles proportionnelle au nombre de clients\n", "# sizes = (matrix_df['size'] / matrix_df['size'].max() * 1000)\n", "#\n", "# scatter = plt.scatter(matrix_df['activity_score'], matrix_df['value_score'],\n", "#                      s=sizes, alpha=0.6, c=matrix_df['cluster'], cmap='Set1')\n", "#\n", "# # Annotations\n", "# for idx, row in matrix_df.iterrows():\n", "#     plt.annotate(f\"C{row['cluster']}\\n{row['persona'][:15]}...\",\n", "#                 (row['activity_score'], row['value_score']),\n", "#                 xytext=(5, 5), textcoords='offset points', fontsize=9)\n", "#\n", "# # Quadrants\n", "# plt.axhline(y=60, color='gray', linestyle='--', alpha=0.5)\n", "# plt.axvline(x=60, color='gray', linestyle='--', alpha=0.5)\n", "#\n", "# plt.xlabel('Score d\\'Activité')\n", "# plt.ylabel('Score de Valeur')\n", "# plt.title('Matrice de Priorisation Marketing\\n(Taille des bulles = nombre de clients)')\n", "#\n", "# # Légendes des quadrants\n", "# plt.text(80, 80, 'CHAMPIONS\\nFidélisation premium', ha='center', va='center',\n", "#         bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))\n", "# plt.text(80, 40, 'POTENTIELS\\nUp-selling', ha='center', va='center',\n", "#         bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.5))\n", "# plt.text(30, 80, 'DORMANTS\\nRéactivation', ha='center', va='center',\n", "#         bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.5))\n", "# plt.text(30, 40, 'À RISQUE\\nWin-back', ha='center', va='center',\n", "#         bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.5))\n", "#\n", "# plt.xlim(0, 110)\n", "# plt.ylim(0, 110)\n", "# plt.grid(True, alpha=0.3)\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Temporalité & limites\n", "\n", "### 5.1 Analyse de la stabilité des clusters dans le temps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: <PERSON><PERSON><PERSON> de la stabilité temporelle (si données historiques disponibles)\n", "# # Simulation d'analyse de stabilité temporelle\n", "# import random\n", "# np.random.seed(42)\n", "#\n", "# # Simulation de l'évolution des segments sur 6 mois\n", "# stability_analysis = {\n", "#     'period': ['Mois -5', 'Mois -4', 'Mois -3', '<PERSON>is -2', '<PERSON>is -1', 'Actuel'],\n", "#     'n_clusters_optimal': [4, 4, 3, 4, 4, len(personas)],\n", "#     'silhouette_score': [0.45, 0.48, 0.42, 0.47, 0.49, 0.51],\n", "#     'cluster_stability': [75, 78, 68, 82, 85, 100]  # % de clients restant dans le même cluster\n", "# }\n", "#\n", "# stability_df = pd.DataFrame(stability_analysis)\n", "#\n", "# print(\"Analyse de stabilité temporelle (simulation) :\")\n", "# display(stability_df)\n", "#\n", "# # Visualisation de la stabilité\n", "# fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "#\n", "# # Évolution du nombre de clusters optimal\n", "# axes[0].plot(stability_df['period'], stability_df['n_clusters_optimal'], 'o-', linewidth=2)\n", "# axes[0].set_title('Évolution du nombre optimal de clusters')\n", "# axes[0].set_ylabel('Nombre de clusters')\n", "# axes[0].grid(True, alpha=0.3)\n", "# axes[0].tick_params(axis='x', rotation=45)\n", "#\n", "# # Évolution de la qualité (silhouette)\n", "# axes[1].plot(stability_df['period'], stability_df['silhouette_score'], 'o-', linewidth=2, color='green')\n", "# axes[1].set_title('Évolution de la qualité de segmentation')\n", "# axes[1].set_ylabel('Score de Silhouette')\n", "# axes[1].grid(True, alpha=0.3)\n", "# axes[1].tick_params(axis='x', rotation=45)\n", "#\n", "# # Stabilité des clusters\n", "# axes[2].plot(stability_df['period'], stability_df['cluster_stability'], 'o-', linewidth=2, color='orange')\n", "# axes[2].set_title('Stabilité des assignations de clusters')\n", "# axes[2].set_ylabel('% de stabilité')\n", "# axes[2].grid(True, alpha=0.3)\n", "# axes[2].tick_params(axis='x', rotation=45)\n", "#\n", "# plt.tight_layout()\n", "# plt.show()\n", "#\n", "# # Recommandations sur la fréquence de mise à jour\n", "# avg_stability = np.mean(stability_df['cluster_stability'][:-1])\n", "# if avg_stability >= 80:\n", "#     update_freq = \"Trimestrielle\"\n", "#     reason = \"Stabilité élevée des segments\"\n", "# elif avg_stability >= 70:\n", "#     update_freq = \"Bimestrielle\"\n", "#     reason = \"Stabilité modérée nécessitant un suivi régulier\"\n", "# else:\n", "#     update_freq = \"Mensuelle\"\n", "#     reason = \"Segments instables nécessitant un suivi fréquent\"\n", "#\n", "# print(f\"\\n📅 RECOMMANDATION DE FRÉQUENCE DE MISE À JOUR :\")\n", "# print(f\"Fréquence recommandée : {update_freq}\")\n", "# print(f\"Justification : {reason} (stabilité moyenne : {avg_stability:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Limites de la segmentation : bruit, évolutivité, biais"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Identification et documentation des limites\n", "# limitations = {\n", "#     'Techniques': [\n", "#         f\"Algorithme K-Means sensible aux outliers\",\n", "#         f\"Nombre de clusters fixe ({len(personas)}) peut ne pas refléter la réalité\",\n", "#         f\"Variables normalisées peuvent masquer certaines nuances\",\n", "#         f\"Segmentation basée sur les données historiques uniquement\"\n", "#     ],\n", "#     'Données': [\n", "#         f\"Période d'analyse limitée (à spécifier selon les données)\",\n", "#         f\"Possibles biais de sélection dans les données clients\",\n", "#         f\"Variables comportementales manquantes (satisfaction, NPS, etc.)\",\n", "#         f\"Données démographiques limitées\"\n", "#     ],\n", "#     'Business': [\n", "#         f\"Segments peuvent ne pas être exploitables avec les ressources actuelles\",\n", "#         f\"Évolution du marché peut rendre la segmentation obsolète\",\n", "#         f\"Réglementation (RGPD) peut limiter l'utilisation de certaines données\",\n", "#         f\"Coût d'acquisition vs valeur client à valider\"\n", "#     ],\n", "#     'Évolutivité': [\n", "#         f\"Nouveaux clients difficiles à classifier sans historique\",\n", "#         f\"Changements saisonniers peuvent affecter les segments\",\n", "#         f\"Croissance de l'entreprise peut modifier les profils\",\n", "#         f\"Nouveaux produits/services peuvent créer de nouveaux segments\"\n", "#     ]\n", "# }\n", "#\n", "# print(\"=== LIMITES DE LA SEGMENTATION ===\")\n", "# for category, limits in limitations.items():\n", "#     print(f\"\\n🔍 {category} :\")\n", "#     for limit in limits:\n", "#         print(f\"   ⚠️ {limit}\")\n", "#\n", "# # Recommandations pour atténuer les limites\n", "# mitigation_strategies = {\n", "#     'Court terme (1-3 mois)': [\n", "#         \"Valider les segments avec l'équipe métier\",\n", "#         \"Tester les recommandations sur un échantillon\",\n", "#         \"Collecter des feedbacks sur l'actionabilité\",\n", "#         \"Mesurer l'impact des premières actions\"\n", "#     ],\n", "#     '<PERSON><PERSON><PERSON> terme (3-6 mois)': [\n", "#         \"Enrichir avec des données de satisfaction client\",\n", "#         \"Intégrer des données comportementales web/app\",\n", "#         \"Automatiser le scoring des nouveaux clients\",\n", "#         \"Développer un dashboard de suivi des segments\"\n", "#     ],\n", "#     'Long terme (6+ mois)': [\n", "#         \"Mettre en place une collecte de données en temps réel\",\n", "#         \"Développer des modèles prédictifs par segment\",\n", "#         \"Intégrer l'IA pour la personnalisation\",\n", "#         \"Créer un système de recommandations dynamiques\"\n", "#     ]\n", "# }\n", "#\n", "# print(\"\\n=== STRATÉGIES D'AMÉLIORATION ===\")\n", "# for timeframe, strategies in mitigation_strategies.items():\n", "#     print(f\"\\n📅 {timeframe} :\")\n", "#     for strategy in strategies:\n", "#         print(f\"   ✅ {strategy}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Préparation de la présentation\n", "\n", "### 6.1 Export des visuels pertinents pour le livrable PowerPoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Génération et export des visuels pour présentation\n", "# import os\n", "#\n", "# # Création du dossier pour les exports\n", "# export_dir = 'exports/presentation_visuals'\n", "# os.makedirs(export_dir, exist_ok=True)\n", "#\n", "# # Recréation des graphiques principaux pour export\n", "# # 1. Répartition des segments\n", "# plt.figure(figsize=(10, 6))\n", "# colors_pres = plt.cm.Set1(np.linspace(0, 1, len(cluster_distribution)))\n", "# plt.pie(cluster_distribution['count'], labels=cluster_distribution['cluster_profile'],\n", "#         autopct='%1.1f%%', colors=colors_pres, startangle=90)\n", "# plt.title('Répartition des clients par segment', fontsize=16, fontweight='bold')\n", "# plt.savefig(f'{export_dir}/01_repartition_segments.png', dpi=300, bbox_inches='tight')\n", "# plt.close()\n", "#\n", "# # 2. Comparaison des métriques clés\n", "# fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "# key_metrics_viz = ['recency', 'frequency', 'monetary_total', 'monetary_avg']\n", "# titles = ['<PERSON><PERSON><PERSON> (jours)', '<PERSON><PERSON><PERSON> (achats)', '<PERSON><PERSON> total (€)', '<PERSON><PERSON> moyen (€)']\n", "#\n", "# for i, (metric, title) in enumerate(zip(key_metrics_viz, titles)):\n", "#     row, col = i // 2, i % 2\n", "#     cluster_means[metric].plot(kind='bar', ax=axes[row, col], color=colors_pres)\n", "#     axes[row, col].set_title(title, fontweight='bold')\n", "#     axes[row, col].tick_params(axis='x', rotation=45)\n", "#     axes[row, col].grid(True, alpha=0.3)\n", "#\n", "# plt.tight_layout()\n", "# plt.savefig(f'{export_dir}/02_metriques_par_segment.png', dpi=300, bbox_inches='tight')\n", "# plt.close()\n", "#\n", "# # 3. <PERSON><PERSON> de priorisation (refaite)\n", "# plt.figure(figsize=(12, 8))\n", "# sizes = (matrix_df['size'] / matrix_df['size'].max() * 1000)\n", "# scatter = plt.scatter(matrix_df['activity_score'], matrix_df['value_score'],\n", "#                      s=sizes, alpha=0.7, c=matrix_df['cluster'], cmap='Set1')\n", "#\n", "# for idx, row in matrix_df.iterrows():\n", "#     plt.annotate(f\"Segment {row['cluster']}\",\n", "#                 (row['activity_score'], row['value_score']),\n", "#                 xytext=(5, 5), textcoords='offset points', fontsize=11, fontweight='bold')\n", "#\n", "# plt.axhline(y=60, color='gray', linestyle='--', alpha=0.5)\n", "# plt.axvline(x=60, color='gray', linestyle='--', alpha=0.5)\n", "# plt.xlabel('Score d\\'Activité', fontsize=14)\n", "# plt.ylabel('Score de Valeur', fontsize=14)\n", "# plt.title('Matrice de Priorisation Marketing', fontsize=16, fontweight='bold')\n", "#\n", "# # Légendes des quadrants améliorées\n", "# plt.text(80, 85, 'CHAMPIONS', ha='center', va='center', fontsize=12, fontweight='bold',\n", "#         bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))\n", "# plt.text(80, 35, 'POTENTIELS', ha='center', va='center', fontsize=12, fontweight='bold',\n", "#         bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))\n", "# plt.text(30, 85, 'DORMANTS', ha='center', va='center', fontsize=12, fontweight='bold',\n", "#         bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))\n", "# plt.text(30, 35, 'À RISQUE', ha='center', va='center', fontsize=12, fontweight='bold',\n", "#         bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n", "#\n", "# plt.xlim(0, 110)\n", "# plt.ylim(0, 110)\n", "# plt.grid(True, alpha=0.3)\n", "# plt.savefig(f'{export_dir}/03_matrice_priorisation.png', dpi=300, bbox_inches='tight')\n", "# plt.close()\n", "#\n", "# print(f\"✅ Visuels exportés dans {export_dir}/\")\n", "# print(f\"   - 01_repartition_segments.png\")\n", "# print(f\"   - 02_metriques_par_segment.png\")\n", "# print(f\"   - 03_matrice_priorisation.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Tableaux de synthèse formatés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Création des tableaux finaux pour présentation\n", "# # Tableau exécutif des segments\n", "# executive_summary = []\n", "#\n", "# for cluster_id, persona in personas.items():\n", "#     metrics = persona['metrics']\n", "#     behavior = persona['comportement']\n", "#     reco = recommendations[cluster_id]\n", "#\n", "#     executive_summary.append({\n", "#         'Segment': f\"Segment {cluster_id}\",\n", "#         'Nom': persona['nom'],\n", "#         'Taille': f\"{metrics['taille']:,} clients\",\n", "#         'Pourcentage': f\"{metrics['pourcentage']:.1f}%\",\n", "#         'Récence_moy': f\"{metrics['recency_mean']:.0f} jours\",\n", "#         'Fréquence_moy': f\"{metrics['frequency_mean']:.1f}\",\n", "#         'Valeur_totale': f\"{metrics['monetary_total_mean']:.0f}€\",\n", "#         'Stratégie_clé': reco['strategies'][0] if reco['strategies'] else 'N/A',\n", "#         'Priorité': reco['priority']\n", "#     })\n", "#\n", "# exec_df = pd.DataFrame(executive_summary)\n", "# exec_df = exec_df.sort_values('Priorité', key=lambda x: x.map({'Haute': 1, 'Moyenne': 2, 'Critique': 3}))\n", "#\n", "# print(\"=== TABLEAU EXÉCUTIF DES SEGMENTS ===\")\n", "# display(exec_df)\n", "#\n", "# # Export des tableaux\n", "# exec_df.to_csv(f'{export_dir}/executive_summary.csv', index=False)\n", "# reco_df.to_csv(f'{export_dir}/marketing_recommendations.csv', index=False)\n", "#\n", "# # Création d'un fichier de synthèse marketing\n", "# marketing_synthesis = {\n", "#     'date_analyse': datetime.now().strftime('%Y-%m-%d'),\n", "#     'nombre_clients_analyses': len(df_clustered),\n", "#     'nombre_segments': len(personas),\n", "#     'segments_prioritaires': exec_df[exec_df['Priorité'] == 'Haute']['Segment'].tolist(),\n", "#     'actions_immediates': [\n", "#         f\"Cibler les {exec_df.iloc[0]['Nom']} ({exec_df.iloc[0]['<PERSON>lle']}) avec {exec_df.iloc[0]['Stratégie_clé']}\",\n", "#         f\"Surveiller les segments à risque d'attrition\",\n", "#         f\"Développer les programmes de fidélisation pour les hautes valeurs\"\n", "#     ],\n", "#     'kpis_suivre': list(set([kpi for reco in recommendations.values() for kpi in reco['kpis'][:2]])),\n", "#     'frequence_mise_a_jour': update_freq\n", "# }\n", "#\n", "# with open(f'{export_dir}/marketing_synthesis.json', 'w') as f:\n", "#     json.dump(marketing_synthesis, f, indent=2, ensure_ascii=False)\n", "#\n", "# print(f\"\\n✅ Tableaux et synthèse exportés :\")\n", "# print(f\"   - executive_summary.csv\")\n", "# print(f\"   - marketing_recommendations.csv\")\n", "# print(f\"   - marketing_synthesis.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ Chargement et analyse des segments du Notebook 3\n", "- ✅ Création de personas clients détaillés\n", "- ✅ Analyse comportementale par segment\n", "- ✅ Génération de recommandations marketing personnalisées\n", "- ✅ Évaluation de la stabilité temporelle\n", "- ✅ Identification des limites et axes d'amélioration\n", "- ✅ Préparation des livrables pour présentation\n", "\n", "### Segments actionables identifiés\n", "<!-- Les segments seront listés lors de l'exécution -->\n", "\n", "### Recommandations prioritaires\n", "1. **I<PERSON><PERSON><PERSON><PERSON> (1 mois)** : <PERSON><PERSON><PERSON> les segments à haute valeur avec des campagnes de fidélisation\n", "2. **Court terme (3 mois)** : Développer des campagnes de réactivation pour les segments dormants\n", "3. **<PERSON><PERSON><PERSON> terme (6 mois)** : <PERSON><PERSON> en place un système de scoring automatique des nouveaux clients\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 5 :** Analyse de maintenance et simulation pour le contrat de maintenance\n", "\n", "---\n", "\n", "**Recommandations marketing prêtes pour implémentation !**"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}