{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 3 : Clustering & Segmentation\n", "\n", "## Objectif\n", "Appliquer des algorithmes de clustering (K-Means, éventuellement DBSCAN ou Agglomératif) sur les données normalisées. Déterminer le nombre optimal de clusters, analyser les profils par segment, et visualiser les résultats.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données préparées\n", "\n", "### 1.1 Import des librairies de clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import des librairies\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import silhouette_score, calinski_harabasz_score\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "from itertools import combinations\n", "\n", "# Configuration\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"Set2\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import des utilitaires du projet\n", "from utils.notebook_init import setup_notebook\n", "from utils.clustering import (\n", "    find_optimal_clusters,\n", "    perform_kmeans_clustering,\n", "    evaluate_clustering_quality\n", ")\n", "from utils.visualize_clusters import (\n", "    plot_cluster_analysis,\n", "    plot_pca_clusters,\n", "    create_cluster_profiles\n", ")\n", "from utils.save_load import load_dataframe, save_dataframe\n", "from utils.analysis_tools import analyze_cluster_characteristics\n", "\n", "# Configuration du notebook\n", "setup_notebook()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Import du jeu de données normalisé"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données préparées du Notebook 2\n", "data_path_scaled = 'data/processed/features_scaled_for_clustering.pkl'\n", "data_path_with_ids = 'data/processed/features_scaled_with_ids.pkl'\n", "data_path_complete = 'data/processed/rfm_enriched_complete.pkl'\n", "\n", "# TODO: Charger les datasets\n", "# X_scaled = load_dataframe(data_path_scaled)\n", "# X_with_ids = load_dataframe(data_path_with_ids)\n", "# df_complete = load_dataframe(data_path_complete)\n", "#\n", "# print(f\"Dataset pour clustering : {X_scaled.shape}\")\n", "# print(f\"Variables : {list(X_scaled.columns)}\")\n", "# print(f\"\\nDataset avec IDs : {X_with_ids.shape}\")\n", "# print(f\"Dataset complet : {df_complete.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Rappel des variables utilisées pour la segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Vérification et rappel des variables\n", "# print(\"Variables utilisées pour la segmentation :\")\n", "# for i, col in enumerate(X_scaled.columns, 1):\n", "#     print(f\"{i:2d}. {col}\")\n", "#\n", "# print(f\"\\nStatistiques descriptives (données normalisées) :\")\n", "# display(X_scaled.describe())\n", "#\n", "# # Vérification de la normalisation\n", "# print(f\"\\nVérification de la normalisation :\")\n", "# print(f\"Moyennes (doivent être ~0) : {X_scaled.mean().abs().max():.6f}\")\n", "# print(f\"Écarts-types (doivent être ~1) : {X_scaled.std().mean():.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Recherche du nombre optimal de clusters\n", "\n", "### 2.1 Méthode du coude (Elbow Method)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Méthode du coude pour déterminer k optimal\n", "# # Calcul de l'inertie pour différentes valeurs de k\n", "# k_range = range(2, 11)\n", "# inertias = []\n", "#\n", "# for k in k_range:\n", "#     kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "#     kmeans.fit(X_scaled)\n", "#     inertias.append(kmeans.inertia_)\n", "#\n", "# # Visualisation de la méthode du coude\n", "# plt.figure(figsize=(12, 5))\n", "#\n", "# plt.subplot(1, 2, 1)\n", "# plt.plot(k_range, inertias, 'bo-', linewidth=2, markersize=8)\n", "# plt.title('<PERSON><PERSON><PERSON><PERSON>ude - Inertie vs <PERSON>')\n", "# plt.xlabel('Nombre de clusters (K)')\n", "# plt.ylabel('Inertie')\n", "# plt.grid(True, alpha=0.3)\n", "#\n", "# # Calcul des différences pour identifier le \"coude\"\n", "# differences = np.diff(inertias)\n", "# second_differences = np.diff(differences)\n", "#\n", "# plt.subplot(1, 2, 2)\n", "# plt.plot(k_range[1:-1], second_differences, 'ro-', linewidth=2, markersize=8)\n", "# plt.title('Deuxième dérivée (identification du coude)')\n", "# plt.xlabel('Nombre de clusters (K)')\n", "# plt.ylabel('Deuxième dérivée')\n", "# plt.grid(True, alpha=0.3)\n", "#\n", "# plt.tight_layout()\n", "# plt.show()\n", "#\n", "# print(f\"Inerties par k : {dict(zip(k_range, inertias))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Score de silhouette pour chaque k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Calcul du score de silhouette pour différentes valeurs de k\n", "# silhouette_scores = []\n", "# calinski_scores = []\n", "#\n", "# for k in k_range:\n", "#     kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "#     cluster_labels = kmeans.fit_predict(X_scaled)\n", "#\n", "#     silhouette_avg = silhouette_score(X_scaled, cluster_labels)\n", "#     calinski_score = calinski_harabasz_score(X_scaled, cluster_labels)\n", "#\n", "#     silhouette_scores.append(silhouette_avg)\n", "#     calinski_scores.append(calinski_score)\n", "#\n", "# # Visualisation des scores\n", "# fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "#\n", "# # Score de silhouette\n", "# axes[0].plot(k_range, silhouette_scores, 'go-', linewidth=2, markersize=8)\n", "# axes[0].set_title('<PERSON> de Si<PERSON>hou<PERSON> vs <PERSON>')\n", "# axes[0].set_xlabel('Nombre de clusters (K)')\n", "# axes[0].set_ylabel('Score de Silhouette')\n", "# axes[0].grid(True, alpha=0.3)\n", "#\n", "# # <PERSON> <PERSON> Calinski-Hara<PERSON>z\n", "# axes[1].plot(k_range, calinski_scores, 'mo-', linewidth=2, markersize=8)\n", "# axes[1].set_title('Score de Calinski-Hara<PERSON>z vs K')\n", "# axes[1].set_xlabel('Nombre de clusters (K)')\n", "# axes[1].set_ylabel('Score de Calinski-Harabasz')\n", "# axes[1].grid(True, alpha=0.3)\n", "#\n", "# # Comparaison des trois métriques\n", "# ax2 = axes[2]\n", "# ax2.plot(k_range, np.array(inertias)/max(inertias), 'b-', label='Inertie (normalisée)', linewidth=2)\n", "# ax2_twin = ax2.twinx()\n", "# ax2_twin.plot(k_range, silhouette_scores, 'g-', label='Silhouette', linewidth=2)\n", "# ax2.set_xlabel('Nombre de clusters (K)')\n", "# ax2.set_ylabel('Inertie normalisée', color='b')\n", "# ax2_twin.set_ylabel('Score de Silhouette', color='g')\n", "# ax2.set_title('Comparaison des métriques')\n", "# ax2.grid(True, alpha=0.3)\n", "#\n", "# plt.tight_layout()\n", "# plt.show()\n", "#\n", "# # Identification du k optimal\n", "# optimal_k_silhouette = k_range[np.argmax(silhouette_scores)]\n", "# print(f\"K optimal selon le score de silhouette : {optimal_k_silhouette}\")\n", "# print(f\"Score de silhouette maximum : {max(silhouette_scores):.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Visualisation conjointe et choix justifié de k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Synthèse pour le choix de k\n", "# # Tableau de synthèse\n", "# results_df = pd.DataFrame({\n", "#     'K': k_range,\n", "#     'Inertie': inertias,\n", "#     'Silhouette': silhouette_scores,\n", "#     '<PERSON><PERSON><PERSON>_Harabasz': calinski_scores\n", "# })\n", "#\n", "# # Ajout des variations relatives\n", "# results_df['Inertie_reduction'] = results_df['Inertie'].pct_change().fillna(0).abs()\n", "#\n", "# print(\"Tableau de synthèse pour le choix de K :\")\n", "# display(results_df.round(3))\n", "#\n", "# # Recommandation basée sur les métriques\n", "# print(\"\\n=== RECOMMANDATIONS ===\")\n", "# print(f\"Méthode du coude : rechercher le point d'inflexion dans le graphique\")\n", "# print(f\"Score de silhouette optimal : K = {optimal_k_silhouette} (score = {max(silhouette_scores):.3f})\")\n", "# print(f\"Score de Calinski-Harabasz optimal : K = {k_range[np.argmax(calinski_scores)]} (score = {max(calinski_scores):.0f})\")\n", "#\n", "# # Choix final (peut être ajusté selon l'analyse)\n", "# optimal_k = optimal_k_silhouette  # Peut être modifié selon l'expertise métier\n", "# print(f\"\\n🎯 CHOIX FINAL : K = {optimal_k}\")\n", "# print(f\"Justification : [à compléter selon l'analyse des graphiques]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON> <PERSON><PERSON>\n", "\n", "### 3.1 Entraînement de KMeans avec k optimal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Entraînement du modèle K-Means final\n", "# # Entraînement avec le k optimal\n", "# kmeans_final = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "# cluster_labels = kmeans_final.fit_predict(X_scaled)\n", "#\n", "# # Ajout des labels aux datasets\n", "# X_with_clusters = X_scaled.copy()\n", "# X_with_clusters['cluster'] = cluster_labels\n", "#\n", "# X_with_ids_clustered = X_with_ids.copy()\n", "# X_with_ids_clustered['cluster'] = cluster_labels\n", "#\n", "# df_complete_clustered = df_complete.copy()\n", "# df_complete_clustered['cluster'] = cluster_labels\n", "#\n", "# print(f\"Clustering réalisé avec K = {optimal_k}\")\n", "# print(f\"Score de silhouette final : {silhouette_score(X_scaled, cluster_labels):.3f}\")\n", "#\n", "# # Distribution des clusters\n", "# cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()\n", "# print(f\"\\nDistribution des clusters :\")\n", "# for cluster_id, count in cluster_counts.items():\n", "#     percentage = (count / len(cluster_labels)) * 100\n", "#     print(f\"Cluster {cluster_id}: {count} clients ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Analyse des centres de clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Analyse des centres de clusters\n", "# # Récupération des centres\n", "# cluster_centers = kmeans_final.cluster_centers_\n", "# centers_df = pd.DataFrame(cluster_centers, columns=X_scaled.columns)\n", "# centers_df.index = [f'Cluster_{i}' for i in range(optimal_k)]\n", "#\n", "# print(\"Centres des clusters (valeurs normalisées) :\")\n", "# display(centers_df.round(3))\n", "#\n", "# # Visualisation des centres\n", "# plt.figure(figsize=(15, 8))\n", "# sns.heatmap(centers_df.T, annot=True, cmap='RdBu_r', center=0,\n", "#             fmt='.2f', cbar_kws={'label': 'Valeur normalisée'})\n", "# plt.title('Heatmap des centres de clusters\\n(Valeurs normalisées)')\n", "# plt.xlabel('Clusters')\n", "# plt.ylabel('Variables')\n", "# plt.tight_layout()\n", "# plt.show()\n", "#\n", "# # Identification des caractéristiques principales par cluster\n", "# print(\"\\nCaractéristiques principales par cluster :\")\n", "# for i, cluster_name in enumerate(centers_df.index):\n", "#     center = centers_df.iloc[i]\n", "#     high_features = center[center > 0.5].sort_values(ascending=False)\n", "#     low_features = center[center < -0.5].sort_values()\n", "#\n", "#     print(f\"\\n{cluster_name}:\")\n", "#     if len(high_features) > 0:\n", "#         print(f\"  ⬆️ Valeurs élevées: {list(high_features.index[:3])}\")\n", "#     if len(low_features) > 0:\n", "#         print(f\"  ⬇️ Valeurs faibles: {list(low_features.index[:3])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Sauvegarde du modèle entraîné (optionnel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: <PERSON><PERSON><PERSON><PERSON> du modèle entraîné\n", "# import joblib\n", "#\n", "# # <PERSON><PERSON><PERSON><PERSON> du modèle\n", "# model_path = 'models/kmeans_segmentation.pkl'\n", "# joblib.dump(kmeans_final, model_path)\n", "# print(f\"Mod<PERSON>le sauve<PERSON> : {model_path}\")\n", "#\n", "# # Sauvegarde des métadonnées du modèle\n", "# import json\n", "# from datetime import datetime\n", "#\n", "# model_metadata = {\n", "#     'model_type': 'KMeans',\n", "#     'n_clusters': optimal_k,\n", "#     'features_used': list(X_scaled.columns),\n", "#     'n_samples': len(X_scaled),\n", "#     'silhouette_score': silhouette_score(X_scaled, cluster_labels),\n", "#     'inertia': kmeans_final.inertia_,\n", "#     'random_state': 42,\n", "#     'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "# }\n", "#\n", "# with open('models/kmeans_metadata.json', 'w') as f:\n", "#     json.dump(model_metadata, f, indent=2)\n", "#\n", "# print(\"Métadonnées du modèle sauvegardées\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Réduction de dimension pour visualisation\n", "\n", "### 4.1 Application de PCA (2D et 3D)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Réduction de dimension avec PCA\n", "# # PCA 2D pour visualisation\n", "# pca_2d = PCA(n_components=2, random_state=42)\n", "# X_pca_2d = pca_2d.fit_transform(X_scaled)\n", "#\n", "# # PCA 3D pour visualisation avancée\n", "# pca_3d = PCA(n_components=3, random_state=42)\n", "# X_pca_3d = pca_3d.fit_transform(X_scaled)\n", "#\n", "# # <PERSON><PERSON><PERSON> expliquée\n", "# print(f\"Variance expliquée PCA 2D : {pca_2d.explained_variance_ratio_.sum():.3f}\")\n", "# print(f\"Détail PCA 2D : PC1={pca_2d.explained_variance_ratio_[0]:.3f}, PC2={pca_2d.explained_variance_ratio_[1]:.3f}\")\n", "# print(f\"Variance expliquée PCA 3D : {pca_3d.explained_variance_ratio_.sum():.3f}\")\n", "#\n", "# # Création des DataFrames pour visualisation\n", "# pca_2d_df = pd.DataFrame({\n", "#     'PC1': X_pca_2d[:, 0],\n", "#     'PC2': X_pca_2d[:, 1],\n", "#     'cluster': cluster_labels\n", "# })\n", "#\n", "# pca_3d_df = pd.DataFrame({\n", "#     'PC1': X_pca_3d[:, 0],\n", "#     'PC2': X_pca_3d[:, 1],\n", "#     'PC3': X_pca_3d[:, 2],\n", "#     'cluster': cluster_labels\n", "# })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Visualisation des clusters (scatterplot coloré)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Visualisation 2D des clusters\n", "# # Graphique 2D des clusters\n", "# plt.figure(figsize=(15, 6))\n", "#\n", "# # Vue PCA 2D\n", "# plt.subplot(1, 2, 1)\n", "# colors = plt.cm.Set2(np.linspace(0, 1, optimal_k))\n", "# for i in range(optimal_k):\n", "#     mask = cluster_labels == i\n", "#     plt.scatter(X_pca_2d[mask, 0], X_pca_2d[mask, 1],\n", "#                c=[colors[i]], label=f'Cluster {i}', alpha=0.7, s=50)\n", "#\n", "# plt.xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.1%} de variance)')\n", "# plt.ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.1%} de variance)')\n", "# plt.title('Visualisation des clusters en 2D (PCA)')\n", "# plt.legend()\n", "# plt.grid(True, alpha=0.3)\n", "#\n", "# # Graphique de densité\n", "# plt.subplot(1, 2, 2)\n", "# for i in range(optimal_k):\n", "#     mask = cluster_labels == i\n", "#     plt.scatter(X_pca_2d[mask, 0], X_pca_2d[mask, 1],\n", "#                c=[colors[i]], label=f'Cluster {i}', alpha=0.4, s=30)\n", "#\n", "# # Ajout des centres des clusters en PCA\n", "# centers_pca = pca_2d.transform(cluster_centers)\n", "# plt.scatter(centers_pca[:, 0], centers_pca[:, 1],\n", "#            c='red', marker='x', s=200, linewidth=3, label='Centres')\n", "#\n", "# plt.xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.1%} de variance)')\n", "# plt.ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.1%} de variance)')\n", "# plt.title('Clusters avec centres (PCA 2D)')\n", "# plt.legend()\n", "# plt.grid(True, alpha=0.3)\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Visualisation 3D interactive (optionnel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Visualisation 3D des clusters\n", "# # Graphique 3D (nécessite matplotlib)\n", "# from mpl_toolkits.mplot3d import Axes3D\n", "#\n", "# fig = plt.figure(figsize=(12, 9))\n", "# ax = fig.add_subplot(111, projection='3d')\n", "#\n", "# colors = plt.cm.Set2(np.linspace(0, 1, optimal_k))\n", "# for i in range(optimal_k):\n", "#     mask = cluster_labels == i\n", "#     ax.scatter(X_pca_3d[mask, 0], X_pca_3d[mask, 1], X_pca_3d[mask, 2],\n", "#               c=[colors[i]], label=f'Cluster {i}', alpha=0.6, s=50)\n", "#\n", "# # Centres en 3D\n", "# centers_pca_3d = pca_3d.transform(cluster_centers)\n", "# ax.scatter(centers_pca_3d[:, 0], centers_pca_3d[:, 1], centers_pca_3d[:, 2],\n", "#           c='red', marker='x', s=200, linewidth=3, label='Centres')\n", "#\n", "# ax.set_xlabel(f'PC1 ({pca_3d.explained_variance_ratio_[0]:.1%})')\n", "# ax.set_ylabel(f'PC2 ({pca_3d.explained_variance_ratio_[1]:.1%})')\n", "# ax.set_zlabel(f'PC3 ({pca_3d.explained_variance_ratio_[2]:.1%})')\n", "# ax.set_title('Visualisation 3D des clusters (PCA)')\n", "# ax.legend()\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "### 5.1 Agrégation des indicateurs par cluster"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Analyse descriptive par cluster (données originales)\n", "# # Agrégation sur les données originales (non normalisées)\n", "# cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "#     'recency': ['mean', 'median', 'std'],\n", "#     'frequency': ['mean', 'median', 'std'],\n", "#     'monetary_total': ['mean', 'median', 'std'],\n", "#     'monetary_avg': ['mean', 'median', 'std'],\n", "#     'customer_lifespan_days': ['mean', 'median'],\n", "#     'days_since_first_order': ['mean', 'median'],\n", "#     'avg_days_between_orders': ['mean', 'median'],\n", "#     'order_std': ['mean', 'median'],\n", "#     'customer_id': 'count'  # <PERSON><PERSON> des clusters\n", "# }).round(2)\n", "#\n", "# # Aplatir les colonnes multi-index\n", "# cluster_analysis.columns = ['_'.join(col).strip() for col in cluster_analysis.columns]\n", "# cluster_analysis = cluster_analysis.rename(columns={'customer_id_count': 'cluster_size'})\n", "#\n", "# print(\"Analyse descriptive par cluster (valeurs originales) :\")\n", "# display(cluster_analysis)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Tableaux de synthèse et graphiques comparatifs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Tableaux de synthèse des profils\n", "# # Tableau simplifié des moyennes principales\n", "# key_metrics = [\n", "#     'recency_mean', 'frequency_mean', 'monetary_total_mean',\n", "#     'monetary_avg_mean', 'customer_lifespan_days_mean', 'cluster_size'\n", "# ]\n", "#\n", "# summary_table = cluster_analysis[key_metrics].copy()\n", "# summary_table.columns = ['<PERSON><PERSON><PERSON>_moy', '<PERSON><PERSON><PERSON>_moy', '<PERSON><PERSON>_total_moy',\n", "#                         '<PERSON><PERSON>_moy_moy', 'Ancienneté_moy', '<PERSON><PERSON>_cluster']\n", "#\n", "# # Ajout de pourcentages\n", "# summary_table['%_clients'] = (summary_table['Taille_cluster'] / summary_table['Taille_cluster'].sum() * 100).round(1)\n", "#\n", "# print(\"\\nTableau de synthèse des segments :\")\n", "# display(summary_table)\n", "#\n", "# # Graphiques comparatifs\n", "# fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "#\n", "# # <PERSON><PERSON><PERSON>\n", "# axes[0,0].bar(range(optimal_k), cluster_analysis['recency_mean'], color=colors)\n", "# axes[0,0].set_title('Récence moyenne par cluster')\n", "# axes[0,0].set_xlabel('Cluster')\n", "# axes[0,0].set_ylabel('<PERSON><PERSON> depuis dernier achat')\n", "#\n", "# # Fréquence\n", "# axes[0,1].bar(range(optimal_k), cluster_analysis['frequency_mean'], color=colors)\n", "# axes[0,1].set_title('Fréquence moyenne par cluster')\n", "# axes[0,1].set_xlabel('Cluster')\n", "# axes[0,1].set_ylabel('Nombre d\\'achats')\n", "#\n", "# # Montant total\n", "# axes[0,2].bar(range(optimal_k), cluster_analysis['monetary_total_mean'], color=colors)\n", "# axes[0,2].set_title('Montant total moyen par cluster')\n", "# axes[0,2].set_xlabel('Cluster')\n", "# axes[0,2].set_ylabel('Montant total (€)')\n", "#\n", "# # <PERSON><PERSON> moyen\n", "# axes[1,0].bar(range(optimal_k), cluster_analysis['monetary_avg_mean'], color=colors)\n", "# axes[1,0].set_title('<PERSON><PERSON> moyen par achat')\n", "# axes[1,0].set_xlabel('Cluster')\n", "# axes[1,0].set_ylabel('<PERSON><PERSON> moyen (€)')\n", "#\n", "# # <PERSON><PERSON>\n", "# axes[1,1].pie(summary_table['Taille_cluster'], labels=[f'Cluster {i}' for i in range(optimal_k)],\n", "#              autopct='%1.1f%%', colors=colors)\n", "# axes[1,1].set_title('Répartition des clients par cluster')\n", "#\n", "# # Ancienneté\n", "# axes[1,2].bar(range(optimal_k), cluster_analysis['customer_lifespan_days_mean'], color=colors)\n", "# axes[1,2].set_title('Ancienneté moyenne par cluster')\n", "# axes[1,2].set_xlabel('Cluster')\n", "# axes[1,2].set_ylabel('Jours')\n", "#\n", "# plt.tight_layout()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Identification des profils types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Identification et nommage des profils clients\n", "# # Analyse automatique des profils\n", "# profile_descriptions = {}\n", "#\n", "# for cluster_id in range(optimal_k):\n", "#     cluster_data = summary_table.iloc[cluster_id]\n", "#\n", "#     # Analyse des caractéristiques principales\n", "#     recency = cluster_data['Récence_moy']\n", "#     frequency = cluster_data['Fréquence_moy']\n", "#     monetary = cluster_data['Montant_total_moy']\n", "#\n", "#     # Classification basique (peut être affinée)\n", "#     if recency <= 50 and frequency >= 3 and monetary >= 200:\n", "#         profile = \"Clients Fidèles & Actifs\"\n", "#     elif recency <= 100 and monetary >= 300:\n", "#         profile = \"Clients à Forte Valeur\"\n", "#     elif frequency >= 4:\n", "#         profile = \"Acheteurs Fréquents\"\n", "#     elif recency >= 200:\n", "#         profile = \"Clients Inactifs/Perdus\"\n", "#     elif monetary <= 100:\n", "#         profile = \"Petits Acheteurs\"\n", "#     else:\n", "#         profile = \"Clients Occasionnels\"\n", "#\n", "#     profile_descriptions[cluster_id] = profile\n", "#\n", "# # Affichage des profils identifiés\n", "# print(\"\\n=== PROFILS CLIENTS IDENTIFIÉS ===\")\n", "# for cluster_id, profile in profile_descriptions.items():\n", "#     cluster_data = summary_table.iloc[cluster_id]\n", "#     print(f\"\\n📊 Cluster {cluster_id}: {profile}\")\n", "#     print(f\"   - Taille: {cluster_data['Taille_cluster']:.0f} clients ({cluster_data['%_clients']:.1f}%)\")\n", "#     print(f\"   - Récence: {cluster_data['Récence_moy']:.0f} jours\")\n", "#     print(f\"   - Fréquence: {cluster_data['Fréquence_moy']:.1f} achats\")\n", "#     print(f\"   - Valeur totale: {cluster_data['Montant_total_moy']:.0f}€\")\n", "#     print(f\"   - <PERSON><PERSON> moyen: {cluster_data['Mont<PERSON>_moy_moy']:.0f}€\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "### 6.1 Export du DataFrame labellisé avec les clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Sauvegarde des résultats de clustering\n", "# # Dataset complet avec clusters\n", "# df_complete_clustered['cluster_profile'] = df_complete_clustered['cluster'].map(profile_descriptions)\n", "#\n", "# # Sauvegarde des datasets clusterisés\n", "# df_complete_clustered.to_csv('data/processed/customers_clustered.csv', index=False)\n", "# df_complete_clustered.to_pickle('data/processed/customers_clustered.pkl')\n", "#\n", "# # Dataset résumé pour analyse rapide\n", "# summary_for_export = df_complete_clustered[[\n", "#     'customer_id', 'cluster', 'cluster_profile',\n", "#     'recency', 'frequency', 'monetary_total', 'monetary_avg'\n", "# ]].copy()\n", "#\n", "# summary_for_export.to_csv('data/processed/customer_segments_summary.csv', index=False)\n", "#\n", "# print(\"✅ Datasets sauvegardés :\")\n", "# print(\"- customers_clustered.pkl : dataset complet avec clusters\")\n", "# print(\"- customer_segments_summary.csv : résumé des segments\")\n", "#\n", "# print(f\"\\nNombre de clients segmentés : {len(df_complete_clustered)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Export des résumés et statistiques par segment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: Export des analyses et statistiques\n", "# # Export du tableau d'analyse des clusters\n", "# cluster_analysis.to_csv('data/processed/cluster_analysis_detailed.csv')\n", "# summary_table.to_csv('data/processed/cluster_summary.csv')\n", "#\n", "# # Export des informations de clustering\n", "# clustering_info = {\n", "#     'clustering_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "#     'algorithm': 'KMeans',\n", "#     'n_clusters': optimal_k,\n", "#     'silhouette_score': silhouette_score(X_scaled, cluster_labels),\n", "#     'inertia': kmeans_final.inertia_,\n", "#     'features_used': list(X_scaled.columns),\n", "#     'cluster_profiles': profile_descriptions,\n", "#     'cluster_sizes': summary_table['Taille_cluster'].to_dict(),\n", "#     'cluster_percentages': summary_table['%_clients'].to_dict(),\n", "#     'pca_variance_explained_2d': pca_2d.explained_variance_ratio_.tolist(),\n", "#     'pca_variance_explained_3d': pca_3d.explained_variance_ratio_.tolist()\n", "# }\n", "#\n", "# with open('data/processed/clustering_results.json', 'w') as f:\n", "#     json.dump(clustering_info, f, indent=2, default=str)\n", "#\n", "# print(\"✅ Analyses sauvegardées :\")\n", "# print(\"- cluster_analysis_detailed.csv : statistiques détaillées\")\n", "# print(\"- cluster_summary.csv : tableau de synthèse\")\n", "# print(\"- clustering_results.json : informations complètes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ Chargement des données préparées du Notebook 2\n", "- ✅ Détermination du nombre optimal de clusters (méthodes multiples)\n", "- ✅ Application de l'algorithme K-Means\n", "- ✅ Réduction de dimension PCA pour visualisation\n", "- ✅ Analyse d<PERSON> des profils par segment\n", "- ✅ Identification et nommage des personas clients\n", "- ✅ Sauvegarde des résultats et modèle\n", "\n", "### Segments identifiés\n", "<!-- Les profils seront automatiquement générés lors de l'exécution -->\n", "\n", "### Qualité de la segmentation\n", "<!-- Les métriques seront affichées lors de l'exécution -->\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 4 :** Analyse détaillée des segments et recommandations marketing\n", "\n", "---\n", "\n", "**Segmentation client réalisée avec succès ! Prêt pour l'analyse marketing.**"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}