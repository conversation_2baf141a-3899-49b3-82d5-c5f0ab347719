{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 2 : Feature Engineering (RFM & comportements)\n", "\n", "## Objectif\n", "Construire des variables pertinentes pour la segmentation client. Utiliser le modèle RFM (Récence, Fréquence, Montant), compléter avec d'autres variables comportementales, et préparer les données pour le clustering.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données nettoyées\n", "\n", "### 1.1 Import des librairies nécessaires"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/raw\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/clean\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/reports\n", "Le dossier source /Users/<USER>/Developer/OPC/P5/data/clean/features n'existe pas, rien à déplacer.\n", "==================================================\n", "\u001b[1m\u001b[95m🚀 INITIALISATION DU NOTEBOOK\u001b[0m\n", "==================================================\n", "\u001b[94mNotebook: 2_feature_engineering.ipynb\u001b[0m\n", "\u001b[94mGraine aléatoire: 42\u001b[0m\n", "\u001b[94mStyle seaborn: whitegrid\u001b[0m\n", "\u001b[94mTaille des figures: (12, 8)\u001b[0m\n", "Vérification des bibliothèques disponibles:\n", "==================================================\n", "- Python: 3.13.2\n", "- NumPy: 2.2.5\n", "- Pandas: 2.2.3\n", "- Matplotlib: 3.10.1\n", "- Seaborn: 0.13.2\n", "- Scikit-learn: 1.6.1\n", "- Folium: Disponible\n", "- Plotly: 6.0.1\n", "==================================================\n", "\u001b[92mVisualisations cartographiques interactives DISPONIBLES.\u001b[0m\n", "Options d'affichage pandas configurées:\n", "- max_rows: 100\n", "- max_columns: 100\n", "- width: 1000\n", "- precision: 4\n", "\u001b[94mAppel de setup_notebook_env pour configurer les dossiers...\u001b[0m\n", "\u001b[95m\n", "Dossiers d'export configurés par setup_notebook_env:\u001b[0m\n", "\u001b[92m- base_export: None\u001b[0m\n", "\u001b[92m- figures: /Users/<USER>/Developer/OPC/P5/reports/figures\u001b[0m\n", "\u001b[92m- maps: /Users/<USER>/Developer/OPC/P5/reports/maps\u001b[0m\n", "\u001b[92m- models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[94mExport config: Figures: /Users/<USER>/Developer/OPC/P5/reports/figures, Maps: /Users/<USER>/Developer/OPC/P5/reports/maps, Models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[92mSauvegarde automatique des figures activée (écraser existants: False).\u001b[0m\n", "\n", "==================================================\n", "\n", "\n", "📁 Répertoire de travail : /Users/<USER>/Developer/OPC/P5\n", "📊 Répertoire des rapports : /Users/<USER>/Developer/OPC/P5/reports\n", "🎲 Graine aléatoire : 42\n"]}], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime, timedelta\n", "\n", "# Preprocessing spécifique\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.feature_engineering import (\n", "    calculate_rfm,\n", "    create_temporal_features,\n", "    create_advanced_transactional_features,\n", "    consolidate_all_features,\n", "    prepare_clustering_features\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results\n", "from utils.clustering_visualization import export_figure\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"2_feature_engineering.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")\n", "\n", "print(f\"\\n📁 Répertoire de travail : {PROJECT_ROOT}\")\n", "print(f\"📊 Répertoire des rapports : {REPORTS_DIR}\")\n", "print(f\"🎲 Graine aléatoire : {SEED}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du fichier nettoyé produit par le Notebook 1"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Dataset chargé depuis : data/processed/1_01_cleaned_dataset.csv\n", "📊 Shape : (99441, 14)\n", "📋 Colonnes : ['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']\n", "\n", "🔍 Vérifications :\n", "- Valeurs manquantes : 2\n", "- Doublons : 0\n", "- Période des données : du 2016-09-04 21:15:19 au 2018-10-17 17:30:18\n"]}], "source": ["# Chargement des données nettoyées du Notebook 1\n", "# Le fichier a été sauvegardé avec la convention de nommage du projet\n", "data_path = 'data/processed/1_01_cleaned_dataset.csv'\n", "\n", "# Vérification de l'existence du fichier\n", "if not os.path.exists(data_path):\n", "    print(f\"⚠️ Fichier non trouvé : {data_path}\")\n", "    print(\"Veuillez d'abord exécuter le Notebook 1 pour générer les données nettoyées.\")\n", "else:\n", "    # Chargement du dataset nettoyé\n", "    df_clean = pd.read_csv(data_path)\n", "    print(f\"✅ Dataset chargé depuis : {data_path}\")\n", "    print(f\"📊 Shape : {df_clean.shape}\")\n", "    print(f\"📋 Colonnes : {list(df_clean.columns)}\")\n", "\n", "    # Vérification de l'intégrité\n", "    print(f\"\\n🔍 Vérifications :\")\n", "    print(f\"- Valeurs manquantes : {df_clean.isnull().sum().sum()}\")\n", "    print(f\"- Doublons : {df_clean.duplicated().sum()}\")\n", "\n", "    # Vérification de la période des données avec le bon nom de colonne\n", "    # Utilisation de 'first_order_date' qui devrait être disponible d'après le code précédent\n", "    if 'first_order_date' in df_clean.columns:\n", "        print(f\"- Période des données : du {df_clean['first_order_date'].min()} au {df_clean['first_order_date'].max()}\")\n", "    else:\n", "        print(\"⚠️ Colonne de date non trouvée. Colonnes disponibles :\")\n", "        print(df_clean.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérifications d'usage"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Toutes les métriques RFM sont présentes\n", "\n", "📋 Aperçu des données :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>frequency</th>\n", "      <th>first_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>recency</th>\n", "      <th>customer_state</th>\n", "      <th>customer_city</th>\n", "      <th>monetary</th>\n", "      <th>value_segment</th>\n", "      <th>frequency_outlier</th>\n", "      <th>recency_outlier</th>\n", "      <th>monetary_outlier</th>\n", "      <th>customer_age_days</th>\n", "      <th>customer_age_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>1</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>337</td>\n", "      <td>SP</td>\n", "      <td>osasco</td>\n", "      <td>89.8000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>337</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>1</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>458</td>\n", "      <td>MG</td>\n", "      <td>itapecerica</td>\n", "      <td>54.9000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>458</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>1</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>596</td>\n", "      <td>ES</td>\n", "      <td>nova venecia</td>\n", "      <td>179.9900</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>596</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>1</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>427</td>\n", "      <td>MG</td>\n", "      <td>mendonca</td>\n", "      <td>149.9000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>427</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>1</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>198</td>\n", "      <td>SP</td>\n", "      <td>sao paulo</td>\n", "      <td>93.0000</td>\n", "      <td>Faible</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>198</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  frequency     first_order_date      last_order_date  recency customer_state customer_city  monetary value_segment  frequency_outlier  recency_outlier  monetary_outlier  customer_age_days customer_age_category\n", "0  00012a2ce6f8dcda20d059ce98491703          1  2017-11-14 16:08:26  2017-11-14 16:08:26      337             SP        osasco   89.8000        Faible              False            False             False                337     Ancien (180-365j)\n", "1  000161a058600d5901f007fab4c27140          1  2017-07-16 09:40:32  2017-07-16 09:40:32      458             MG   itapecerica   54.9000        Faible              False            False             False                458   Très ancien (>365j)\n", "2  0001fd6190edaaf884bcaf3d49edf079          1  2017-02-28 11:06:43  2017-02-28 11:06:43      596             ES  nova venecia  179.9900        Faible              False            False             False                596   Très ancien (>365j)\n", "3  0002414f95344307404f0ace7a26f1d5          1  2017-08-16 13:09:20  2017-08-16 13:09:20      427             MG      mendonca  149.9000        Faible              False            False             False                427   Très ancien (>365j)\n", "4  000379cdec625522490c315e70c7a9fb          1  2018-04-02 13:42:17  2018-04-02 13:42:17      198             SP     sao paulo   93.0000        Faible              False            False             False                198     Ancien (180-365j)"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Statistiques descriptives des métriques RFM :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frequency</th>\n", "      <th>recency</th>\n", "      <th>monetary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.0000</td>\n", "      <td>289.9002</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.0000</td>\n", "      <td>153.6673</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.0000</td>\n", "      <td>166.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.0000</td>\n", "      <td>271.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.0000</td>\n", "      <td>400.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.0000</td>\n", "      <td>772.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       frequency    recency   monetary\n", "count 99441.0000 99441.0000 99441.0000\n", "mean      1.0000   289.9002   137.3577\n", "std       0.0000   153.6673   209.8703\n", "min       1.0000     0.0000     0.8500\n", "25%       1.0000   166.0000    45.9900\n", "50%       1.0000   271.0000    86.9000\n", "75%       1.0000   400.0000   149.9000\n", "max       1.0000   772.0000 13440.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 Distribution des segments :\n"]}, {"data": {"text/plain": ["value_segment\n", "Faible    99431\n", "Moyen         9\n", "Elevé         1\n", "Name: count, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Vérifications d'usage et préparation pour RFM\n", "if 'df_clean' in locals():\n", "    # Vérification des colonnes RFM déjà calculées\n", "    rfm_columns = ['frequency', 'recency', 'monetary']\n", "    missing_rfm = [col for col in rfm_columns if col not in df_clean.columns]\n", "\n", "    if missing_rfm:\n", "        print(f\"⚠️ Métriques RFM manquantes : {missing_rfm}\")\n", "        print(f\"Colonnes disponibles : {list(df_clean.columns)}\")\n", "    else:\n", "        print(\"✅ Toutes les métriques RFM sont présentes\")\n", "\n", "        # Aperçu des données\n", "        print(f\"\\n📋 Aperçu des données :\")\n", "        display(df_clean.head())\n", "\n", "        # Statistiques descriptives des métriques RFM\n", "        print(f\"\\n📊 Statistiques descriptives des métriques RFM :\")\n", "        display(df_clean[rfm_columns].describe())\n", "\n", "        # Vérification des segments\n", "        if 'value_segment' in df_clean.columns:\n", "            print(f\"\\n🎯 Distribution des segments :\")\n", "            segment_dist = df_clean['value_segment'].value_counts()\n", "            display(segment_dist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Calcul des variables RFM\n", "\n", "### 2.1 Définition de la date de référence pour la récence"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 Date de référence pour la récence : 2018-10-18 17:30:18\n", "📊 Période d'analyse : du 2016-09-04 21:15:19 au 2018-10-17 17:30:18\n", "🕰️ Durée totale de la période : 772 jours (2.1 années)\n", "\n", "🔍 Vérification de la récence :\n", "- <PERSON><PERSON><PERSON> moyenne : 289.9 jours\n", "- R<PERSON><PERSON> médiane : 271.0 jours\n", "- Récence min : 0.0 jours\n", "- Récence max : 772.0 jours\n"]}], "source": ["# Définition de la date de référence pour calculer la récence\n", "if 'df_clean' in locals():\n", "    # Conversion des colonnes de dates en datetime\n", "    df_clean['first_order_date'] = pd.to_datetime(df_clean['first_order_date'])\n", "    df_clean['last_order_date'] = pd.to_datetime(df_clean['last_order_date'])\n", "\n", "    # Date de référence = dernière date d'achat + 1 jour\n", "    reference_date = df_clean['last_order_date'].max() + <PERSON><PERSON><PERSON>(days=1)\n", "    print(f\"📅 Date de référence pour la récence : {reference_date}\")\n", "    print(f\"📊 Période d'analyse : du {df_clean['first_order_date'].min()} au {df_clean['last_order_date'].max()}\")\n", "\n", "    # Calcul de la durée totale de la période\n", "    period_duration = (df_clean['last_order_date'].max() - df_clean['first_order_date'].min()).days\n", "    print(f\"🕰️ Durée totale de la période : {period_duration} jours ({period_duration/365:.1f} années)\")\n", "\n", "    # Vérification de la cohérence avec la récence déjà calculée\n", "    if 'recency' in df_clean.columns:\n", "        print(f\"\\n🔍 Vérification de la récence :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {df_clean['recency'].mean():.1f} jours\")\n", "        print(f\"- R<PERSON><PERSON> médiane : {df_clean['recency'].median():.1f} jours\")\n", "        print(f\"- Récence min : {df_clean['recency'].min():.1f} jours\")\n", "        print(f\"- Récence max : {df_clean['recency'].max():.1f} jours\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Agrégation par client : calcul R, F, M"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Vérification des métriques RFM existantes...\n", "✅ Les métriques RFM sont déjà calculées\n", "📊 Données RFM disponibles pour 99,441 clients\n", "\n", "📋 Aperçu des données RFM :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>337</td>\n", "      <td>1</td>\n", "      <td>89.8000</td>\n", "      <td>89.8000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>458</td>\n", "      <td>1</td>\n", "      <td>54.9000</td>\n", "      <td>54.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>596</td>\n", "      <td>1</td>\n", "      <td>179.9900</td>\n", "      <td>179.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>427</td>\n", "      <td>1</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>198</td>\n", "      <td>1</td>\n", "      <td>93.0000</td>\n", "      <td>93.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  recency  frequency  monetary  montant_moyen\n", "0  00012a2ce6f8dcda20d059ce98491703      337          1   89.8000        89.8000\n", "1  000161a058600d5901f007fab4c27140      458          1   54.9000        54.9000\n", "2  0001fd6190edaaf884bcaf3d49edf079      596          1  179.9900       179.9900\n", "3  0002414f95344307404f0ace7a26f1d5      427          1  149.9000       149.9000\n", "4  000379cdec625522490c315e70c7a9fb      198          1   93.0000        93.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Statistiques descriptives RFM :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>289.9002</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>153.6673</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>166.0000</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>271.0000</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>400.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>772.0000</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         recency  frequency   monetary  montant_moyen\n", "count 99441.0000 99441.0000 99441.0000     99441.0000\n", "mean    289.9002     1.0000   137.3577       137.3577\n", "std     153.6673     0.0000   209.8703       209.8703\n", "min       0.0000     1.0000     0.8500         0.8500\n", "25%     166.0000     1.0000    45.9900        45.9900\n", "50%     271.0000     1.0000    86.9000        86.9000\n", "75%     400.0000     1.0000   149.9000       149.9000\n", "max     772.0000     1.0000 13440.0000     13440.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Vérification de la cohérence :\n", "- <PERSON><PERSON><PERSON> moyenne : 289.9 jours\n", "- <PERSON><PERSON><PERSON> m<PERSON> : 1.0 commandes\n", "- <PERSON><PERSON> moyen : 137.36 €\n"]}], "source": ["# Calcul des métriques RFM par client\n", "if 'df_clean' in locals() and 'reference_date' in locals():\n", "    print(\"🔄 Vérification des métriques RFM existantes...\")\n", "\n", "    # Vérification des colonnes RFM existantes\n", "    rfm_columns = ['recency', 'frequency', 'monetary']\n", "    if all(col in df_clean.columns for col in rfm_columns):\n", "        print(\"✅ Les métriques RFM sont déjà calculées\")\n", "\n", "        # Création d'un DataFrame RFM à partir des métriques existantes\n", "        rfm_df = df_clean[['customer_id'] + rfm_columns].copy()\n", "\n", "        # Ajout des montants moyens si disponibles\n", "        if 'monetary' in df_clean.columns:\n", "            rfm_df['montant_moyen'] = df_clean['monetary'] / df_clean['frequency']\n", "\n", "        print(f\"📊 Données RFM disponibles pour {len(rfm_df):,} clients\")\n", "        print(f\"\\n📋 Aperçu des données RFM :\")\n", "        display(rfm_df.head())\n", "        print(f\"\\n📈 Statistiques descriptives RFM :\")\n", "        display(rfm_df.describe())\n", "\n", "        # Vérification de la cohérence des métriques\n", "        print(f\"\\n🔍 Vérification de la cohérence :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {rfm_df['recency'].mean():.1f} jours\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {rfm_df['frequency'].mean():.1f} commandes\")\n", "        print(f\"- <PERSON><PERSON> moyen : {rfm_df['monetary'].mean():.2f} €\")\n", "    else:\n", "        print(\"⚠️ Métriques RFM incomplètes. Colonnes manquantes :\")\n", "        missing_cols = [col for col in rfm_columns if col not in df_clean.columns]\n", "        print(f\"- {missing_cols}\")\n", "        print(f\"Colonnes disponibles : {list(df_clean.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Construction et validation du DataFrame RFM final"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Visualisation des distributions RFM...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/y_/cgv899p56slcpfy0rh4wrfkc0000gn/T/ipykernel_62499/471334389.py:49: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  axes[1,1].boxplot([rfm_numeric[col].dropna() for col in rfm_numeric.columns if col != 'customer_id'],\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Figure exportée : reports/figures/2_01_rfm_distributions.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualisation des distributions RFM\n", "if 'rfm_df' in locals():\n", "    print(\"📊 Visualisation des distributions RFM...\")\n", "\n", "    # D<PERSON>terminer les noms de colonnes selon la source (utilitaire ou manuel)\n", "    if 'recence' in rfm_df.columns:\n", "        # Version manuelle\n", "        recency_col, frequency_col = 'recence', 'frequence'\n", "        monetary_total_col, monetary_avg_col = 'montant_total', 'montant_moyen'\n", "    else:\n", "        # Version utilitaire\n", "        recency_col, frequency_col = 'recency', 'frequency'\n", "        monetary_total_col, monetary_avg_col = 'monetary', 'monetary'\n", "\n", "    # C<PERSON>ation de la figure\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle('Distribution des Variables RFM', fontsize=16, fontweight='bold')\n", "\n", "    # Récence\n", "    axes[0,0].hist(rfm_df[recency_col], bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0,0].set_title('Distribution de la Récence (jours)')\n", "    axes[0,0].set_xlabel('Jours depuis dernier achat')\n", "    axes[0,0].set_ylabel('Nombre de clients')\n", "    axes[0,0].grid(True, alpha=0.3)\n", "\n", "    # Fréquence\n", "    axes[0,1].hist(rfm_df[frequency_col], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')\n", "    axes[0,1].set_title('Distribution de la Fréquence')\n", "    axes[0,1].set_xlabel('Nombre d\\'achats')\n", "    axes[0,1].set_ylabel('Nombre de clients')\n", "    axes[0,1].grid(True, alpha=0.3)\n", "\n", "    # Montant (utiliser la colonne appropriée)\n", "    if monetary_total_col in rfm_df.columns:\n", "        monetary_col = monetary_total_col\n", "        title_suffix = 'Total'\n", "    else:\n", "        monetary_col = monetary_avg_col\n", "        title_suffix = 'Moyen'\n", "\n", "    axes[1,0].hist(rfm_df[monetary_col], bins=50, alpha=0.7, color='salmon', edgecolor='black')\n", "    axes[1,0].set_title(f'Distribution du Montant {title_suffix}')\n", "    axes[1,0].set_xlabel('Montant (€)')\n", "    axes[1,0].set_ylabel('Nombre de clients')\n", "    axes[1,0].grid(True, alpha=0.3)\n", "\n", "    # Boxplot des variables RFM pour détecter les outliers\n", "    rfm_numeric = rfm_df.select_dtypes(include=[np.number])\n", "    axes[1,1].boxplot([rfm_numeric[col].dropna() for col in rfm_numeric.columns if col != 'customer_id'],\n", "                      labels=[col for col in rfm_numeric.columns if col != 'customer_id'])\n", "    axes[1,1].set_title('Boxplots des Variables RFM')\n", "    axes[1,1].set_ylabel('Valeurs (normalisées)')\n", "    axes[1,1].tick_params(axis='x', rotation=45)\n", "    axes[1,1].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les règles du projet\n", "    export_figure(fig, notebook_name=\"2\", export_number=1, base_name=\"rfm_distributions\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Enrichissement comportemental\n", "\n", "### 3.1 Calcul de l'ancienneté client"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul des features temporelles...\n", "🔄 Calcul des features temporelles...\n", "📊 Features temporelles calculées pour 99,441 clients\n", "\n", "📈 Statistiques d'ancienneté :\n", "- Ancienneté moyenne : 0.0 jours\n", "- Ancienneté médiane : 0.0 jours\n", "- Ancienneté max : 0.0 jours\n", "\n", "📊 Distribution des catégories d'ancienneté :\n", "- Ancien (180-365j): 41161 clients (41.4%)\n", "- Très ancien (>365j): 30096 clients (30.3%)\n", "- Etabli (90-180j): 18655 clients (18.8%)\n", "- <PERSON><PERSON><PERSON> (30-90j): 9521 clients (9.6%)\n", "- Nouveau (<30j): 8 clients (0.0%)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>first_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "      <th>customer_age_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>2017-11-14 16:08:26</td>\n", "      <td>0</td>\n", "      <td>338</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>2017-07-16 09:40:32</td>\n", "      <td>0</td>\n", "      <td>459</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>2017-02-28 11:06:43</td>\n", "      <td>0</td>\n", "      <td>597</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>2017-08-16 13:09:20</td>\n", "      <td>0</td>\n", "      <td>428</td>\n", "      <td>Très ancien (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>2018-04-02 13:42:17</td>\n", "      <td>0</td>\n", "      <td>199</td>\n", "      <td>Ancien (180-365j)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id    first_order_date     last_order_date  customer_lifespan_days  days_since_first_order customer_age_category\n", "0  00012a2ce6f8dcda20d059ce98491703 2017-11-14 16:08:26 2017-11-14 16:08:26                       0                     338     Ancien (180-365j)\n", "1  000161a058600d5901f007fab4c27140 2017-07-16 09:40:32 2017-07-16 09:40:32                       0                     459   Très ancien (>365j)\n", "2  0001fd6190edaaf884bcaf3d49edf079 2017-02-28 11:06:43 2017-02-28 11:06:43                       0                     597   Très ancien (>365j)\n", "3  0002414f95344307404f0ace7a26f1d5 2017-08-16 13:09:20 2017-08-16 13:09:20                       0                     428   Très ancien (>365j)\n", "4  000379cdec625522490c315e70c7a9fb 2018-04-02 13:42:17 2018-04-02 13:42:17                       0                     199     Ancien (180-365j)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calcul de l'ancienneté client et features temporelles\n", "if 'df_clean' in locals() and 'reference_date' in locals():\n", "    print(\"🔄 Calcul des features temporelles...\")\n", "\n", "    # Calcul manuel des features temporelles\n", "    print(\"🔄 Calcul des features temporelles...\")\n", "\n", "    # Calcul de l'ancienneté client\n", "    customer_lifetime = df_clean.groupby('customer_id').agg({\n", "        'first_order_date': 'min',\n", "        'last_order_date': 'max'\n", "    })\n", "\n", "    # Ancienneté = différence entre première et dernière commande\n", "    customer_lifetime['customer_lifespan_days'] = (\n", "        customer_lifetime['last_order_date'] - customer_lifetime['first_order_date']\n", "    ).dt.days\n", "\n", "    # Ancienneté depuis la première commande\n", "    customer_lifetime['days_since_first_order'] = (\n", "        reference_date - customer_lifetime['first_order_date']\n", "    ).dt.days\n", "\n", "    # Ajout de catégories d'ancienneté\n", "    customer_lifetime['customer_age_category'] = pd.cut(\n", "        customer_lifetime['days_since_first_order'],\n", "        bins=[0, 30, 90, 180, 365, float('inf')],\n", "        labels=['Nouveau (<30j)', '<PERSON><PERSON><PERSON> (30-90j)', '<PERSON><PERSON><PERSON><PERSON> (90-180j)',\n", "               'Ancien (180-365j)', 'Très ancien (>365j)']\n", "    )\n", "\n", "    temporal_features = customer_lifetime.reset_index()\n", "\n", "    print(f\"📊 Features temporelles calculées pour {len(temporal_features):,} clients\")\n", "    print(f\"\\n📈 Statistiques d'ancienneté :\")\n", "    print(f\"- Ancienneté moyenne : {temporal_features['customer_lifespan_days'].mean():.1f} jours\")\n", "    print(f\"- Ancienneté médiane : {temporal_features['customer_lifespan_days'].median():.1f} jours\")\n", "    print(f\"- Ancienneté max : {temporal_features['customer_lifespan_days'].max():.1f} jours\")\n", "\n", "    # Distribution des catégories d'ancienneté\n", "    print(f\"\\n📊 Distribution des catégories d'ancienneté :\")\n", "    age_distribution = temporal_features['customer_age_category'].value_counts()\n", "    for category, count in age_distribution.items():\n", "        pct = (count / len(temporal_features)) * 100\n", "        print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "    display(temporal_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 <PERSON><PERSON><PERSON> entre commandes"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul des features transactionnelles avancées...\n", "📊 Features transactionnelles calculées pour 99,441 clients\n", "\n", "📈 Statistiques des montants :\n", "- Mont<PERSON> moyen par commande : 137.36 €\n", "- Écart-type des montants : nan €\n", "- CV des montants : 0.00\n", "\n", "�� Statistiques de fréquence :\n", "- <PERSON><PERSON><PERSON> d'achat moyenne : inf commandes/jour\n", "- Nombre moyen de commandes : 1.0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>total_orders</th>\n", "      <th>total_amount</th>\n", "      <th>amount_std</th>\n", "      <th>recency</th>\n", "      <th>avg_order_value</th>\n", "      <th>amount_cv</th>\n", "      <th>purchase_frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>1</td>\n", "      <td>89.8000</td>\n", "      <td>NaN</td>\n", "      <td>337</td>\n", "      <td>89.8000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>1</td>\n", "      <td>54.9000</td>\n", "      <td>NaN</td>\n", "      <td>458</td>\n", "      <td>54.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>1</td>\n", "      <td>179.9900</td>\n", "      <td>NaN</td>\n", "      <td>596</td>\n", "      <td>179.9900</td>\n", "      <td>0.0000</td>\n", "      <td>0.0017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>1</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>427</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>1</td>\n", "      <td>93.0000</td>\n", "      <td>NaN</td>\n", "      <td>198</td>\n", "      <td>93.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0051</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  total_orders  total_amount  amount_std  recency  avg_order_value  amount_cv  purchase_frequency\n", "0  00012a2ce6f8dcda20d059ce98491703             1       89.8000         NaN      337          89.8000     0.0000              0.0030\n", "1  000161a058600d5901f007fab4c27140             1       54.9000         NaN      458          54.9000     0.0000              0.0022\n", "2  0001fd6190edaaf884bcaf3d49edf079             1      179.9900         NaN      596         179.9900     0.0000              0.0017\n", "3  0002414f95344307404f0ace7a26f1d5             1      149.9000         NaN      427         149.9000     0.0000              0.0023\n", "4  000379cdec625522490c315e70c7a9fb             1       93.0000         NaN      198          93.0000     0.0000              0.0051"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Vérification des valeurs aberrantes :\n", "- avg_order_value: 7915 valeurs aberrantes (8.0%)\n", "- amount_cv: 0 valeurs aberrantes (0.0%)\n", "- purchase_frequency: 9255 valeurs aberrantes (9.3%)\n"]}], "source": ["# Calcul des features transactionnelles avancées\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul des features transactionnelles avancées...\")\n", "\n", "    # Calcul des features à partir des métriques RFM existantes\n", "    transactional_features = df_clean.groupby('customer_id').agg({\n", "        'frequency': 'first',  # <PERSON><PERSON> de commandes\n", "        'monetary': ['first', 'std'],  # Montant total et écart-type\n", "        'recency': 'first'  # <PERSON><PERSON><PERSON> commande\n", "    }).reset_index()\n", "\n", "    # Renommage des colonnes\n", "    transactional_features.columns = ['customer_id', 'total_orders', 'total_amount', 'amount_std', 'recency']\n", "\n", "    # Calcul du montant moyen par commande\n", "    transactional_features['avg_order_value'] = (\n", "        transactional_features['total_amount'] / transactional_features['total_orders']\n", "    )\n", "\n", "    # Calcul du coefficient de variation des montants\n", "    transactional_features['amount_cv'] = (\n", "        transactional_features['amount_std'] / transactional_features['avg_order_value']\n", "    ).<PERSON>na(0)\n", "\n", "    # Cal<PERSON>l de la fréquence d'achat (commandes par jour)\n", "    transactional_features['purchase_frequency'] = (\n", "        transactional_features['total_orders'] / transactional_features['recency']\n", "    ).<PERSON>na(0)\n", "\n", "    print(f\"📊 Features transactionnelles calculées pour {len(transactional_features):,} clients\")\n", "\n", "    print(f\"\\n📈 Statistiques des montants :\")\n", "    print(f\"- <PERSON><PERSON> moyen par commande : {transactional_features['avg_order_value'].mean():.2f} €\")\n", "    print(f\"- Écart-type des montants : {transactional_features['amount_std'].mean():.2f} €\")\n", "    print(f\"- CV des montants : {transactional_features['amount_cv'].mean():.2f}\")\n", "\n", "    print(f\"\\n�� Statistiques de fréquence :\")\n", "    print(f\"- <PERSON><PERSON><PERSON> d'achat moyenne : {transactional_features['purchase_frequency'].mean():.3f} commandes/jour\")\n", "    print(f\"- Nombre moyen de commandes : {transactional_features['total_orders'].mean():.1f}\")\n", "\n", "    # Affichage des résultats\n", "    display(transactional_features.head())\n", "\n", "    # Vérification des valeurs aberrantes\n", "    print(f\"\\n🔍 Vérification des valeurs aberrantes :\")\n", "    for col in ['avg_order_value', 'amount_cv', 'purchase_frequency']:\n", "        q1 = transactional_features[col].quantile(0.25)\n", "        q3 = transactional_features[col].quantile(0.75)\n", "        iqr = q3 - q1\n", "        outliers = transactional_features[\n", "            (transactional_features[col] < q1 - 1.5 * iqr) |\n", "            (transactional_features[col] > q3 + 1.5 * iqr)\n", "        ]\n", "        print(f\"- {col}: {len(outliers)} valeurs aberrantes ({len(outliers)/len(transactional_features)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Nombre de catégories achetées et diversité"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul de la diversité des achats...\n", "📋 Colonnes disponibles :\n", "['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']\n", "\n", "📊 Calcul de la variabilité des montants...\n", "\n", "📈 Statistiques des montants :\n", "- Montant total moyen : 137.36 €\n", "- Mont<PERSON> moyen par commande : 137.36 €\n", "- Écart-type moyen : nan €\n", "- CV moyen : 0.000\n", "\n", "🔍 Vérification des valeurs aberrantes :\n", "- amount_std: 0 valeurs aberrantes (0.0%)\n", "- amount_cv: 0 valeurs aberrantes (0.0%)\n", "- amount_range: 0 valeurs aberrantes (0.0%)\n", "\n", "📊 Features calculées pour 99,441 clients\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>total_amount</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>total_orders</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_range</th>\n", "      <th>avg_order_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>89.8000</td>\n", "      <td>89.8000</td>\n", "      <td>NaN</td>\n", "      <td>89.8000</td>\n", "      <td>89.8000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>89.8000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>54.9000</td>\n", "      <td>54.9000</td>\n", "      <td>NaN</td>\n", "      <td>54.9000</td>\n", "      <td>54.9000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>54.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>179.9900</td>\n", "      <td>179.9900</td>\n", "      <td>NaN</td>\n", "      <td>179.9900</td>\n", "      <td>179.9900</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>179.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>93.0000</td>\n", "      <td>93.0000</td>\n", "      <td>NaN</td>\n", "      <td>93.0000</td>\n", "      <td>93.0000</td>\n", "      <td>1</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>93.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  total_amount  avg_amount  amount_std  min_amount  max_amount  total_orders  amount_cv  amount_range  avg_order_value\n", "0  00012a2ce6f8dcda20d059ce98491703       89.8000     89.8000         NaN     89.8000     89.8000             1     0.0000        0.0000          89.8000\n", "1  000161a058600d5901f007fab4c27140       54.9000     54.9000         NaN     54.9000     54.9000             1     0.0000        0.0000          54.9000\n", "2  0001fd6190edaaf884bcaf3d49edf079      179.9900    179.9900         NaN    179.9900    179.9900             1     0.0000        0.0000         179.9900\n", "3  0002414f95344307404f0ace7a26f1d5      149.9000    149.9000         NaN    149.9000    149.9000             1     0.0000        0.0000         149.9000\n", "4  000379cdec625522490c315e70c7a9fb       93.0000     93.0000         NaN     93.0000     93.0000             1     0.0000        0.0000          93.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Statistiques descriptives :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>total_amount</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>total_orders</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_range</th>\n", "      <th>avg_order_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       total_amount  avg_amount  amount_std  min_amount  max_amount  total_orders  amount_cv  amount_range  avg_order_value\n", "count    99441.0000  99441.0000      0.0000  99441.0000  99441.0000    99441.0000 99441.0000    99441.0000       99441.0000\n", "mean       137.3577    137.3577         NaN    137.3577    137.3577        1.0000     0.0000        0.0000         137.3577\n", "std        209.8703    209.8703         NaN    209.8703    209.8703        0.0000     0.0000        0.0000         209.8703\n", "min          0.8500      0.8500         NaN      0.8500      0.8500        1.0000     0.0000        0.0000           0.8500\n", "25%         45.9900     45.9900         NaN     45.9900     45.9900        1.0000     0.0000        0.0000          45.9900\n", "50%         86.9000     86.9000         NaN     86.9000     86.9000        1.0000     0.0000        0.0000          86.9000\n", "75%        149.9000    149.9000         NaN    149.9000    149.9000        1.0000     0.0000        0.0000         149.9000\n", "max      13440.0000  13440.0000         NaN  13440.0000  13440.0000        1.0000     0.0000        0.0000       13440.0000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calcul de la diversité des achats et variabilité\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul de la diversité des achats...\")\n", "\n", "    # Vérification des colonnes disponibles\n", "    print(\"📋 Colonnes disponibles :\")\n", "    print(df_clean.columns.tolist())\n", "\n", "    # Création du DataFrame de base avec customer_id\n", "    diversity_features = df_clean[['customer_id']].drop_duplicates().reset_index(drop=True)\n", "\n", "    # Calcul de la variabilité des montants d'achat\n", "    print(\"\\n📊 Calcul de la variabilité des montants...\")\n", "\n", "    # Vérification de la présence des colonnes nécessaires\n", "    if 'monetary' in df_clean.columns and 'frequency' in df_clean.columns:\n", "        # Calcul des statistiques de base\n", "        order_stats = df_clean.groupby('customer_id').agg({\n", "            'monetary': ['sum', 'mean', 'std', 'min', 'max'],\n", "            'frequency': 'first'\n", "        })\n", "\n", "        # Renommage des colonnes\n", "        order_stats.columns = ['total_amount', 'avg_amount', 'amount_std', 'min_amount', 'max_amount', 'total_orders']\n", "        order_stats = order_stats.reset_index()\n", "\n", "        # Calcul des métriques dérivées\n", "        order_stats['amount_cv'] = (order_stats['amount_std'] / order_stats['avg_amount']).fillna(0)\n", "        order_stats['amount_range'] = order_stats['max_amount'] - order_stats['min_amount']\n", "        order_stats['avg_order_value'] = order_stats['total_amount'] / order_stats['total_orders']\n", "\n", "        # Fusion avec le DataFrame de base\n", "        diversity_features = diversity_features.merge(order_stats, on='customer_id', how='left')\n", "\n", "        # Affichage des statistiques\n", "        print(f\"\\n📈 Statistiques des montants :\")\n", "        print(f\"- Montant total moyen : {order_stats['total_amount'].mean():.2f} €\")\n", "        print(f\"- <PERSON><PERSON> moyen par commande : {order_stats['avg_order_value'].mean():.2f} €\")\n", "        print(f\"- Écart-type moyen : {order_stats['amount_std'].mean():.2f} €\")\n", "        print(f\"- CV moyen : {order_stats['amount_cv'].mean():.3f}\")\n", "\n", "        # Vérification des valeurs aberrantes\n", "        print(f\"\\n🔍 Vérification des valeurs aberrantes :\")\n", "        for col in ['amount_std', 'amount_cv', 'amount_range']:\n", "            q1 = order_stats[col].quantile(0.25)\n", "            q3 = order_stats[col].quantile(0.75)\n", "            iqr = q3 - q1\n", "            outliers = order_stats[\n", "                (order_stats[col] < q1 - 1.5 * iqr) |\n", "                (order_stats[col] > q3 + 1.5 * iqr)\n", "            ]\n", "            print(f\"- {col}: {len(outliers)} valeurs aberrantes ({len(outliers)/len(order_stats)*100:.1f}%)\")\n", "\n", "        # Affichage des résultats\n", "        print(f\"\\n📊 Features calculées pour {len(diversity_features):,} clients\")\n", "        display(diversity_features.head())\n", "\n", "        # Statistiques descriptives\n", "        print(\"\\n📈 Statistiques descriptives :\")\n", "        display(diversity_features.describe())\n", "    else:\n", "        print(\"⚠️ Colonnes 'monetary' ou 'frequency' manquantes\")\n", "        print(\"Colonnes disponibles :\", df_clean.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 <PERSON><PERSON> moyen, écart-type et ratios"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Consolidation de toutes les features...\n", "+ 5 features tempo<PERSON><PERSON>\n", "+ 7 features <PERSON><PERSON><PERSON> a<PERSON>\n", "+ 9 features de diversité ajoutées\n", "\n", "🧹 Nettoyage des colonnes en double...\n", "\n", "📊 Dataset enrichi final : (99441, 28)\n", "\n", "📋 Organisation des colonnes :\n", "- Colonnes de base : ['customer_id']\n", "- Métriques RFM : ['recency', 'frequency', 'monetary']\n", "- Features temporelles : ['first_order_date', 'last_order_date', 'customer_lifespan_days', 'days_since_first_order', 'customer_age_category', 'recency_days']\n", "- Features monétaires : ['monetary', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean']\n", "- Features de fréquence : ['frequency', 'total_orders', 'purchase_frequency', 'order_count']\n", "- Autres features : ['montant_moyen']\n", "\n", "📈 Statistiques descriptives :\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/pandas/core/nanops.py:1016: RuntimeWarning: invalid value encountered in subtract\n", "  sqr = _ensure_numeric((avg - values) ** 2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>first_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "      <th>recency_days</th>\n", "      <th>monetary</th>\n", "      <th>total_amount</th>\n", "      <th>amount_std</th>\n", "      <th>avg_order_value</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_total</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std_dev</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>amount_cv_coef</th>\n", "      <th>amount_range</th>\n", "      <th>order_value_mean</th>\n", "      <th>frequency</th>\n", "      <th>total_orders</th>\n", "      <th>purchase_frequency</th>\n", "      <th>order_count</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441</td>\n", "      <td>99441</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>289.9002</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "      <td>2017-12-31 08:43:12.776581120</td>\n", "      <td>2017-12-31 08:43:12.776581120</td>\n", "      <td>0.0000</td>\n", "      <td>290.9002</td>\n", "      <td>289.9002</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "      <td>2016-09-04 21:15:19</td>\n", "      <td>2016-09-04 21:15:19</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0013</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>166.0000</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "      <td>2017-09-12 14:46:19</td>\n", "      <td>2017-09-12 14:46:19</td>\n", "      <td>0.0000</td>\n", "      <td>167.0000</td>\n", "      <td>166.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0025</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>271.0000</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "      <td>2018-01-18 23:04:36</td>\n", "      <td>2018-01-18 23:04:36</td>\n", "      <td>0.0000</td>\n", "      <td>272.0000</td>\n", "      <td>271.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0037</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>400.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "      <td>2018-05-04 15:42:16</td>\n", "      <td>2018-05-04 15:42:16</td>\n", "      <td>0.0000</td>\n", "      <td>401.0000</td>\n", "      <td>400.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0060</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>772.0000</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>2018-10-17 17:30:18</td>\n", "      <td>2018-10-17 17:30:18</td>\n", "      <td>0.0000</td>\n", "      <td>773.0000</td>\n", "      <td>772.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>153.6673</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0000</td>\n", "      <td>153.6673</td>\n", "      <td>153.6673</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>NaN</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         recency  frequency   monetary               first_order_date                last_order_date  customer_lifespan_days  days_since_first_order  recency_days   monetary  total_amount  amount_std  avg_order_value  amount_cv  amount_total  avg_amount  amount_std_dev  min_amount  max_amount  amount_cv_coef  amount_range  order_value_mean  frequency  total_orders  purchase_frequency  order_count  montant_moyen\n", "count 99441.0000 99441.0000 99441.0000                          99441                          99441              99441.0000              99441.0000    99441.0000 99441.0000    99441.0000      0.0000       99441.0000 99441.0000    99441.0000  99441.0000          0.0000  99441.0000  99441.0000      99441.0000    99441.0000        99441.0000 99441.0000    99441.0000          99441.0000   99441.0000     99441.0000\n", "mean    289.9002     1.0000   137.3577  2017-12-31 08:43:12.776581120  2017-12-31 08:43:12.776581120                  0.0000                290.9002      289.9002   137.3577      137.3577         NaN         137.3577     0.0000      137.3577    137.3577             NaN    137.3577    137.3577          0.0000        0.0000          137.3577     1.0000        1.0000                 inf       1.0000       137.3577\n", "min       0.0000     1.0000     0.8500            2016-09-04 21:15:19            2016-09-04 21:15:19                  0.0000                  1.0000        0.0000     0.8500        0.8500         NaN           0.8500     0.0000        0.8500      0.8500             NaN      0.8500      0.8500          0.0000        0.0000            0.8500     1.0000        1.0000              0.0013       1.0000         0.8500\n", "25%     166.0000     1.0000    45.9900            2017-09-12 14:46:19            2017-09-12 14:46:19                  0.0000                167.0000      166.0000    45.9900       45.9900         NaN          45.9900     0.0000       45.9900     45.9900             NaN     45.9900     45.9900          0.0000        0.0000           45.9900     1.0000        1.0000              0.0025       1.0000        45.9900\n", "50%     271.0000     1.0000    86.9000            2018-01-18 23:04:36            2018-01-18 23:04:36                  0.0000                272.0000      271.0000    86.9000       86.9000         NaN          86.9000     0.0000       86.9000     86.9000             NaN     86.9000     86.9000          0.0000        0.0000           86.9000     1.0000        1.0000              0.0037       1.0000        86.9000\n", "75%     400.0000     1.0000   149.9000            2018-05-04 15:42:16            2018-05-04 15:42:16                  0.0000                401.0000      400.0000   149.9000      149.9000         NaN         149.9000     0.0000      149.9000    149.9000             NaN    149.9000    149.9000          0.0000        0.0000          149.9000     1.0000        1.0000              0.0060       1.0000       149.9000\n", "max     772.0000     1.0000 13440.0000            2018-10-17 17:30:18            2018-10-17 17:30:18                  0.0000                773.0000      772.0000 13440.0000    13440.0000         NaN       13440.0000     0.0000    13440.0000  13440.0000             NaN  13440.0000  13440.0000          0.0000        0.0000        13440.0000     1.0000        1.0000                 inf       1.0000     13440.0000\n", "std     153.6673     0.0000   209.8703                            NaN                            NaN                  0.0000                153.6673      153.6673   209.8703      209.8703         NaN         209.8703     0.0000      209.8703    209.8703             NaN    209.8703    209.8703          0.0000        0.0000          209.8703     0.0000        0.0000                 NaN       0.0000       209.8703"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "⚠️ Valeurs manquantes par colonne :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n"]}], "source": ["# Consolidation de toutes les features\n", "if 'rfm_df' in locals():\n", "    print(\"🔄 Consolidation de toutes les features...\")\n", "\n", "    # Consolidation manuelle\n", "    rfm_enriched = rfm_df.copy()\n", "\n", "    # Fusion avec les features temporelles si disponibles\n", "    if 'temporal_features' in locals() and not temporal_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(temporal_features, on='customer_id', how='left')\n", "        print(f\"+ {len(temporal_features.columns)-1} features temporelles a<PERSON>\")\n", "\n", "    # Fusion avec les features transactionnelles si disponibles\n", "    if 'transactional_features' in locals() and not transactional_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(transactional_features, on='customer_id', how='left')\n", "        print(f\"+ {len(transactional_features.columns)-1} features transactionnelles ajoutées\")\n", "\n", "    # Fusion avec les features de diversité si disponibles\n", "    if 'diversity_features' in locals() and not diversity_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(diversity_features, on='customer_id', how='left')\n", "        print(f\"+ {len(diversity_features.columns)-1} features de diversité ajoutées\")\n", "\n", "    # Nettoyage des colonnes en double\n", "    print(\"\\n🧹 Nettoyage des colonnes en double...\")\n", "\n", "    # Dictionnaire de mapping pour les colonnes en double\n", "    column_mapping = {\n", "        'recency_x': 'recency',\n", "        'recency_y': 'recency_days',\n", "        'total_orders_x': 'total_orders',\n", "        'total_orders_y': 'order_count',\n", "        'total_amount_x': 'total_amount',\n", "        'total_amount_y': 'amount_total',\n", "        'amount_std_x': 'amount_std',\n", "        'amount_std_y': 'amount_std_dev',\n", "        'amount_cv_x': 'amount_cv',\n", "        'amount_cv_y': 'amount_cv_coef',\n", "        'avg_order_value_x': 'avg_order_value',\n", "        'avg_order_value_y': 'order_value_mean'\n", "    }\n", "\n", "    # Renommage des colonnes\n", "    rfm_enriched = rfm_enriched.rename(columns=column_mapping)\n", "\n", "    # Suppression des colonnes en double\n", "    duplicate_cols = [col for col in rfm_enriched.columns if col.endswith(('_x', '_y'))]\n", "    rfm_enriched = rfm_enriched.drop(columns=duplicate_cols)\n", "\n", "    # Organisation des colonnes par catégorie\n", "    base_cols = ['customer_id']\n", "    rfm_cols = ['recency', 'frequency', 'monetary']\n", "    temporal_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['date', 'days', 'age', 'lifespan'])]\n", "    monetary_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['amount', 'monetary', 'value'])]\n", "    frequency_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['frequency', 'orders', 'count'])]\n", "    other_cols = [col for col in rfm_enriched.columns if col not in base_cols + rfm_cols + temporal_cols + monetary_cols + frequency_cols]\n", "\n", "    # Réorganisation des colonnes\n", "    rfm_enriched = rfm_enriched[base_cols + rfm_cols + temporal_cols + monetary_cols + frequency_cols + other_cols]\n", "\n", "    print(f\"\\n📊 Dataset enrichi final : {rfm_enriched.shape}\")\n", "    print(\"\\n📋 Organisation des colonnes :\")\n", "    print(f\"- Colonnes de base : {base_cols}\")\n", "    print(f\"- Métriques RFM : {rfm_cols}\")\n", "    print(f\"- Features temporelles : {temporal_cols}\")\n", "    print(f\"- Features monétaires : {monetary_cols}\")\n", "    print(f\"- Features de fréquence : {frequency_cols}\")\n", "    print(f\"- Autres features : {other_cols}\")\n", "\n", "    # Affichage des statistiques descriptives\n", "    print(\"\\n📈 Statistiques descriptives :\")\n", "    display(rfm_enriched.describe())\n", "\n", "    # Vérification des valeurs manquantes\n", "    missing_values = rfm_enriched.isnull().sum()\n", "    if missing_values.any():\n", "        print(\"\\n⚠️ Valeurs manquantes par colonne :\")\n", "        print(missing_values[missing_values > 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.5 Taux de retour et indicateurs avancés (si applicable)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Calcul des indicateurs avancés...\n", "📋 Colonnes disponibles :\n", "['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment', 'frequency_outlier', 'recency_outlier', 'monetary_outlier', 'customer_age_days', 'customer_age_category']\n", "\n", "📅 Calcul de la saisonnalité des achats...\n", "\n", "🔍 Vérification des données :\n", "R<PERSON><PERSON> moyenne : 289.9 jours\n", "Récence médiane : 271.0 jours\n", "Récence max : 772.0 jours\n", "\n", "📊 Statistiques de période d'activité :\n", "- <PERSON><PERSON><PERSON><PERSON> moy<PERSON> : 289.9 jours\n", "- Période médiane : 271.0 jours\n", "- Période max : 772.0 jours\n", "\n", "📊 Distribution des catégories d'activité :\n", "- Long (180-365j): 41118 clients (41.3%)\n", "- Très long (>365j): 29898 clients (30.1%)\n", "- Moyen (90-180j): 18636 clients (18.7%)\n", "- Court (30-90j): 9780 clients (9.8%)\n", "- Très court (<30j): 7 clients (0.0%)\n", "\n", "📊 Statistiques de fréquence d'achat :\n", "- <PERSON><PERSON><PERSON> moyenne : 0.005 achats/jour\n", "- <PERSON><PERSON><PERSON> médiane : 0.004 achats/jour\n", "\n", "📊 Features avancées calculées pour 99,441 clients\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>activity_period_days</th>\n", "      <th>activity_period_months</th>\n", "      <th>activity_period_quarters</th>\n", "      <th>first_order_month</th>\n", "      <th>first_order_quarter</th>\n", "      <th>first_order_weekday</th>\n", "      <th>purchase_frequency</th>\n", "      <th>activity_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>337</td>\n", "      <td>11.0710</td>\n", "      <td>3.6907</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0.0030</td>\n", "      <td>Long (180-365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>458</td>\n", "      <td>15.0460</td>\n", "      <td>5.0159</td>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>0.0022</td>\n", "      <td>Tr<PERSON> long (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>596</td>\n", "      <td>19.5795</td>\n", "      <td>6.5272</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.0017</td>\n", "      <td>Tr<PERSON> long (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>427</td>\n", "      <td>14.0276</td>\n", "      <td>4.6764</td>\n", "      <td>8</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.0023</td>\n", "      <td>Tr<PERSON> long (&gt;365j)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>198</td>\n", "      <td>6.5046</td>\n", "      <td>2.1684</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0051</td>\n", "      <td>Long (180-365j)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id  activity_period_days  activity_period_months  activity_period_quarters  first_order_month  first_order_quarter  first_order_weekday  purchase_frequency  activity_category\n", "0  00012a2ce6f8dcda20d059ce98491703                   337                 11.0710                    3.6907                 11                    4                    1              0.0030    Long (180-365j)\n", "1  000161a058600d5901f007fab4c27140                   458                 15.0460                    5.0159                  7                    3                    6              0.0022  Très long (>365j)\n", "2  0001fd6190edaaf884bcaf3d49edf079                   596                 19.5795                    6.5272                  2                    1                    1              0.0017  Très long (>365j)\n", "3  0002414f95344307404f0ace7a26f1d5                   427                 14.0276                    4.6764                  8                    3                    2              0.0023  Très long (>365j)\n", "4  000379cdec625522490c315e70c7a9fb                   198                  6.5046                    2.1684                  4                    2                    0              0.0051    Long (180-365j)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calcul d'indicateurs avancés : taux de retour et saisonnalité\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul des indicateurs avancés...\")\n", "\n", "    # Vérification des colonnes disponibles\n", "    print(\"📋 Colonnes disponibles :\")\n", "    print(df_clean.columns.tolist())\n", "\n", "    advanced_features = pd.DataFrame()\n", "\n", "    # Saisonnalité des achats\n", "    print(\"\\n📅 Calcul de la saisonnalité des achats...\")\n", "\n", "    # Utilisation des dates disponibles\n", "    if 'first_order_date' in df_clean.columns and 'recency' in df_clean.columns:\n", "        seasonal_patterns = df_clean.copy()\n", "\n", "        # Vérification des données\n", "        print(\"\\n🔍 Vérification des données :\")\n", "        print(f\"Récence moyenne : {seasonal_patterns['recency'].mean():.1f} jours\")\n", "        print(f\"Récence médiane : {seasonal_patterns['recency'].median():.1f} jours\")\n", "        print(f\"Récence max : {seasonal_patterns['recency'].max():.1f} jours\")\n", "\n", "        # Calcul des indicateurs temporels\n", "        temporal_features = pd.DataFrame()\n", "        temporal_features['customer_id'] = seasonal_patterns['customer_id']\n", "\n", "        # Période d'activité basée sur la récence\n", "        temporal_features['activity_period_days'] = seasonal_patterns['recency']\n", "        temporal_features['activity_period_months'] = temporal_features['activity_period_days'] / 30.44\n", "        temporal_features['activity_period_quarters'] = temporal_features['activity_period_days'] / 91.31\n", "\n", "        # Saisonnalité\n", "        temporal_features['first_order_month'] = seasonal_patterns['first_order_date'].dt.month\n", "        temporal_features['first_order_quarter'] = seasonal_patterns['first_order_date'].dt.quarter\n", "        temporal_features['first_order_weekday'] = seasonal_patterns['first_order_date'].dt.dayofweek\n", "\n", "        # <PERSON><PERSON><PERSON> d'achat\n", "        if 'frequency' in seasonal_patterns.columns:\n", "            temporal_features['purchase_frequency'] = (\n", "                seasonal_patterns['frequency'] / temporal_features['activity_period_days']\n", "            ).<PERSON>na(0)\n", "            temporal_features['purchase_frequency'] = temporal_features['purchase_frequency'].replace([np.inf, -np.inf], 0)\n", "\n", "        # Catégorisation de la période d'activité\n", "        temporal_features['activity_category'] = pd.cut(\n", "            temporal_features['activity_period_days'],\n", "            bins=[0, 30, 90, 180, 365, float('inf')],\n", "            labels=['Très court (<30j)', 'Court (30-90j)', '<PERSON><PERSON><PERSON> (90-180j)',\n", "                   'Long (180-365j)', 'Tr<PERSON> long (>365j)']\n", "        )\n", "\n", "        advanced_features = temporal_features\n", "\n", "        # Affichage des statistiques\n", "        print(\"\\n📊 Statistiques de période d'activité :\")\n", "        print(f\"- <PERSON><PERSON><PERSON><PERSON> moyenne : {temporal_features['activity_period_days'].mean():.1f} jours\")\n", "        print(f\"- <PERSON><PERSON><PERSON><PERSON> médiane : {temporal_features['activity_period_days'].median():.1f} jours\")\n", "        print(f\"- Période max : {temporal_features['activity_period_days'].max():.1f} jours\")\n", "\n", "        print(\"\\n📊 Distribution des catégories d'activité :\")\n", "        activity_dist = temporal_features['activity_category'].value_counts()\n", "        for category, count in activity_dist.items():\n", "            pct = (count / len(temporal_features)) * 100\n", "            print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "        if 'purchase_frequency' in temporal_features.columns:\n", "            print(\"\\n📊 Statistiques de fréquence d'achat :\")\n", "            print(f\"- <PERSON><PERSON><PERSON> moyenne : {temporal_features['purchase_frequency'].mean():.3f} achats/jour\")\n", "            print(f\"- <PERSON><PERSON><PERSON> médiane : {temporal_features['purchase_frequency'].median():.3f} achats/jour\")\n", "\n", "    else:\n", "        print(\"⚠️ Colonnes nécessaires manquantes pour l'analyse temporelle\")\n", "\n", "    print(f\"\\n📊 Features avancées calculées pour {len(advanced_features):,} clients\")\n", "    display(advanced_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Normalisation des variables\n", "\n", "### 4.1 Sélection des variables pour clustering"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Sélection des variables pour clustering...\n", "📊 Variables sélectionnées pour clustering : 24\n", "📋 Variables : ['recency', 'frequency', 'monetary', 'customer_lifespan_days', 'days_since_first_order', 'recency_days', 'monetary', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'frequency', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen']\n", "\n", "🔍 Valeurs manquantes par variable :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n", "\n", "📈 Statistiques descriptives :\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/pandas/core/nanops.py:1016: RuntimeWarning: invalid value encountered in subtract\n", "  sqr = _ensure_numeric((avg - values) ** 2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>frequency</th>\n", "      <th>monetary</th>\n", "      <th>monetary</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "      <th>recency_days</th>\n", "      <th>monetary</th>\n", "      <th>monetary</th>\n", "      <th>total_amount</th>\n", "      <th>amount_std</th>\n", "      <th>avg_order_value</th>\n", "      <th>amount_cv</th>\n", "      <th>amount_total</th>\n", "      <th>avg_amount</th>\n", "      <th>amount_std_dev</th>\n", "      <th>min_amount</th>\n", "      <th>max_amount</th>\n", "      <th>amount_cv_coef</th>\n", "      <th>amount_range</th>\n", "      <th>order_value_mean</th>\n", "      <th>frequency</th>\n", "      <th>frequency</th>\n", "      <th>total_orders</th>\n", "      <th>purchase_frequency</th>\n", "      <th>order_count</th>\n", "      <th>montant_moyen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>0.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "      <td>99441.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>289.9002</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>290.9002</td>\n", "      <td>289.9002</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>NaN</td>\n", "      <td>137.3577</td>\n", "      <td>137.3577</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>137.3577</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>137.3577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>153.6673</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>153.6673</td>\n", "      <td>153.6673</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>NaN</td>\n", "      <td>209.8703</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>NaN</td>\n", "      <td>0.0000</td>\n", "      <td>209.8703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>NaN</td>\n", "      <td>0.8500</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0013</td>\n", "      <td>1.0000</td>\n", "      <td>0.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>166.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>167.0000</td>\n", "      <td>166.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>NaN</td>\n", "      <td>45.9900</td>\n", "      <td>45.9900</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>45.9900</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0025</td>\n", "      <td>1.0000</td>\n", "      <td>45.9900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>271.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>272.0000</td>\n", "      <td>271.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>NaN</td>\n", "      <td>86.9000</td>\n", "      <td>86.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>86.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0037</td>\n", "      <td>1.0000</td>\n", "      <td>86.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>400.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>401.0000</td>\n", "      <td>400.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>NaN</td>\n", "      <td>149.9000</td>\n", "      <td>149.9000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>149.9000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0060</td>\n", "      <td>1.0000</td>\n", "      <td>149.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>772.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>773.0000</td>\n", "      <td>772.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>NaN</td>\n", "      <td>13440.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>inf</td>\n", "      <td>1.0000</td>\n", "      <td>13440.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         recency  frequency  frequency   monetary   monetary  customer_lifespan_days  days_since_first_order  recency_days   monetary   monetary  total_amount  amount_std  avg_order_value  amount_cv  amount_total  avg_amount  amount_std_dev  min_amount  max_amount  amount_cv_coef  amount_range  order_value_mean  frequency  frequency  total_orders  purchase_frequency  order_count  montant_moyen\n", "count 99441.0000 99441.0000 99441.0000 99441.0000 99441.0000              99441.0000              99441.0000    99441.0000 99441.0000 99441.0000    99441.0000      0.0000       99441.0000 99441.0000    99441.0000  99441.0000          0.0000  99441.0000  99441.0000      99441.0000    99441.0000        99441.0000 99441.0000 99441.0000    99441.0000          99441.0000   99441.0000     99441.0000\n", "mean    289.9002     1.0000     1.0000   137.3577   137.3577                  0.0000                290.9002      289.9002   137.3577   137.3577      137.3577         NaN         137.3577     0.0000      137.3577    137.3577             NaN    137.3577    137.3577          0.0000        0.0000          137.3577     1.0000     1.0000        1.0000                 inf       1.0000       137.3577\n", "std     153.6673     0.0000     0.0000   209.8703   209.8703                  0.0000                153.6673      153.6673   209.8703   209.8703      209.8703         NaN         209.8703     0.0000      209.8703    209.8703             NaN    209.8703    209.8703          0.0000        0.0000          209.8703     0.0000     0.0000        0.0000                 NaN       0.0000       209.8703\n", "min       0.0000     1.0000     1.0000     0.8500     0.8500                  0.0000                  1.0000        0.0000     0.8500     0.8500        0.8500         NaN           0.8500     0.0000        0.8500      0.8500             NaN      0.8500      0.8500          0.0000        0.0000            0.8500     1.0000     1.0000        1.0000              0.0013       1.0000         0.8500\n", "25%     166.0000     1.0000     1.0000    45.9900    45.9900                  0.0000                167.0000      166.0000    45.9900    45.9900       45.9900         NaN          45.9900     0.0000       45.9900     45.9900             NaN     45.9900     45.9900          0.0000        0.0000           45.9900     1.0000     1.0000        1.0000              0.0025       1.0000        45.9900\n", "50%     271.0000     1.0000     1.0000    86.9000    86.9000                  0.0000                272.0000      271.0000    86.9000    86.9000       86.9000         NaN          86.9000     0.0000       86.9000     86.9000             NaN     86.9000     86.9000          0.0000        0.0000           86.9000     1.0000     1.0000        1.0000              0.0037       1.0000        86.9000\n", "75%     400.0000     1.0000     1.0000   149.9000   149.9000                  0.0000                401.0000      400.0000   149.9000   149.9000      149.9000         NaN         149.9000     0.0000      149.9000    149.9000             NaN    149.9000    149.9000          0.0000        0.0000          149.9000     1.0000     1.0000        1.0000              0.0060       1.0000       149.9000\n", "max     772.0000     1.0000     1.0000 13440.0000 13440.0000                  0.0000                773.0000      772.0000 13440.0000 13440.0000    13440.0000         NaN       13440.0000     0.0000    13440.0000  13440.0000             NaN  13440.0000  13440.0000          0.0000        0.0000        13440.0000     1.0000     1.0000        1.0000                 inf       1.0000     13440.0000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Sélection des variables pour le clustering\n", "if 'rfm_enriched' in locals():\n", "    print(\"🔄 Sélection des variables pour clustering...\")\n", "\n", "    # Exclusion de customer_id et autres identifiants\n", "    exclude_cols = ['customer_id', 'first_order', 'last_order']\n", "\n", "    # Sélection automatique des colonnes numériques\n", "    numeric_cols = rfm_enriched.select_dtypes(include=[np.number]).columns.tolist()\n", "    clustering_features = [col for col in numeric_cols if col not in exclude_cols]\n", "\n", "    print(f\"📊 Variables sélectionnées pour clustering : {len(clustering_features)}\")\n", "    print(f\"📋 Variables : {clustering_features}\")\n", "\n", "    # Création du dataset pour clustering\n", "    X = rfm_enriched[clustering_features].copy()\n", "\n", "    # Vérification des valeurs manquantes\n", "    print(f\"\\n🔍 Valeurs manquantes par variable :\")\n", "    missing_counts = X.isnull().sum()\n", "    if missing_counts.sum() > 0:\n", "        print(missing_counts[missing_counts > 0])\n", "    else:\n", "        print(\"✅ Aucune valeur manquante\")\n", "\n", "    # Statistiques descriptives\n", "    print(f\"\\n📈 Statistiques descriptives :\")\n", "    display(X.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Traitement des valeurs manquantes avant normalisation"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Traitement des valeurs manquantes et infinies...\n", "\n", "🧹 Nettoyage des colonnes en double...\n", "\n", "📊 Colonnes avant imputation :\n", "Nombre de colonnes : 22\n", "Liste des colonnes :\n", "['recency', 'frequency', 'monetary', 'customer_lifespan_days', 'days_since_first_order', 'recency_days', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen']\n", "\n", "🔄 Imputation des valeurs manquantes avec la médiane...\n", "\n", "📊 Types de données avant conversion :\n", "recency                     int64\n", "frequency                   int64\n", "monetary                  float64\n", "customer_lifespan_days      int64\n", "days_since_first_order      int64\n", "recency_days                int64\n", "total_amount              float64\n", "amount_std                float64\n", "avg_order_value           float64\n", "amount_cv                 float64\n", "amount_total              float64\n", "avg_amount                float64\n", "amount_std_dev            float64\n", "min_amount                float64\n", "max_amount                float64\n", "amount_cv_coef            float64\n", "amount_range              float64\n", "order_value_mean          float64\n", "total_orders                int64\n", "purchase_frequency        float64\n", "order_count                 int64\n", "montant_moyen             float64\n", "dtype: object\n", "\n", "📊 Valeurs manquantes par colonne :\n", "amount_std            99441\n", "amount_std_dev        99441\n", "purchase_frequency        2\n", "dtype: int64\n", "\n", "📊 Colonnes après imputation :\n", "Nombre de colonnes : 22\n", "Liste des colonnes :\n", "['recency', 'frequency', 'monetary', 'customer_lifespan_days', 'days_since_first_order', 'recency_days', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen']\n", "\n", "✅ Valeurs manquantes après imputation : 198882\n", "\n", "📊 Dataset préparé pour normalisation : (99441, 22)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py:1215: RuntimeWarning: Mean of empty slice\n", "  return np.nanmean(a, axis, out=out, keepdims=keepdims)\n", "/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py:1215: RuntimeWarning: Mean of empty slice\n", "  return np.nanmean(a, axis, out=out, keepdims=keepdims)\n"]}], "source": ["# Traitement des valeurs manquantes et infinies\n", "if 'X' in locals():\n", "    print(\"🔄 Traitement des valeurs manquantes et infinies...\")\n", "\n", "    # Vérification des valeurs infinies\n", "    inf_check = np.isinf(X).sum()\n", "    if inf_check.sum() > 0:\n", "        print(f\"⚠️ Valeurs infinies détectées : {inf_check[inf_check > 0]}\")\n", "        # Remplacer les valeurs infinies par NaN\n", "        X = X.replace([np.inf, -np.inf], np.nan)\n", "\n", "    # Nettoyage des colonnes en double\n", "    print(\"\\n🧹 Nettoyage des colonnes en double...\")\n", "    duplicate_cols = X.columns[X.columns.duplicated()].tolist()\n", "    if duplicate_cols:\n", "        print(f\"Colonnes en double détectées : {duplicate_cols}\")\n", "        # Garder la première occurrence de chaque colonne\n", "        X = X.loc[:, ~X.columns.duplicated()]\n", "        print(f\"Nombre de colonnes après nettoyage : {len(X.columns)}\")\n", "\n", "    # Vérification des colonnes avant imputation\n", "    print(\"\\n📊 Colonnes avant imputation :\")\n", "    print(f\"Nombre de colonnes : {len(X.columns)}\")\n", "    print(\"Liste des colonnes :\")\n", "    print(X.columns.tolist())\n", "\n", "    # Imputation des valeurs manquantes\n", "    if <PERSON><PERSON>isnull().sum().sum() > 0:\n", "        print(\"\\n🔄 Imputation des valeurs manquantes avec la médiane...\")\n", "\n", "        # Vérification des types de données\n", "        print(\"\\n📊 Types de données avant conversion :\")\n", "        print(X.dtypes)\n", "\n", "        # Conversion explicite des colonnes en types numériques\n", "        for col in X.columns:\n", "            try:\n", "                X[col] = pd.to_numeric(X[col], errors='coerce')\n", "            except Exception as e:\n", "                print(f\"⚠️ Erreur lors de la conversion de {col}: {str(e)}\")\n", "\n", "        # Vérification des valeurs manquantes par colonne\n", "        print(\"\\n📊 Valeurs manquantes par colonne :\")\n", "        missing_values = X.isnull().sum()\n", "        print(missing_values[missing_values > 0])\n", "\n", "        # Imputation directe avec la médiane\n", "        X_imputed = X.copy()\n", "        for col in X_imputed.columns:\n", "            median_value = X_imputed[col].median()\n", "            X_imputed[col] = X_imputed[col].fillna(median_value)\n", "\n", "        # Vérification des colonnes après imputation\n", "        print(\"\\n📊 Colonnes après imputation :\")\n", "        print(f\"Nombre de colonnes : {len(X_imputed.columns)}\")\n", "        print(\"Liste des colonnes :\")\n", "        print(X_imputed.columns.tolist())\n", "\n", "        print(f\"\\n✅ Valeurs manquantes après imputation : {X_imputed.isnull().sum().sum()}\")\n", "    else:\n", "        X_imputed = X.copy()\n", "        print(\"✅ Aucune imputation nécessaire\")\n", "\n", "    print(f\"\\n📊 Dataset préparé pour normalisation : {X_imputed.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Standardisation via StandardScaler"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Standardisation des variables...\n", "\n", "📊 Variables à normaliser : 13\n", "📊 Variables constantes : 7\n", "\n", "📊 Vérification des valeurs manquantes après standardisation :\n", "amount_std        99441\n", "amount_std_dev    99441\n", "dtype: int64\n", "\n", "📊 Dataset normalisé : (99441, 22)\n", "\n", "📈 Vérification de la standardisation :\n", "- Moyennes (doivent être ~0) : 0.000000\n", "- Écarts-types (doivent être ~1) : 1.000005\n", "\n", "📊 Statistiques par variable :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Écart-type</th>\n", "      <th>Normalisée</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>recency</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>frequency</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>monetary</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>customer_lifespan_days</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>days_since_first_order</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>recency_days</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_amount</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>amount_std</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>avg_order_value</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>amount_cv</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>amount_total</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>avg_amount</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>amount_std_dev</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min_amount</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max_amount</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>amount_cv_coef</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>amount_range</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>order_value_mean</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_orders</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>purchase_frequency</th>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>order_count</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>montant_moyen</th>\n", "      <td>-0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Moyenne  Écart-type  Normalisée\n", "recency                  0.0000      1.0000        True\n", "frequency                1.0000      0.0000       False\n", "monetary                -0.0000      1.0000        True\n", "customer_lifespan_days   0.0000      0.0000       False\n", "days_since_first_order   0.0000      1.0000        True\n", "recency_days             0.0000      1.0000        True\n", "total_amount            -0.0000      1.0000        True\n", "amount_std               0.0000      0.0000       False\n", "avg_order_value         -0.0000      1.0000        True\n", "amount_cv                0.0000      0.0000       False\n", "amount_total            -0.0000      1.0000        True\n", "avg_amount              -0.0000      1.0000        True\n", "amount_std_dev           0.0000      0.0000       False\n", "min_amount              -0.0000      1.0000        True\n", "max_amount              -0.0000      1.0000        True\n", "amount_cv_coef           0.0000      0.0000       False\n", "amount_range             0.0000      0.0000       False\n", "order_value_mean        -0.0000      1.0000        True\n", "total_orders             1.0000      0.0000       False\n", "purchase_frequency       0.0000      1.0000        True\n", "order_count              1.0000      0.0000       False\n", "montant_moyen           -0.0000      1.0000        True"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Figure exportée : reports/figures/2_02_normalized_distributions.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1500 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Standardisation des variables\n", "if 'X_imputed' in locals():\n", "    print(\"🔄 Standardisation des variables...\")\n", "\n", "    # Identification des variables à normaliser (écart-type > 0)\n", "    variables_to_scale = X_imputed.columns[X_imputed.std() > 0].tolist()\n", "    constant_variables = X_imputed.columns[X_imputed.std() == 0].tolist()\n", "\n", "    print(f\"\\n📊 Variables à normaliser : {len(variables_to_scale)}\")\n", "    print(f\"📊 Variables constantes : {len(constant_variables)}\")\n", "\n", "    # Création d'une copie du DataFrame\n", "    X_scaled = X_imputed.copy()\n", "\n", "    # Normalisation uniquement des variables non constantes\n", "    if variables_to_scale:\n", "        scaler = StandardScaler()\n", "        X_scaled[variables_to_scale] = scaler.fit_transform(X_imputed[variables_to_scale])\n", "\n", "    # Vérification des valeurs manquantes après standardisation\n", "    print(\"\\n📊 Vérification des valeurs manquantes après standardisation :\")\n", "    missing_after = X_scaled.isnull().sum()\n", "    print(missing_after[missing_after > 0])\n", "\n", "    # Remplacement des valeurs NaN par 0 (ou une autre stratégie appropriée)\n", "    X_scaled = X_scaled.fillna(0)\n", "\n", "    print(f\"\\n📊 Dataset normalisé : {X_scaled.shape}\")\n", "    print(f\"\\n📈 Vérification de la standardisation :\")\n", "    print(f\"- <PERSON><PERSON><PERSON> (doivent être ~0) : {X_scaled[variables_to_scale].mean().mean():.6f}\")\n", "    print(f\"- Écarts-types (doivent être ~1) : {X_scaled[variables_to_scale].std().mean():.6f}\")\n", "\n", "    # Affichage des statistiques par variable\n", "    print(\"\\n📊 Statistiques par variable :\")\n", "    stats_df = pd.DataFrame({\n", "        'Moyenne': X_scaled.mean(),\n", "        'Écart-type': X_scaled.std(),\n", "        'Normalisée': X_scaled.columns.isin(variables_to_scale)\n", "    })\n", "    display(stats_df)\n", "\n", "    # Visualisation des distributions après normalisation\n", "    n_features = min(12, len(variables_to_scale))\n", "    n_rows = (n_features + 3) // 4\n", "\n", "    fig, axes = plt.subplots(n_rows, 4, figsize=(20, 5*n_rows))\n", "    if n_rows == 1:\n", "        axes = axes.reshape(1, -1)\n", "    axes = axes.ravel()\n", "\n", "    for i, col in enumerate(variables_to_scale[:n_features]):\n", "        # Vérification des valeurs avant tracé\n", "        values = X_scaled[col].dropna()\n", "        if len(values) > 0:\n", "            axes[i].hist(values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "            axes[i].set_title(f'{col} (normalisé)', fontsize=10)\n", "            axes[i].axvline(0, color='red', linestyle='--', alpha=0.7, label='Moyenne=0')\n", "            axes[i].grid(True, alpha=0.3)\n", "            axes[i].legend()\n", "        else:\n", "            axes[i].text(0.5, 0.5, f'Pas de données valides\\npour {col}',\n", "                        horizontalalignment='center', verticalalignment='center')\n", "            axes[i].set_title(f'{col} (normalisé)', fontsize=10)\n", "\n", "    # Masquer les axes non utilisés\n", "    for i in range(n_features, len(axes)):\n", "        axes[i].set_visible(False)\n", "\n", "    plt.suptitle('Distributions des Variables Normalisées', fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les règles du projet\n", "    export_figure(fig, notebook_name=\"2\", export_number=2, base_name=\"normalized_distributions\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 Export et backup des variables d'origine"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Préparation des datasets finaux...\n", "📊 Dataset final pour clustering : (99441, 22)\n", "📊 Dataset complet avec IDs : (99441, 23)\n", "📊 Dataset enrichi original : (99441, 50)\n", "\n", "📋 Résumé des features créées :\n", "- RFM de base: 14 features\n", "- Temporelles: 4 features\n", "- Variabilité: 14 features\n", "\n", "✅ Total: 50 variables dans le dataset complet\n", "✅ Variables pour clustering: 22\n"]}], "source": ["# Export et backup des variables d'origine et normalisées\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"💾 Préparation des datasets finaux...\")\n", "\n", "    # Sauvegarde du dataset complet avec versions originales et normalisées\n", "    rfm_final = rfm_enriched.copy()\n", "\n", "    # Ajout des versions normalisées avec suffixe '_scaled'\n", "    for col in X_scaled.columns:\n", "        if col in rfm_final.columns:\n", "            rfm_final[f'{col}_scaled'] = X_scaled[col]\n", "\n", "    # Dataset pour clustering avec customer_id pour traçabilité\n", "    X_scaled_with_id = X_scaled.copy()\n", "    X_scaled_with_id['customer_id'] = rfm_enriched['customer_id'].values\n", "\n", "    # Statistiques finales\n", "    print(f\"📊 Dataset final pour clustering : {X_scaled.shape}\")\n", "    print(f\"📊 Dataset complet avec IDs : {X_scaled_with_id.shape}\")\n", "    print(f\"📊 Dataset enrichi original : {rfm_final.shape}\")\n", "\n", "    # Résumé des features créées\n", "    print(f\"\\n📋 Résumé des features créées :\")\n", "    feature_types = {\n", "        'RFM de base': [col for col in rfm_final.columns if any(x in col.lower() for x in ['recency', 'frequency', 'monetary', 'recence', 'frequence', 'montant'])],\n", "        'Temporelles': [col for col in rfm_final.columns if any(x in col.lower() for x in ['lifespan', 'days_since', 'interval', 'between'])],\n", "        'Variabilité': [col for col in rfm_final.columns if any(x in col.lower() for x in ['std', 'cv', 'min', 'max', 'range'])],\n", "        'Diversité': [col for col in rfm_final.columns if any(x in col.lower() for x in ['unique', 'categories', 'diversity'])],\n", "        'Avancées': [col for col in rfm_final.columns if any(x in col.lower() for x in ['cancellation', 'delivery', 'quarter', 'seasonal'])],\n", "        'Ratios': [col for col in rfm_final.columns if any(x in col.lower() for x in ['per_', 'ratio', '_per_'])]\n", "    }\n", "\n", "    for feature_type, features in feature_types.items():\n", "        if features:\n", "            print(f\"- {feature_type}: {len(features)} features\")\n", "\n", "    print(f\"\\n✅ Total: {len(rfm_final.columns)} variables dans le dataset complet\")\n", "    print(f\"✅ Variables pour clustering: {len(X_scaled.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Nettoyage final et Sauvegarde\n", "\n", "### 5.1 Nettoyage final (verif)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valeurs manquantes avant traitement : 0\n", "Valeurs infinies avant traitement : 0\n", "Valeurs manquantes après traitement : 0\n", "Valeurs infinies après traitement : 0\n"]}], "source": ["# Nettoyage des valeurs infinies et manquantes dans X_scaled\n", "X_scaled.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "\n", "print(\"Valeurs manquantes avant traitement :\", X_scaled.isna().sum().sum())\n", "print(\"Valeurs infinies avant traitement :\", np.isinf(X_scaled.values).sum())\n", "\n", "# Option 1 : <PERSON><PERSON><PERSON><PERSON> les NaN par 0 (ou une autre valeur pertinente)\n", "X_scaled.fillna(0, inplace=True)\n", "\n", "# Option 2 (alternative) : Su<PERSON><PERSON>er les lignes avec NaN\n", "# X_scaled.dropna(inplace=True)\n", "\n", "print(\"Valeurs manquantes après traitement :\", X_scaled.isna().sum().sum())\n", "print(\"Valeurs infinies après traitement :\", np.isinf(X_scaled.values).sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Export du jeu final prêt à clusteriser"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Export des datasets pour le clustering...\n", "✅ Dataset clustering sauvegardé : data/processed/2_01_features_scaled_clustering.csv\n", "✅ Dataset avec IDs sauvegardé : data/processed/2_02_features_scaled_with_ids.csv\n", "✅ Dataset complet final sauvegardé : data/processed/2_03_rfm_enriched_complete.csv\n", "✅ Scaler sauvegardé : data/processed/2_04_scaler.joblib\n", "\n", "📊 Résumé des exports :\n", "- Dataset clustering : (99441, 22) - data/processed/2_01_features_scaled_clustering.csv\n", "- Dataset avec IDs : (99441, 23) - data/processed/2_02_features_scaled_with_ids.csv\n", "- Dataset complet final : (99441, 50) - data/processed/2_03_rfm_enriched_complete.csv\n"]}], "source": ["# Export des datasets pour le clustering\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"💾 Export des datasets pour le clustering...\")\n", "\n", "    # Création du dossier de sortie\n", "    os.makedirs('data/processed', exist_ok=True)\n", "\n", "    # Dataset normalisé pour clustering (sans customer_id)\n", "    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'\n", "    X_scaled.to_csv(clustering_data_path, index=False)\n", "    print(f\"✅ Dataset clustering sauvegardé : {clustering_data_path}\")\n", "\n", "    # Dataset avec customer_id pour traçabilité\n", "    X_scaled_with_id = X_scaled.copy()\n", "    X_scaled_with_id['customer_id'] = rfm_enriched['customer_id']\n", "\n", "    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "    X_scaled_with_id.to_csv(traceability_path, index=False)\n", "    print(f\"✅ Dataset avec IDs sauvegardé : {traceability_path}\")\n", "\n", "    # Dataset complet enrichi avec versions originales et normalisées\n", "    if 'rfm_final' in locals():\n", "        complete_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "        rfm_final.to_csv(complete_path, index=False)\n", "        print(f\"✅ Dataset complet final sauvegardé : {complete_path}\")\n", "    else:\n", "        # Fallback vers rfm_enriched si rfm_final n'existe pas\n", "        complete_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "        rfm_enriched.to_csv(complete_path, index=False)\n", "        print(f\"✅ Dataset enrichi sauvegardé : {complete_path}\")\n", "\n", "    # Sauvegarde du scaler pour usage futur\n", "    if 'scaler' in locals():\n", "        import joblib\n", "        scaler_path = 'data/processed/2_04_scaler.joblib'\n", "        joblib.dump(scaler, scaler_path)\n", "        print(f\"✅ Scaler sauvegardé : {scaler_path}\")\n", "\n", "    print(f\"\\n📊 Résumé des exports :\")\n", "    print(f\"- Dataset clustering : {X_scaled.shape} - {clustering_data_path}\")\n", "    if 'X_scaled_with_id' in locals():\n", "        print(f\"- Dataset avec IDs : {X_scaled_with_id.shape} - {traceability_path}\")\n", "    if 'rfm_final' in locals():\n", "        print(f\"- Dataset complet final : {rfm_final.shape} - {complete_path}\")\n", "    else:\n", "        print(f\"- Dataset enrichi : {rfm_enriched.shape} - {complete_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Export de la liste des variables utilisées"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📝 Export de la configuration des features...\n", "✅ Configuration sauvegardée : data/processed/2_05_feature_engineering_config.json\n", "📊 Variables finales pour clustering : 22\n", "👥 Nombre de clients : 99,441\n"]}], "source": ["# Export de la configuration des features\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"📝 Export de la configuration des features...\")\n", "\n", "    # Création de la configuration\n", "    feature_config = {\n", "        'notebook': '2_feature_engineering',\n", "        'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'reference_date': reference_date.strftime('%Y-%m-%d') if 'reference_date' in locals() else None,\n", "        'clustering_features': list(X_scaled.columns),\n", "        'original_shape': list(rfm_enriched.shape),\n", "        'final_shape': list(X_scaled.shape),\n", "        'normalization_method': 'StandardScaler',\n", "        'imputation_strategy': 'median',\n", "        'n_customers': len(rfm_enriched),\n", "        'n_features': len(X_scaled.columns),\n", "        'feature_descriptions': {\n", "            'recency': '<PERSON><PERSON> depuis le dernier achat',\n", "            'frequency': 'Nombre total d\\'achats',\n", "            'monetary': '<PERSON><PERSON> des achats',\n", "            'customer_lifespan_days': '<PERSON><PERSON>e entre premier et dernier achat',\n", "            'days_since_first_order': 'Jo<PERSON> depuis le premier achat',\n", "            'avg_days_between_orders': '<PERSON><PERSON><PERSON> moy<PERSON> entre commandes',\n", "            'order_std': 'Écart-type des montants',\n", "            'order_cv': 'Coefficient de variation des montants',\n", "            'monetary_per_frequency': 'Montant total / fréquence',\n", "            'frequency_per_day': 'Fréquence normalisée par jour'\n", "        },\n", "        'files_exported': {\n", "            'clustering_data': '2_01_features_scaled_clustering.csv',\n", "            'data_with_ids': '2_02_features_scaled_with_ids.csv',\n", "            'complete_data': '2_03_rfm_enriched_complete.csv',\n", "            'scaler': '2_04_scaler.joblib'\n", "        }\n", "    }\n", "\n", "    # Sauvegarde de la configuration\n", "    config_path = 'data/processed/2_05_feature_engineering_config.json'\n", "    with open(config_path, 'w', encoding='utf-8') as f:\n", "        json.dump(feature_config, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "    print(f\"✅ Configuration sauvegardée : {config_path}\")\n", "    print(f\"📊 Variables finales pour clustering : {len(X_scaled.columns)}\")\n", "    print(f\"👥 Nombre de clients : {len(rfm_enriched):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ **Section 1 :** Chargement des données nettoyées du Notebook 1 avec modules utils\n", "- ✅ **Section 2 :** Calcul des variables RFM (Ré<PERSON>, Fréquence, Montant) avec visualisations\n", "- ✅ **Section 3 :** Enrichissement comportemental complet :\n", "  - 3.1 : Ancienneté client et features temporelles\n", "  - 3.2 : <PERSON><PERSON><PERSON> entre commandes et features transactionnelles\n", "  - 3.3 : Diversité des achats et variabilité des montants\n", "  - 3.4 : Consolidation de toutes les features\n", "  - 3.5 : <PERSON><PERSON> de retour et indicateurs avancés (saisonnalité)\n", "- ✅ **Section 4 :** Normalisation et préparation pour clustering :\n", "  - 4.1 : Sélection automatique des variables\n", "  - 4.2 : Traitement des valeurs manquantes et infinies\n", "  - 4.3 : Standardisation avec visualisations\n", "  - 4.4 : Export et backup des versions originales et normalisées\n", "- ✅ **Section 5 :** Sauvegarde complète avec convention de nommage du projet\n", "\n", "### Variables créées pour la segmentation\n", "- **Variables RFM classiques :** récence, fréquence, montant total/moyen\n", "- **Variables temporelles :** ancie<PERSON><PERSON>, d<PERSON><PERSON>s entre commandes, diversité temporelle\n", "- **Variables de variabilité :** écart-type, coefficient de variation, min/max/range\n", "- **Variables de diversité :** catégories uniques, variabilité des achats\n", "- **Variables avancées :** taux d'annulation, livraison, saisonnalité\n", "- **Variables de ratio :** montant/fréquence, fréquence/jour\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 3 :** Clustering et segmentation avec les variables préparées\n", "\n", "---\n", "\n", "**Dataset enrichi et normalisé prêt pour la segmentation !**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}