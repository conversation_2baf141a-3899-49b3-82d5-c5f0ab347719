# =====================================
# REQUIREMENTS.TXT - Projet Olist
# Segmentation Client & Recommandations Marketing
# =====================================

# =========================================
# CORE DATA SCIENCE & MACHINE LEARNING
# =========================================
pandas==2.2.3              # Manipulation et analyse des données
numpy==2.2.5               # Calculs numériques et algèbre linéaire
scipy==1.15.2              # Calculs scientifiques (utilisé dans maintenance simulation)
scikit-learn==1.6.1        # Machine learning (clustering KMeans, preprocessing)

# =========================================
# VISUALISATION & GRAPHIQUES
# =========================================
matplotlib==3.10.1         # Graphiques de base
seaborn==0.13.2            # Visualisations statistiques
plotly==6.0.1              # Graphiques interactifs
folium==0.19.5             # Cartes géographiques interactives

# =========================================
# UTILITAIRES & OUTILS
# =========================================
joblib==1.5.0             # Sauvegarde/chargement de modèles ML
chardet==5.2.0            # Détection d'encodage des fichiers
tqdm==4.67.1              # Barres de progression

# =========================================
# JUPYTER NOTEBOOK ENVIRONMENT
# =========================================
jupyter==1.1.1            # Environnement Jupyter complet
ipykernel==6.29.5         # Kernel Python pour Jupyter
matplotlib-inline==0.1.7  # Affichage inline des graphiques matplotlib
