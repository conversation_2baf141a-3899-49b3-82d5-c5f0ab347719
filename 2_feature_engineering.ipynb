{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 2 : Feature Engineering (First Purchase)\n", "\n", "## Objectif\n", "Construire des variables pertinentes pour la segmentation client adaptées au contexte \"First Purchase\". Puisque chaque client n'a qu'une seule commande, nous nous concentrons sur la récence, le montant et les variables contextuelles plutôt que sur l'approche RFM classique.\n", "\n", "### Variables créées (6 maximum) :\n", "1. **`recency_days`** : <PERSON><PERSON> depu<PERSON> l'achat\n", "2. **`order_value`** : <PERSON><PERSON>e\n", "3. **`state_encoded`** : Localisation géographique\n", "4. **`purchase_month`** : Saisonnalité\n", "5. **`delivery_days`** : <PERSON><PERSON><PERSON>\n", "6. **`review_score_filled`** : Satisfaction client\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données et préparation\n", "\n", "### 1.1 Import des librairies nécessaires"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import sqlite3\n", "from datetime import datetime, timedelta\n", "\n", "# Preprocessing spécifique\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.feature_engineering import (\n", "    create_first_purchase_features,\n", "    clean_and_impute_features,\n", "    analyze_feature_correlation\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results\n", "from utils.clustering_visualization import export_figure\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"2_feature_engineering.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")\n", "\n", "print(f\"\\n📁 Répertoire de travail : {PROJECT_ROOT}\")\n", "print(f\"📊 Répertoire des rapports : {REPORTS_DIR}\")\n", "print(f\"🎲 Graine aléatoire : {SEED}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement des données nettoyées du Notebook 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données nettoyées du Notebook 1\n", "print(\"🔄 Chargement des données nettoyées du Notebook 1...\")\n", "\n", "# Chemin du fichier généré par le Notebook 1\n", "cleaned_data_path = 'data/processed/1_01_cleaned_dataset.csv'\n", "\n", "# Vérification de l'existence du fichier\n", "if not os.path.exists(cleaned_data_path):\n", "    print(f\"❌ Fichier non trouvé : {cleaned_data_path}\")\n", "    print(\"⚠️ Veuillez d'abord exécuter le Notebook 1 pour générer les données nettoyées.\")\n", "    print(\"\\n🔄 Chargement alternatif depuis la base SQLite...\")\n", "\n", "    # Fallback : chargement depuis SQLite si le fichier n'existe pas\n", "    db_path = 'data/raw/olist.db'\n", "    if not os.path.exists(db_path):\n", "        raise FileNotFoundError(f\"❌ Base de données non trouvée : {db_path}\")\n", "\n", "    conn = sqlite3.connect(db_path)\n", "    query = \"\"\"\n", "    SELECT\n", "        o.customer_id,\n", "        o.order_id,\n", "        o.order_purchase_timestamp,\n", "        o.order_delivered_customer_date,\n", "        o.order_status,\n", "        oi.price,\n", "        c.customer_state,\n", "        r.review_score\n", "    FROM orders o\n", "    LEFT JOIN order_items oi ON o.order_id = oi.order_id\n", "    LEFT JOIN customers c ON o.customer_id = c.customer_id\n", "    LEFT JOIN order_reviews r ON o.order_id = r.order_id\n", "    WHERE o.order_status = 'delivered'\n", "      AND o.order_delivered_customer_date IS NOT NULL\n", "    \"\"\"\n", "\n", "    df_raw = pd.read_sql_query(query, conn)\n", "    conn.close()\n", "    print(f\"✅ Données chargées depuis SQLite (fallback)\")\n", "else:\n", "    # Chargement normal depuis le fichier nettoyé\n", "    df_raw = pd.read_csv(cleaned_data_path)\n", "    print(f\"✅ Données chargées depuis le Notebook 1 : {cleaned_data_path}\")\n", "\n", "print(f\"📊 Shape : {df_raw.shape}\")\n", "print(f\"📋 Colonnes : {list(df_raw.columns)}\")\n", "\n", "# Vérification de l'intégrité\n", "print(f\"\\n🔍 Vérifications :\")\n", "print(f\"- Valeurs manquantes : {df_raw.isnull().sum().sum()}\")\n", "print(f\"- Doublons : {df_raw.duplicated().sum()}\")\n", "\n", "# Conversion des dates si nécessaire\n", "date_cols = ['order_purchase_timestamp', 'order_delivered_customer_date']\n", "for col in date_cols:\n", "    if col in df_raw.columns and not pd.api.types.is_datetime64_any_dtype(df_raw[col]):\n", "        df_raw[col] = pd.to_datetime(df_raw[col])\n", "\n", "# Vérification de la période si les colonnes de dates existent\n", "if 'order_purchase_timestamp' in df_raw.columns:\n", "    print(f\"- Période des données : du {df_raw['order_purchase_timestamp'].min()} au {df_raw['order_purchase_timestamp'].max()}\")\n", "\n", "# Vérification du pattern \"1 client = 1 commande\"\n", "if 'customer_id' in df_raw.columns and 'order_id' in df_raw.columns:\n", "    orders_per_customer = df_raw.groupby('customer_id')['order_id'].nunique()\n", "    multi_order_customers = (orders_per_customer > 1).sum()\n", "    print(f\"\\n🎯 Pattern découvert :\")\n", "    print(f\"- Clients avec >1 commande : {multi_order_customers}\")\n", "    print(f\"- Confirmation : {'✅ Chaque client = 1 commande' if multi_order_customers == 0 else '⚠️ Certains clients ont plusieurs commandes'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Préparation des données pour feature engineering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Préparation des données pour l'approche \"First Purchase\"\n", "if 'df_raw' in locals():\n", "    print(\"🔄 Préparation des données pour feature engineering...\")\n", "\n", "    # Vérification des colonnes nécessaires\n", "    required_cols = ['customer_id', 'order_purchase_timestamp', 'price', 'customer_state']\n", "    missing_cols = [col for col in required_cols if col not in df_raw.columns]\n", "\n", "    if missing_cols:\n", "        print(f\"⚠️ Colonnes manquantes : {missing_cols}\")\n", "        print(f\"Colonnes disponibles : {list(df_raw.columns)}\")\n", "    else:\n", "        print(\"✅ Toutes les colonnes nécessaires sont présentes\")\n", "\n", "    # Agrégation par client (somme des prix par commande)\n", "    # Gestion flexible des colonnes selon ce qui est disponible\n", "    group_cols = ['customer_id']\n", "    if 'order_id' in df_raw.columns:\n", "        group_cols.append('order_id')\n", "    if 'order_purchase_timestamp' in df_raw.columns:\n", "        group_cols.append('order_purchase_timestamp')\n", "    if 'order_delivered_customer_date' in df_raw.columns:\n", "        group_cols.append('order_delivered_customer_date')\n", "    if 'customer_state' in df_raw.columns:\n", "        group_cols.append('customer_state')\n", "\n", "    agg_dict = {}\n", "    if 'price' in df_raw.columns:\n", "        agg_dict['price'] = 'sum'\n", "    if 'review_score' in df_raw.columns:\n", "        agg_dict['review_score'] = 'mean'\n", "\n", "    if agg_dict:\n", "        df_aggregated = df_raw.groupby(group_cols).agg(agg_dict).reset_index()\n", "        print(f\"📊 Données agrégées : {df_aggregated.shape}\")\n", "\n", "        # Aperçu des données\n", "        print(f\"📋 Aperçu des données :\")\n", "        display(df_aggregated.head())\n", "\n", "        # Statistiques descriptives\n", "        numeric_cols = df_aggregated.select_dtypes(include=[np.number]).columns\n", "        if len(numeric_cols) > 0:\n", "            print(f\"\\n📊 Statistiques descriptives :\")\n", "            display(df_aggregated[numeric_cols].describe())\n", "\n", "        # Vérification des valeurs manquantes\n", "        print(f\"\\n🔍 Valeurs manquantes par colonne :\")\n", "        missing_values = df_aggregated.isnull().sum()\n", "        for col, count in missing_values.items():\n", "            if count > 0:\n", "                print(f\"- {col}: {count} ({count/len(df_aggregated)*100:.1f}%)\")\n", "\n", "        if missing_values.sum() == 0:\n", "            print(\"✅ Aucune valeur manquante détectée\")\n", "    else:\n", "        print(\"⚠️ Aucune colonne numérique trouvée pour l'agrégation\")\n", "        df_aggregated = df_raw.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Création des variables \"First Purchase\"\n", "\n", "### 2.1 Utilisation du module utils pour créer les features optimisées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création des features \"First Purchase\" avec le module utils\n", "if 'df_aggregated' in locals():\n", "    print(\"🔄 Création des features 'First Purchase' optimisées...\")\n", "\n", "    # Vérification de l'existence de la fonction dans le module utils\n", "    try:\n", "        # Test d'import de la fonction\n", "        from utils.feature_engineering import create_first_purchase_features\n", "        print(\"✅ Fonction create_first_purchase_features trouvée dans utils\")\n", "\n", "        # Utilisation de la fonction du module utils\n", "        first_purchase_features = create_first_purchase_features(\n", "            df=df_aggregated,\n", "            customer_id_col='customer_id',\n", "            date_col='order_purchase_timestamp',\n", "            amount_col='price',\n", "            state_col='customer_state',\n", "            delivery_date_col='order_delivered_customer_date' if 'order_delivered_customer_date' in df_aggregated.columns else None,\n", "            review_col='review_score' if 'review_score' in df_aggregated.columns else None\n", "        )\n", "\n", "        print(f\"✅ Features 'First Purchase' créées : {first_purchase_features.shape}\")\n", "        print(f\"📋 Variables créées : {list(first_purchase_features.columns)}\")\n", "\n", "    except ImportError as e:\n", "        print(f\"⚠️ Fonction create_first_purchase_features non trouvée : {e}\")\n", "        print(\"🔄 Création manuelle des features...\")\n", "\n", "        # Création manuelle des features si la fonction n'existe pas\n", "        first_purchase_features = df_aggregated.copy()\n", "\n", "        # 1. <PERSON><PERSON><PERSON> (jours depuis l'achat)\n", "        if 'order_purchase_timestamp' in first_purchase_features.columns:\n", "            reference_date = first_purchase_features['order_purchase_timestamp'].max()\n", "            first_purchase_features['recency_days'] = (\n", "                reference_date - first_purchase_features['order_purchase_timestamp']\n", "            ).dt.days\n", "\n", "        # 2. <PERSON><PERSON>e\n", "        if 'price' in first_purchase_features.columns:\n", "            first_purchase_features['order_value'] = first_purchase_features['price']\n", "\n", "        # 3. <PERSON><PERSON> encodé\n", "        if 'customer_state' in first_purchase_features.columns:\n", "            le = LabelEncoder()\n", "            first_purchase_features['state_encoded'] = le.fit_transform(\n", "                first_purchase_features['customer_state'].fillna('Unknown')\n", "            )\n", "\n", "        # 4. <PERSON><PERSON>\n", "        if 'order_purchase_timestamp' in first_purchase_features.columns:\n", "            first_purchase_features['purchase_month'] = first_purchase_features['order_purchase_timestamp'].dt.month\n", "\n", "        # 5. <PERSON><PERSON><PERSON>\n", "        if 'order_delivered_customer_date' in first_purchase_features.columns and 'order_purchase_timestamp' in first_purchase_features.columns:\n", "            first_purchase_features['delivery_days'] = (\n", "                first_purchase_features['order_delivered_customer_date'] -\n", "                first_purchase_features['order_purchase_timestamp']\n", "            ).dt.days\n", "\n", "        # 6. Score de review rempli\n", "        if 'review_score' in first_purchase_features.columns:\n", "            median_score = first_purchase_features['review_score'].median()\n", "            first_purchase_features['review_score_filled'] = first_purchase_features['review_score'].fillna(median_score)\n", "\n", "        print(f\"✅ Features créées manuellement : {first_purchase_features.shape}\")\n", "        print(f\"📋 Variables disponibles : {list(first_purchase_features.columns)}\")\n", "\n", "    # Aperçu des features créées\n", "    print(f\"\\n📊 Aperçu des features :\")\n", "    display(first_purchase_features.head())\n", "\n", "    # Statistiques descriptives\n", "    numeric_cols = first_purchase_features.select_dtypes(include=[np.number]).columns\n", "    if len(numeric_cols) > 0:\n", "        print(f\"\\n📈 Statistiques descriptives :\")\n", "        display(first_purchase_features[numeric_cols].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Nettoyage et préparation pour clustering\n", "\n", "### 3.1 Sélection et nettoyage des features finales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sélection et nettoyage des features pour clustering\n", "if 'first_purchase_features' in locals():\n", "    print(\"🔄 Sélection et nettoyage des features pour clustering...\")\n", "\n", "    # Variables attendues selon la stratégie (6 maximum)\n", "    target_features = ['customer_id', 'recency_days', 'order_value', 'state_encoded',\n", "                      'purchase_month', 'delivery_days', 'review_score_filled']\n", "\n", "    # Sélection des features disponibles\n", "    available_features = [col for col in target_features if col in first_purchase_features.columns]\n", "    missing_features = [col for col in target_features if col not in first_purchase_features.columns]\n", "\n", "    print(f\"📊 Features disponibles ({len(available_features)}) : {available_features}\")\n", "    if missing_features:\n", "        print(f\"⚠️ Features manquantes ({len(missing_features)}) : {missing_features}\")\n", "\n", "    # Création du dataset final avec les features disponibles\n", "    features_clean = first_purchase_features[available_features].copy()\n", "\n", "    # Nettoyage des valeurs manquantes\n", "    print(f\"\\n🔍 Nettoyage des valeurs manquantes...\")\n", "    missing_before = features_clean.isnull().sum().sum()\n", "    print(f\"- Valeurs manquantes avant nettoyage : {missing_before}\")\n", "\n", "    # Gestion des valeurs manquantes par type de variable\n", "    for col in features_clean.columns:\n", "        if col != 'customer_id' and features_clean[col].isnull().sum() > 0:\n", "            if features_clean[col].dtype in ['int64', 'float64']:\n", "                # Variables numériques : médiane\n", "                median_val = features_clean[col].median()\n", "                features_clean[col] = features_clean[col].fillna(median_val)\n", "                print(f\"  - {col}: {features_clean[col].isnull().sum()} NaN remplis avec médiane ({median_val:.2f})\")\n", "            else:\n", "                # Variables catégorielles : mode\n", "                mode_val = features_clean[col].mode().iloc[0] if len(features_clean[col].mode()) > 0 else 'Unknown'\n", "                features_clean[col] = features_clean[col].fillna(mode_val)\n", "                print(f\"  - {col}: {features_clean[col].isnull().sum()} NaN remplis avec mode ({mode_val})\")\n", "\n", "    missing_after = features_clean.isnull().sum().sum()\n", "    print(f\"- Valeurs manquantes après nettoyage : {missing_after}\")\n", "\n", "    # Identification des variables numériques pour clustering\n", "    numeric_cols = [col for col in features_clean.columns\n", "                   if col != 'customer_id' and features_clean[col].dtype in ['int64', 'float64']]\n", "\n", "    # Vérification des variables à variance nulle\n", "    zero_variance_vars = []\n", "    for col in numeric_cols:\n", "        if features_clean[col].std() == 0:\n", "            zero_variance_vars.append(col)\n", "\n", "    if zero_variance_vars:\n", "        print(f\"\\n⚠️ Variables à variance nulle détectées : {zero_variance_vars}\")\n", "        print(\"Ces variables seront exclues du clustering.\")\n", "        numeric_cols = [col for col in numeric_cols if col not in zero_variance_vars]\n", "    else:\n", "        print(f\"\\n✅ Aucune variable à variance nulle détectée\")\n", "\n", "    clustering_vars = numeric_cols\n", "    print(f\"\\n📋 Variables finales pour clustering ({len(clustering_vars)}) : {clustering_vars}\")\n", "\n", "    # Vérification des corrélations élevées\n", "    if len(clustering_vars) > 1:\n", "        corr_matrix = features_clean[clustering_vars].corr()\n", "        high_corr_pairs = []\n", "        for i in range(len(clustering_vars)):\n", "            for j in range(i+1, len(clustering_vars)):\n", "                corr_val = abs(corr_matrix.iloc[i, j])\n", "                if corr_val > 0.7:\n", "                    high_corr_pairs.append((clustering_vars[i], clustering_vars[j], corr_val))\n", "\n", "        if high_corr_pairs:\n", "            print(f\"\\n⚠️ Corrélations élevées détectées (>0.7) :\")\n", "            for var1, var2, corr in high_corr_pairs:\n", "                print(f\"  - {var1} ↔ {var2}: {corr:.3f}\")\n", "        else:\n", "            print(f\"\\n✅ Aucune corrélation élevée détectée (seuil: 0.7)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Normalisation et export final\n", "\n", "### 4.1 Standardisation des variables pour clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Standardisation des features pour clustering\n", "if 'features_clean' in locals() and 'clustering_vars' in locals():\n", "    print(\"🔄 Standardisation des variables pour clustering...\")\n", "\n", "    if len(clustering_vars) == 0:\n", "        print(\"❌ Aucune variable numérique disponible pour le clustering\")\n", "    else:\n", "        # Préparation des données pour clustering (exclure customer_id)\n", "        X_clustering = features_clean[clustering_vars].copy()\n", "\n", "        print(f\"📊 Variables à standardiser : {len(clustering_vars)}\")\n", "        print(f\"📋 Variables : {clustering_vars}\")\n", "\n", "        # Standardisation avec StandardScaler\n", "        scaler = StandardScaler()\n", "        X_scaled = pd.DataFrame(\n", "            scaler.fit_transform(X_clustering),\n", "            columns=clustering_vars,\n", "            index=X_clustering.index\n", "        )\n", "\n", "        print(f\"\\n✅ Standardisation terminée : {X_scaled.shape}\")\n", "\n", "        # Vérification de la standardisation\n", "        print(f\"\\n📈 Vérification de la standardisation :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> (doivent être ~0) : {X_scaled.mean().mean():.6f}\")\n", "        print(f\"- Écarts-types (doivent être ~1) : {X_scaled.std().mean():.6f}\")\n", "\n", "        # Ajout de customer_id pour traçabilité\n", "        X_scaled_with_id = X_scaled.copy()\n", "        if 'customer_id' in features_clean.columns:\n", "            X_scaled_with_id['customer_id'] = features_clean['customer_id'].values\n", "\n", "        print(f\"\\n📊 Dataset final pour clustering : {X_scaled.shape}\")\n", "        print(f\"📊 Dataset avec IDs : {X_scaled_with_id.shape}\")\n", "\n", "        # Aperçu des données standardisées\n", "        print(f\"\\n📋 Aperçu des données standardisées :\")\n", "        display(X_scaled.head())\n", "\n", "        # Statistiques descriptives\n", "        print(f\"\\n📈 Statistiques descriptives :\")\n", "        display(X_scaled.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Export des datasets pour le Notebook 3 (CORRIGÉ)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export des datasets pour le clustering - NOMS CORRIGÉS pour Notebook 3\n", "if 'X_scaled' in locals() and 'X_scaled_with_id' in locals():\n", "    print(\"💾 Export des datasets pour le clustering...\")\n", "\n", "    # Création du dossier de sortie\n", "    os.makedirs('data/processed', exist_ok=True)\n", "\n", "    # 1. Dataset normalisé pour clustering (sans customer_id)\n", "    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'\n", "    X_scaled.to_csv(clustering_data_path, index=False)\n", "    print(f\"✅ Dataset clustering sauvegardé : {clustering_data_path}\")\n", "\n", "    # 2. Dataset avec customer_id pour traçabilité\n", "    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "    X_scaled_with_id.to_csv(traceability_path, index=False)\n", "    print(f\"✅ Dataset avec IDs sauvegardé : {traceability_path}\")\n", "\n", "    # 3. Dataset complet enrichi (NOM CORRIGÉ pour Notebook 3)\n", "    # Le Notebook 3 attend '2_03_rfm_enriched_complete.csv'\n", "    complete_features_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "    features_clean.to_csv(complete_features_path, index=False)\n", "    print(f\"✅ Dataset complet sauvegardé : {complete_features_path}\")\n", "\n", "    # 4. <PERSON><PERSON><PERSON><PERSON> du scaler pour usage futur\n", "    import joblib\n", "    scaler_path = 'data/processed/2_04_scaler.pkl'\n", "    joblib.dump(scaler, scaler_path)\n", "    print(f\"✅ Scaler sauvegardé : {scaler_path}\")\n", "\n", "    # Résumé final\n", "    print(f\"\\n📊 Résumé final :\")\n", "    print(f\"- Variables créées : {len(features_clean.columns)} (dont customer_id)\")\n", "    print(f\"- Variables pour clustering : {len(clustering_vars)}\")\n", "    print(f\"- Clients traités : {len(X_scaled):,}\")\n", "    print(f\"- Variables finales : {clustering_vars}\")\n", "\n", "    print(f\"\\n✅ Feature engineering terminé avec succès !\")\n", "    print(f\"📁 Fichiers prêts pour le Notebook 3 (Clustering)\")\n", "    print(f\"\\n📁 Fichiers exportés (compatibles Notebook 3) :\")\n", "    print(f\"  - {clustering_data_path}\")\n", "    print(f\"  - {traceability_path}\")\n", "    print(f\"  - {complete_features_path}\")\n", "    print(f\"  - {scaler_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}