{
  "cSpell.language": "fr,en",
  "cSpell.enabled": false,
  "apc.electron": {
    "opacity": 0.85,
    "transparent": true,
    "backgroundColor": "#00000000",
    "vibrancy": "ultra-dark"
  },
  "editor.fontSize": 12,
  "editor.fontFamily": "SF Mono ",
  "markdown.preview.fontSize": 12,
  "markdown.preview.fontFamily": "SF Mono",
  "terminal.integrated.fontSize": 12,
  "scm.inputFontSize": 12,
  "debug.console.fontSize": 12,
  "tailwindCSS.rootFontSize": 12,
  "editor.formatOnSave": true,
  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  "editor.rulers": [80, 120],
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,

  // Désactiver Tabnine
  "tabnine.disable": true,
  "tabnine.disableFileRegex": [".*"],
  "tabnine.experimentalAutoImports": false,

  // Activation automatique de l'environnement virtuel Python
  "python.terminal.activateEnvironment": true,
  "python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python",

  // "[python]": {
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  // "[kotlin]": {
  //   "editor.tabSize": 4
  // },
  // "[graphql]": {
  //   "editor.defaultFormatter": "esbenp.prettier-vscode"
  // },
  // "[yaml]": {
  //   "editor.tabSize": 4
  // },
  // "[nix]": {
  //   "editor.formatOnSave": true,
  //   "editor.defaultFormatter": "jnoortheen.nix-ide"
  // },

  "eslint.validate": [
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact"
  ],

  // Nouvelles configurations Python pour le linting
  "pylint.enabled": true,
  "flake8.enabled": true,
  "flake8.args": [
    "--max-line-length=88",
    "--select=C,E,F,W,B,B950",
    "--ignore=E203,W503"
  ],

  // Configuration de Black
  "black-formatter.args": ["--line-length=88"],

  "sonarlint.analyzeEverything": true,
  "snyk.preview.enabled": true,
  "gitGuardian.scanOnSave": true,
  "security.workspace.trust.enabled": true,

  "editor.inlineSuggest.enabled": true,
  "github.copilot.enable": {
    "*": true,
    "plaintext": false,
    "markdown": false,
    "scminput": false
  },
  "codeium.enable": true,
  "codeium.inlineSuggest.enable": true,

  "terminal.integrated.defaultProfile.linux": "bash",
  "workbench.startupEditor": "none",

  // Optimisation de la performance
  "files.exclude": {
    "node_modules/": true,
    ".venv/": true,
    "dist/": true,
    "coverage/": true,
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/.next/**": true,
    "**/build/**": true,
    "**/.gradle/**": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.hg/store/**": true,
    "**/dist/**": true,
    "**/.next/**": true,
    "**/build/**": true,
    "**/.gradle/**": true,
    "**/coverage/**": true,
    "**/.venv/**": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/*.code-search": true,
    "**/dist": true,
    "**/.next": true,
    "**/build": true,
    "**/.venv": true
  },

  // Intégration MLOps/AI
  "jupyter.askForKernelRestart": false,
  "jupyter.interactiveWindow.textEditor.executeSelection": true,
  "jupyter.widgetScriptSources": ["jsdelivr.com", "unpkg.com"],
  "python.analysis.extraPaths": ["./scripts", "./src"],

  // Profiles contextuels
  "profiles": [
    {
      "name": "Frontend",
      "settings": {
        "workbench.colorTheme": "GitHub Dark",
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "workbench.startupEditor": "newUntitledFile",
        "terminal.integrated.defaultProfile.osx": "zsh",
        "terminal.integrated.env.osx": {
          "PATH": "${workspaceFolder}/node_modules/.bin:${env:PATH}"
        }
      }
    },
    {
      "name": "Backend",
      "settings": {
        "workbench.colorTheme": "Monokai Pro",
        "terminal.integrated.defaultProfile.osx": "bash",
        "editor.tabSize": 4
      }
    },
    {
      "name": "DataScience",
      "settings": {
        "workbench.colorTheme": "Winter is Coming (Dark Blue)",
        "editor.tabSize": 4
      }
    }
  ],

  // Optimisation de l'éditeur
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.minimap.renderCharacters": false,
  "editor.minimap.maxColumn": 100,
  "editor.cursorBlinking": "phase",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.smoothScrolling": true,
  "workbench.list.smoothScrolling": true,
  "terminal.integrated.smoothScrolling": true,

  // Gestion des onglets et navigation
  "workbench.editor.limit.enabled": true,
  "workbench.editor.limit.value": 4,
  "workbench.editor.limit.perEditorGroup": true,
  "workbench.editor.closeOnFileDelete": true,
  "workbench.editor.enablePreviewFromCodeNavigation": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.enablePreview": false,
  "workbench.editor.highlightModifiedTabs": true,
  "explorer.compactFolders": false,
  "workbench.editor.showTabs": "multiple",

  "workbench.preferredDarkColorTheme": "Theme Darker",
  "workbench.preferredLightColorTheme": "Default Light Modern",
  "window.autoDetectColorScheme": true,
  "python.analysis.typeCheckingMode": "basic",
  "cursorpyright.analysis.extraPaths": ["./scripts", "./src"],
  "cursorpyright.analysis.typeCheckingMode": "basic",
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
