#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module de clustering et segmentation client

Ce module contient toutes les fonctions nécessaires pour effectuer le clustering
des clients, optimiser les paramètres et analyser les résultats.

Fonctionnalités :
- Optimisation du nombre de clusters
- Application d'algorithmes de clustering
- Calcul de métriques de qualité
- Analyse des profils de clusters
"""

from typing import Any, Dict, List, Literal, Optional, Tuple, Union

import numpy as np
import pandas as pd
from sklearn.cluster import DBSCAN, AgglomerativeClustering, KMeans
from sklearn.metrics import (
    calinski_harabasz_score,
    davies_bouldin_score,
    silhouette_score,
)


def find_optimal_k(data: np.ndarray, k_range: range) -> Tuple[List[float], List[float]]:
    """
    Trouve le nombre optimal de clusters avec la méthode du coude et silhouette.

    Args:
        data: Données numériques normalisées pour le clustering
        k_range: Plage de valeurs k à tester

    Returns:
        Tuple contenant les listes d'inertie et de scores silhouette
    """
    inertias = []
    silhouette_scores = []

    for k in k_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(data)

        inertias.append(kmeans.inertia_)

        if k > 1:  # Silhouette score nécessite au moins 2 clusters
            sil_score = silhouette_score(data, labels)
            silhouette_scores.append(sil_score)
        else:
            silhouette_scores.append(0)

    return inertias, silhouette_scores


def perform_kmeans(
    data: np.ndarray, n_clusters: int, random_state: int = 42
) -> Tuple[KMeans, np.ndarray]:
    """
    Applique l'algorithme K-Means aux données.

    Args:
        data: Données numériques normalisées
        n_clusters: Nombre de clusters souhaité
        random_state: Graine aléatoire pour reproductibilité

    Returns:
        Tuple contenant le modèle KMeans entraîné et les labels
    """
    kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init=10)
    labels = kmeans.fit_predict(data)
    return kmeans, labels


def perform_dbscan(
    data: np.ndarray, eps: float = 0.5, min_samples: int = 5
) -> Tuple[DBSCAN, np.ndarray]:
    """
    Applique l'algorithme DBSCAN aux données.

    Args:
        data: Données numériques normalisées
        eps: Distance maximale entre deux échantillons
        min_samples: Nombre minimum d'échantillons par cluster

    Returns:
        Tuple contenant le modèle DBSCAN et les labels
    """
    dbscan = DBSCAN(eps=eps, min_samples=min_samples)
    labels = dbscan.fit_predict(data)
    return dbscan, labels


def perform_hierarchical_clustering(
    data: np.ndarray,
    n_clusters: int,
    linkage: Literal["ward", "complete", "average", "single"] = "ward",
) -> Tuple[AgglomerativeClustering, np.ndarray]:
    """
    Applique le clustering hiérarchique aux données.

    Args:
        data: Données numériques normalisées
        n_clusters: Nombre de clusters souhaité
        linkage: Méthode de liaison

    Returns:
        Tuple contenant le modèle de clustering hiérarchique et les labels
    """
    hierarchical = AgglomerativeClustering(n_clusters=n_clusters, linkage=linkage)
    labels = hierarchical.fit_predict(data)
    return hierarchical, labels


def calculate_clustering_metrics(
    data: np.ndarray, labels: np.ndarray
) -> Dict[str, Optional[float]]:
    """
    Calcule les métriques de qualité du clustering.

    Args:
        data: Données numériques normalisées
        labels: Labels des clusters attribués

    Returns:
        Dictionnaire contenant les métriques de qualité
    """
    if -1 in labels:  # Si DBSCAN avec outliers
        mask = labels != -1
        data_filtered = data[mask]
        labels_filtered = labels[mask]
    else:
        data_filtered = data
        labels_filtered = labels

    if len(np.unique(labels_filtered)) < 2:
        return {"silhouette": None, "calinski_harabasz": None, "davies_bouldin": None}

    try:
        silhouette = silhouette_score(data_filtered, labels_filtered)
        calinski_harabasz = calinski_harabasz_score(data_filtered, labels_filtered)
        davies_bouldin = davies_bouldin_score(data_filtered, labels_filtered)

        return {
            "silhouette": silhouette,
            "calinski_harabasz": calinski_harabasz,
            "davies_bouldin": davies_bouldin,
        }
    except Exception as e:
        print(f"⚠️ Erreur calcul métriques: {e}")
        return {"silhouette": None, "calinski_harabasz": None, "davies_bouldin": None}


def analyze_clusters(
    data: np.ndarray, labels: np.ndarray, feature_names: List[str]
) -> pd.DataFrame:
    """
    Analyse les profils des clusters en calculant des statistiques descriptives.

    Args:
        data: Données numériques normalisées
        labels: Labels des clusters
        feature_names: Noms des features

    Returns:
        DataFrame avec les profils des clusters
    """
    df_analysis = pd.DataFrame(data, columns=feature_names)
    df_analysis["cluster"] = labels

    # Calculer les statistiques par cluster
    cluster_profiles = df_analysis.groupby("cluster").agg(["mean", "std", "count"])
    return cluster_profiles


def evaluate_cluster_stability(
    data: np.ndarray, n_clusters: int, n_iterations: int = 10
) -> Dict[str, float]:
    """
    Évalue la stabilité du clustering en répétant l'algorithme.

    Args:
        data: Données numériques normalisées
        n_clusters: Nombre de clusters
        n_iterations: Nombre d'itérations pour tester la stabilité

    Returns:
        Dictionnaire avec les métriques de stabilité
    """
    silhouette_scores = []

    for i in range(n_iterations):
        kmeans = KMeans(n_clusters=n_clusters, random_state=i, n_init=10)
        labels = kmeans.fit_predict(data)

        if len(np.unique(labels)) >= 2:
            score = silhouette_score(data, labels)
            silhouette_scores.append(score)

    if silhouette_scores:
        return {
            "mean_silhouette": np.mean(silhouette_scores),
            "std_silhouette": np.std(silhouette_scores),
            "stability_score": 1
            - (np.std(silhouette_scores) / np.mean(silhouette_scores)),
        }
    else:
        return {"mean_silhouette": 0, "std_silhouette": 0, "stability_score": 0}


def optimize_dbscan_parameters(
    data: np.ndarray, eps_range: List[float], min_samples_range: List[int]
) -> Tuple[float, int, float]:
    """
    Optimise les paramètres DBSCAN pour obtenir le meilleur score silhouette.

    Args:
        data: Données numériques normalisées
        eps_range: Liste des valeurs eps à tester
        min_samples_range: Liste des valeurs min_samples à tester

    Returns:
        Tuple avec les meilleurs paramètres (eps, min_samples, score)
    """
    best_score = -1
    best_eps = eps_range[0]
    best_min_samples = min_samples_range[0]

    for eps in eps_range:
        for min_samples in min_samples_range:
            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
            labels = dbscan.fit_predict(data)

            # Vérifier qu'il y a au moins 2 clusters (sans compter les outliers)
            unique_labels = np.unique(labels)
            valid_clusters = unique_labels[unique_labels != -1]

            if len(valid_clusters) >= 2:
                # Calculer le score silhouette en excluant les outliers
                mask = labels != -1
                if np.sum(mask) > 0:
                    try:
                        score = silhouette_score(data[mask], labels[mask])
                        if score > best_score:
                            best_score = score
                            best_eps = eps
                            best_min_samples = min_samples
                    except:
                        continue

    return best_eps, best_min_samples, best_score


def create_cluster_summary(labels: np.ndarray, algorithm_name: str) -> Dict[str, Any]:
    """
    Crée un résumé des résultats de clustering.

    Args:
        labels: Labels des clusters
        algorithm_name: Nom de l'algorithme utilisé

    Returns:
        Dictionnaire avec le résumé du clustering
    """
    unique_labels = np.unique(labels)
    cluster_counts = pd.Series(labels).value_counts().sort_index()

    n_outliers = cluster_counts.get(-1, 0)
    n_clusters = (
        len(unique_labels) if -1 not in unique_labels else len(unique_labels) - 1
    )

    return {
        "algorithm": algorithm_name,
        "n_clusters": n_clusters,
        "n_outliers": n_outliers,
        "total_samples": len(labels),
        "cluster_distribution": cluster_counts.to_dict(),
        "largest_cluster": cluster_counts.max(),
        "smallest_cluster": (
            cluster_counts[cluster_counts.index != -1].min() if n_clusters > 0 else 0
        ),
    }


def assign_new_customers_to_clusters(
    new_data: np.ndarray, model: Any, algorithm_type: str
) -> np.ndarray:
    """
    Assigne de nouveaux clients aux clusters existants.

    Args:
        new_data: Nouvelles données client normalisées
        model: Modèle de clustering entraîné
        algorithm_type: Type d'algorithme ('kmeans', 'dbscan', 'hierarchical')

    Returns:
        Labels des clusters pour les nouveaux clients
    """
    if algorithm_type.lower() == "kmeans":
        return model.predict(new_data)
    elif algorithm_type.lower() == "dbscan":
        # Pour DBSCAN, on utilise la distance aux clusters existants
        # Note: Cette implémentation est simplifiée
        return model.fit_predict(new_data)
    else:
        # Pour le clustering hiérarchique, on réentraîne (limitation de scikit-learn)
        print(
            "⚠️ Le clustering hiérarchique ne supporte pas la prédiction de nouveaux points"
        )
        return np.array([-1] * len(new_data))


def compare_clustering_algorithms(
    data: np.ndarray, n_clusters: int, feature_names: List[str], random_state: int = 42
) -> Dict[str, Dict[str, Any]]:
    """
    Compare différents algorithmes de clustering sur les mêmes données.

    Args:
        data: Données numériques normalisées
        n_clusters: Nombre de clusters pour K-Means et hiérarchique
        feature_names: Noms des features
        random_state: Graine aléatoire

    Returns:
        Dictionnaire comparatif des résultats
    """
    results = {}

    # K-Means
    kmeans_model, kmeans_labels = perform_kmeans(data, n_clusters, random_state)
    kmeans_metrics = calculate_clustering_metrics(data, kmeans_labels)
    results["kmeans"] = {
        "model": kmeans_model,
        "labels": kmeans_labels,
        "metrics": kmeans_metrics,
        "summary": create_cluster_summary(kmeans_labels, "K-Means"),
    }

    # DBSCAN (paramètres par défaut)
    dbscan_model, dbscan_labels = perform_dbscan(data)
    dbscan_metrics = calculate_clustering_metrics(data, dbscan_labels)
    results["dbscan"] = {
        "model": dbscan_model,
        "labels": dbscan_labels,
        "metrics": dbscan_metrics,
        "summary": create_cluster_summary(dbscan_labels, "DBSCAN"),
    }

    # Clustering hiérarchique
    hierarchical_model, hierarchical_labels = perform_hierarchical_clustering(
        data, n_clusters
    )
    hierarchical_metrics = calculate_clustering_metrics(data, hierarchical_labels)
    results["hierarchical"] = {
        "model": hierarchical_model,
        "labels": hierarchical_labels,
        "metrics": hierarchical_metrics,
        "summary": create_cluster_summary(hierarchical_labels, "Hierarchical"),
    }

    return results


def select_best_algorithm(comparison_results: Dict[str, Dict[str, Any]]) -> str:
    """
    Sélectionne le meilleur algorithme basé sur les métriques.

    Args:
        comparison_results: Résultats de compare_clustering_algorithms

    Returns:
        Nom du meilleur algorithme
    """
    scores = {}

    for algo_name, results in comparison_results.items():
        metrics = results["metrics"]

        # Calculer un score composite (silhouette prioritaire)
        if metrics["silhouette"] is not None:
            score = metrics["silhouette"]

            # Bonus si Calinski-Harabasz est élevé
            if metrics["calinski_harabasz"] is not None:
                score += (
                    metrics["calinski_harabasz"] / 1000
                )  # Normalisation approximative

            # Malus si Davies-Bouldin est élevé
            if metrics["davies_bouldin"] is not None:
                score -= metrics["davies_bouldin"] / 10  # Malus modéré

            scores[algo_name] = score
        else:
            scores[algo_name] = -1  # Score très bas si pas de silhouette

    # Retourner l'algorithme avec le meilleur score
    return max(scores.items(), key=lambda x: x[1])[0]
