#!/usr/bin/env python3
# coding: utf-8

"""
Module de prétraitement des données.

Ce module fournit des fonctions pour préparer les données avant la modélisation,
notamment la division en jeux d'entraînement/test et la standardisation des features.
"""

import pandas as pd
import numpy as np
from typing import Optional, Literal
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from .core import SEED, TEST_SIZE, VALIDATION_SIZE

def split_data(X, y, test_size=None, random_state=None, stratify=None):
    """
    Divise les données en jeux d'entraînement et de test.

    Args:
        X (pandas.DataFrame): Features
        y (pandas.Series): Variable cible
        test_size (float, optional): Proportion du jeu de test. Par défaut, utilise la valeur de config.py
        random_state (int, optional): Graine aléatoire. Par défaut, utilise la valeur de config.py
        stratify (array-like, optional): Variable pour stratification. Par défaut None.

    Returns:
        tuple: (X_train, X_test, y_train, y_test)
    """
    if test_size is None:
        test_size = TEST_SIZE

    if random_state is None:
        random_state = SEED

    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=stratify
    )

    print("Division des données:")
    print(f"- Jeu d'entraînement: {X_train.shape[0]} échantillons ({(1-test_size)*100:.0f}%)")
    print(f"- Jeu de test: {X_test.shape[0]} échantillons ({test_size*100:.0f}%)")

    return X_train, X_test, y_train, y_test

def create_train_val_test_split(X, y, test_size=None, val_size=None, random_state=None, stratify=None):
    """
    Divise les données en jeux d'entraînement, validation et test.

    Args:
        X (pandas.DataFrame): Features
        y (pandas.Series): Variable cible
        test_size (float, optional): Proportion du jeu de test. Par défaut, utilise la valeur de config.py
        val_size (float, optional): Proportion du jeu de validation (par rapport au jeu d'entraînement).
                                   Par défaut, utilise la valeur de config.py
        random_state (int, optional): Graine aléatoire. Par défaut, utilise la valeur de config.py
        stratify (array-like, optional): Variable pour stratification. Par défaut None.

    Returns:
        tuple: (X_train, X_val, X_test, y_train, y_val, y_test)
    """
    if test_size is None:
        test_size = TEST_SIZE

    if val_size is None:
        val_size = VALIDATION_SIZE

    if random_state is None:
        random_state = SEED

    # Première division: train+val vs test
    X_trainval, X_test, y_trainval, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=stratify
    )

    # Deuxième division: train vs val
    X_train, X_val, y_train, y_val = train_test_split(
        X_trainval, y_trainval, test_size=val_size, random_state=random_state,
        stratify=y_trainval if stratify is not None else None
    )

    print("Division des données en trois jeux:")
    print(f"- Jeu d'entraînement: {X_train.shape[0]} échantillons ({(1-test_size)*(1-val_size)*100:.0f}%)")
    print(f"- Jeu de validation: {X_val.shape[0]} échantillons ({(1-test_size)*val_size*100:.0f}%)")
    print(f"- Jeu de test: {X_test.shape[0]} échantillons ({test_size*100:.0f}%)")

    return X_train, X_val, X_test, y_train, y_val, y_test

def scale_features(X_train, X_test=None, scaler_type='standard', columns=None):
    """
    Standardise les features numériques.

    Args:
        X_train (pandas.DataFrame): Features d'entraînement
        X_test (pandas.DataFrame, optional): Features de test. Par défaut None.
        scaler_type (str, optional): Type de scaler ('standard', 'minmax', 'robust'). Par défaut 'standard'.
        columns (list, optional): Liste des colonnes à standardiser. Si None, toutes les colonnes numériques.

    Returns:
        tuple: (X_train_scaled, X_test_scaled, scaler)
    """
    # Sélectionner les colonnes numériques si non spécifiées
    if columns is None:
        columns = X_train.select_dtypes(include=[np.number]).columns.tolist()

    # Créer le scaler approprié
    if scaler_type.lower() == 'standard':
        scaler = StandardScaler()
    elif scaler_type.lower() == 'minmax':
        scaler = MinMaxScaler()
    elif scaler_type.lower() == 'robust':
        scaler = RobustScaler()
    else:
        raise ValueError(f"Type de scaler non reconnu: {scaler_type}. Utilisez 'standard', 'minmax' ou 'robust'.")

    # Appliquer le scaling
    X_train_scaled = X_train.copy()
    X_train_scaled[columns] = scaler.fit_transform(X_train[columns])

    if X_test is not None:
        X_test_scaled = X_test.copy()
        X_test_scaled[columns] = scaler.transform(X_test[columns])
    else:
        X_test_scaled = None

    print(f"Standardisation appliquée ({scaler_type}) sur {len(columns)} colonnes numériques")

    return X_train_scaled, X_test_scaled, scaler

def encode_categorical(
    X_train: pd.DataFrame,
    X_test: Optional[pd.DataFrame] = None,
    columns: Optional[list] = None,
    drop: Optional[str] = 'first',
    handle_unknown: Literal['error', 'ignore', 'infrequent_if_exist'] = 'error'
):
    """
    Encode les variables catégorielles avec OneHotEncoder.

    Args:
        X_train (pandas.DataFrame): Features d'entraînement
        X_test (pandas.DataFrame, optional): Features de test. Par défaut None.
        columns (list, optional): Liste des colonnes à encoder. Si None, toutes les colonnes non-numériques.
        drop (str, optional): Stratégie de suppression ('first' ou None). Par défaut 'first'.
        handle_unknown (str, optional): Stratégie pour les catégories inconnues ('error' ou 'ignore'). Par défaut 'error'.

    Returns:
        tuple: (X_train_encoded, X_test_encoded, encoder)
    """
    # Sélectionner les colonnes catégorielles si non spécifiées
    if columns is None:
        columns = X_train.select_dtypes(exclude=[np.number]).columns.tolist()

    if not columns:
        print("Aucune colonne catégorielle à encoder.")
        return X_train, X_test, None

    # Créer l'encodeur
    encoder = OneHotEncoder(sparse_output=False, drop=drop, handle_unknown=handle_unknown)

    # Ajuster l'encodeur sur les données d'entraînement
    encoder.fit(X_train[columns])

    # Transformer les données
    encoded_cols = encoder.get_feature_names_out(columns)

    # Convertir explicitement en array numpy pour éviter les problèmes de matrice sparse
    train_encoded_data = encoder.transform(X_train[columns])
    # Pour les matrices sparse, convertir en array dense
    if hasattr(train_encoded_data, 'toarray'):
        train_encoded_array = train_encoded_data.toarray()  # type: ignore
    else:
        train_encoded_array = np.asarray(train_encoded_data)

    X_train_encoded = pd.DataFrame(
        train_encoded_array,
        columns=encoded_cols,
        index=X_train.index
    )

    # Concaténer avec les colonnes numériques
    numeric_cols = X_train.select_dtypes(include=[np.number]).columns.tolist()
    X_train_encoded = pd.concat([X_train[numeric_cols], X_train_encoded], axis=1)

    if X_test is not None:
        # Convertir explicitement en array numpy pour éviter les problèmes de matrice sparse
        test_encoded_data = encoder.transform(X_test[columns])
        if hasattr(test_encoded_data, 'toarray'):
            test_encoded_array = test_encoded_data.toarray()  # type: ignore
        else:
            test_encoded_array = np.asarray(test_encoded_data)

        X_test_encoded = pd.DataFrame(
            test_encoded_array,
            columns=encoded_cols,
            index=X_test.index
        )
        X_test_encoded = pd.concat([X_test[numeric_cols], X_test_encoded], axis=1)
    else:
        X_test_encoded = None

    print(f"Encodage one-hot appliqué sur {len(columns)} colonnes catégorielles")
    print(f"Nombre de features après encodage: {X_train_encoded.shape[1]}")

    return X_train_encoded, X_test_encoded, encoder

def create_preprocessing_pipeline(numeric_features=None, categorical_features=None,
                                 scaler_type='standard', impute_strategy='median'):
    """
    Crée un pipeline de prétraitement avec ColumnTransformer.

    Args:
        numeric_features (list, optional): Liste des colonnes numériques. Si None, à définir lors de l'ajustement.
        categorical_features (list, optional): Liste des colonnes catégorielles. Si None, à définir lors de l'ajustement.
        scaler_type (str, optional): Type de scaler ('standard', 'minmax', 'robust'). Par défaut 'standard'.
        impute_strategy (str, optional): Stratégie d'imputation ('mean', 'median', 'most_frequent'). Par défaut 'median'.

    Returns:
        sklearn.compose.ColumnTransformer: Pipeline de prétraitement
    """
    # Définir les transformateurs pour les features numériques
    numeric_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy=impute_strategy)),
        ('scaler', StandardScaler() if scaler_type == 'standard' else
                  MinMaxScaler() if scaler_type == 'minmax' else
                  RobustScaler())
    ])

    # Définir les transformateurs pour les features catégorielles
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('onehot', OneHotEncoder(handle_unknown='ignore', sparse_output=False))
    ])

    # Créer le ColumnTransformer
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numeric_transformer, numeric_features),
            ('cat', categorical_transformer, categorical_features)
        ],
        remainder='drop'
    )

    return preprocessor

# ============================================================================
# Fonctions d'optimisation des DataFrames (héritées d'utils_general.py)
# ============================================================================

def memory_usage(df: pd.DataFrame, name: Optional[str] = None) -> float:
    """
    Affiche l'utilisation mémoire d'un DataFrame.

    Args:
        df (pandas.DataFrame): DataFrame à analyser
        name (str, optional): Nom du DataFrame. Par défaut None.

    Returns:
        float: Utilisation mémoire en Mo
    """
    memory_mb = df.memory_usage(deep=True).sum() / 1024 / 1024

    if name:
        print(f"Utilisation mémoire de {name}: {memory_mb:.2f} Mo")
    else:
        print(f"Utilisation mémoire: {memory_mb:.2f} Mo")

    return memory_mb


def optimize_dtypes(df: pd.DataFrame, categorical_threshold: int = 10, verbose: bool = True) -> pd.DataFrame:
    """
    Optimise les types de données d'un DataFrame pour réduire l'utilisation mémoire.

    Args:
        df (pandas.DataFrame): DataFrame à optimiser
        categorical_threshold (int, optional): Nombre maximum de valeurs uniques pour convertir en catégorie. Par défaut 10.
        verbose (bool, optional): Afficher les détails de l'optimisation. Par défaut True.

    Returns:
        pandas.DataFrame: DataFrame optimisé
    """
    # Créer une copie du DataFrame
    df_optimized = df.copy()

    # Mesurer l'utilisation mémoire initiale
    initial_memory = memory_usage(df_optimized, "DataFrame initial") if verbose else df_optimized.memory_usage(deep=True).sum() / 1024 / 1024

    # Optimiser les colonnes numériques
    int_columns = df_optimized.select_dtypes(include=['int']).columns
    float_columns = df_optimized.select_dtypes(include=['float']).columns

    for col in int_columns:
        col_min = df_optimized[col].min()
        col_max = df_optimized[col].max()

        # Convertir en int8, int16, int32 ou int64 selon la plage de valeurs
        if col_min >= -128 and col_max <= 127:
            df_optimized[col] = df_optimized[col].astype(np.int8)
        elif col_min >= -32768 and col_max <= 32767:
            df_optimized[col] = df_optimized[col].astype(np.int16)
        elif col_min >= -2147483648 and col_max <= 2147483647:
            df_optimized[col] = df_optimized[col].astype(np.int32)

    for col in float_columns:
        # Convertir en float32 si possible
        df_optimized[col] = df_optimized[col].astype(np.float32)

    # Convertir les colonnes avec peu de valeurs uniques en catégories
    for col in df_optimized.select_dtypes(include=['object']).columns:
        num_unique = df_optimized[col].nunique()
        if num_unique <= categorical_threshold:
            df_optimized[col] = df_optimized[col].astype('category')

    # Mesurer l'utilisation mémoire finale
    final_memory = memory_usage(df_optimized, "DataFrame optimisé") if verbose else df_optimized.memory_usage(deep=True).sum() / 1024 / 1024

    # Afficher les résultats
    if verbose:
        reduction = 100 * (initial_memory - final_memory) / initial_memory
        print(f"Réduction de l'utilisation mémoire: {reduction:.2f}%")

    return df_optimized


def log_shape(df: pd.DataFrame, name: Optional[str] = None) -> None:
    """
    Affiche les dimensions d'un DataFrame de manière lisible.

    Args:
        df (pandas.DataFrame): DataFrame à analyser
        name (str, optional): Nom du DataFrame. Par défaut None.
    """
    if name:
        print(f"Dimensions de {name}: {df.shape[0]} lignes × {df.shape[1]} colonnes")
    else:
        print(f"Dimensions: {df.shape[0]} lignes × {df.shape[1]} colonnes")
