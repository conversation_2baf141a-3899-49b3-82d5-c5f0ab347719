"""
Module de détection robuste de colonnes par type pour éviter les erreurs fragiles.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Set
import logging

logger = logging.getLogger(__name__)


def detect_date_columns(
    df: pd.DataFrame,
    keywords: Optional[List[str]] = None,
    strict_validation: bool = True
) -> List[str]:
    """
    Détecte les colonnes de dates de façon robuste avec validation.

    Args:
        df: DataFrame à analyser
        keywords: Liste de mots-clés pour la détection (par défaut: date, time, timestamp, created, updated)
        strict_validation: Si True, valide que les colonnes contiennent vraiment des dates

    Returns:
        Liste des noms de colonnes de dates détectées
    """
    if df is None or df.empty:
        logger.warning("DataFrame vide ou None, aucune colonne de date détectée")
        return []

    if keywords is None:
        keywords = ['date', 'time', 'timestamp', 'created', 'updated', 'at', 'on']

    date_cols = []

    try:
        # Étape 1: Détection par nom de colonne
        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in keywords):
                date_cols.append(col)

        # Étape 2: Validation si demandée
        if strict_validation:
            validated_cols = []
            for col in date_cols:
                try:
                    # Test de conversion datetime sur un échantillon
                    sample_size = min(100, len(df))
                    sample = df[col].dropna().head(sample_size)

                    if len(sample) > 0:
                        # Tenter la conversion
                        pd.to_datetime(sample, errors='coerce')
                        validated_cols.append(col)
                        logger.debug(f"Colonne de date validée: {col}")
                    else:
                        logger.warning(f"Colonne {col} est entièrement vide, ignorée")

                except Exception as e:
                    logger.warning(f"Colonne {col} non validée comme date: {e}")

            date_cols = validated_cols

        logger.info(f"Colonnes de dates détectées: {date_cols}")
        return date_cols

    except Exception as e:
        logger.error(f"Erreur lors de la détection de colonnes de dates: {e}")
        return []


def detect_amount_columns(
    df: pd.DataFrame,
    keywords: Optional[List[str]] = None,
    min_positive_ratio: float = 0.8
) -> List[str]:
    """
    Détecte les colonnes de montants de façon robuste avec validation.

    Args:
        df: DataFrame à analyser
        keywords: Liste de mots-clés pour la détection
        min_positive_ratio: Ratio minimum de valeurs positives pour valider une colonne de montant

    Returns:
        Liste des noms de colonnes de montants détectées
    """
    if df is None or df.empty:
        logger.warning("DataFrame vide ou None, aucune colonne de montant détectée")
        return []

    if keywords is None:
        keywords = ['price', 'value', 'amount', 'cost', 'payment', 'revenue', 'total', 'sum']

    amount_cols = []

    try:
        # Étape 1: Détection par nom de colonne parmi les colonnes numériques
        numeric_cols = df.select_dtypes(include=[np.number]).columns

        for col in numeric_cols:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in keywords):
                # Validation: vérifier que c'est majoritairement positif
                non_null_values = df[col].dropna()
                if len(non_null_values) > 0:
                    positive_ratio = (non_null_values >= 0).mean()
                    if positive_ratio >= min_positive_ratio:
                        amount_cols.append(col)
                        logger.debug(f"Colonne de montant validée: {col} (ratio positif: {positive_ratio:.2f})")
                    else:
                        logger.warning(f"Colonne {col} rejetée (trop de valeurs négatives: {positive_ratio:.2f})")

        logger.info(f"Colonnes de montants détectées: {amount_cols}")
        return amount_cols

    except Exception as e:
        logger.error(f"Erreur lors de la détection de colonnes de montants: {e}")
        return []


def detect_categorical_columns(
    df: pd.DataFrame,
    max_unique_ratio: float = 0.1,
    max_unique_count: int = 50
) -> List[str]:
    """
    Détecte les colonnes catégorielles de façon robuste.

    Args:
        df: DataFrame à analyser
        max_unique_ratio: Ratio maximum de valeurs uniques par rapport au total
        max_unique_count: Nombre maximum de valeurs uniques

    Returns:
        Liste des noms de colonnes catégorielles détectées
    """
    if df is None or df.empty:
        logger.warning("DataFrame vide ou None, aucune colonne catégorielle détectée")
        return []

    categorical_cols = []

    try:
        for col in df.columns:
            # Exclure les colonnes numériques avec beaucoup de valeurs uniques
            if df[col].dtype in ['object', 'category']:
                unique_count = df[col].nunique()
                unique_ratio = unique_count / len(df) if len(df) > 0 else 0

                if unique_count <= max_unique_count and unique_ratio <= max_unique_ratio:
                    categorical_cols.append(col)
                    logger.debug(f"Colonne catégorielle détectée: {col} ({unique_count} valeurs uniques)")

        logger.info(f"Colonnes catégorielles détectées: {categorical_cols}")
        return categorical_cols

    except Exception as e:
        logger.error(f"Erreur lors de la détection de colonnes catégorielles: {e}")
        return []


def safe_column_detection(
    df: pd.DataFrame,
    detection_type: str = "date",
    **kwargs
) -> List[str]:
    """
    Fonction générique de détection robuste avec gestion d'erreurs complète.

    Args:
        df: DataFrame à analyser
        detection_type: Type de détection ("date", "amount", "categorical")
        **kwargs: Arguments supplémentaires pour les fonctions spécifiques

    Returns:
        Liste des colonnes détectées ou liste vide en cas d'erreur
    """
    try:
        if df is None or df.empty:
            logger.warning(f"DataFrame invalide pour détection {detection_type}")
            return []

        if detection_type == "date":
            return detect_date_columns(df, **kwargs)
        elif detection_type == "amount":
            return detect_amount_columns(df, **kwargs)
        elif detection_type == "categorical":
            return detect_categorical_columns(df, **kwargs)
        else:
            logger.error(f"Type de détection inconnu: {detection_type}")
            return []

    except Exception as e:
        logger.error(f"Erreur fatale lors de la détection {detection_type}: {e}")
        return []


def get_column_info(df: pd.DataFrame) -> Dict[str, List[str]]:
    """
    Retourne un résumé complet des types de colonnes détectées.

    Args:
        df: DataFrame à analyser

    Returns:
        Dictionnaire avec les types de colonnes détectées
    """
    if df is None or df.empty:
        return {"error": "DataFrame vide ou None"}

    try:
        info = {
            "date_columns": safe_column_detection(df, "date"),
            "amount_columns": safe_column_detection(df, "amount"),
            "categorical_columns": safe_column_detection(df, "categorical"),
            "numeric_columns": list(df.select_dtypes(include=[np.number]).columns),
            "text_columns": list(df.select_dtypes(include=['object']).columns),
            "total_columns": len(df.columns)
        }

        logger.info(f"Analyse des colonnes terminée: {info['total_columns']} colonnes analysées")
        return info

    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des colonnes: {e}")
        return {"error": str(e)}
