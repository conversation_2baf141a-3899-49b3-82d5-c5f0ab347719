"""
Module de diagnostic et visualisation robuste pour les features de satisfaction.
Permet de détecter et corriger les problèmes de données vides ou incomplètes.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings

def diagnose_satisfaction_data(satisfaction_df: pd.DataFrame, 
                              reviews_df: pd.DataFrame = None, 
                              orders_df: pd.DataFrame = None) -> Dict:
    """
    Diagnostique complet des données de satisfaction.
    
    Args:
        satisfaction_df: DataFrame des features de satisfaction
        reviews_df: DataFrame original des reviews (optionnel)
        orders_df: DataFrame original des orders (optionnel)
    
    Returns:
        Dict avec le diagnostic complet
    """
    diagnosis = {
        'status': 'unknown',
        'issues': [],
        'recommendations': [],
        'data_quality': {},
        'column_analysis': {},
        'statistics': {}
    }
    
    # 1. Vérifications de base
    if satisfaction_df.empty:
        diagnosis['status'] = 'critical'
        diagnosis['issues'].append("DataFrame satisfaction complètement vide")
        diagnosis['recommendations'].append("Recréer les features de satisfaction")
        return diagnosis
    
    # 2. Analyse des colonnes
    columns = satisfaction_df.columns.tolist()
    expected_cols = ['customer_id', 'avg_review_score', 'total_reviews', 'high_score_ratio']
    missing_cols = [col for col in expected_cols if col not in columns]
    
    if missing_cols:
        diagnosis['issues'].append(f"Colonnes manquantes: {missing_cols}")
        diagnosis['recommendations'].append("Vérifier la fonction create_satisfaction_features")
    
    # 3. Analyse de la qualité des données
    for col in columns:
        if col != 'customer_id':
            non_null_count = satisfaction_df[col].notna().sum()
            null_count = satisfaction_df[col].isna().sum()
            total_count = len(satisfaction_df)
            
            diagnosis['column_analysis'][col] = {
                'non_null': non_null_count,
                'null': null_count,
                'total': total_count,
                'completeness_pct': (non_null_count / total_count) * 100,
                'dtype': str(satisfaction_df[col].dtype)
            }
            
            # Détection de problèmes
            if non_null_count == 0:
                diagnosis['issues'].append(f"Colonne {col} entièrement vide")
            elif non_null_count < total_count * 0.5:
                diagnosis['issues'].append(f"Colonne {col} plus de 50% de valeurs manquantes")
    
    # 4. Statistiques générales
    if 'avg_review_score' in columns:
        score_col = satisfaction_df['avg_review_score']
        if score_col.notna().any():
            diagnosis['statistics']['score_mean'] = score_col.mean()
            diagnosis['statistics']['score_median'] = score_col.median()
            diagnosis['statistics']['score_std'] = score_col.std()
            diagnosis['statistics']['score_min'] = score_col.min()
            diagnosis['statistics']['score_max'] = score_col.max()
    
    if 'total_reviews' in columns:
        reviews_col = satisfaction_df['total_reviews']
        if reviews_col.notna().any():
            diagnosis['statistics']['total_reviews'] = reviews_col.sum()
            diagnosis['statistics']['avg_reviews_per_client'] = reviews_col.mean()
            diagnosis['statistics']['clients_with_reviews'] = (reviews_col > 0).sum()
    
    # 5. Évaluation du statut global
    critical_issues = len([issue for issue in diagnosis['issues'] if 'vide' in issue or 'manquantes' in issue])
    
    if critical_issues == 0:
        diagnosis['status'] = 'good'
    elif critical_issues <= 2:
        diagnosis['status'] = 'warning'
    else:
        diagnosis['status'] = 'critical'
    
    # 6. Recommandations
    if diagnosis['status'] == 'critical':
        diagnosis['recommendations'].extend([
            "Vérifier les données sources (reviews, orders)",
            "Relancer la création des features de satisfaction",
            "Vérifier les jointures entre tables"
        ])
    elif diagnosis['status'] == 'warning':
        diagnosis['recommendations'].extend([
            "Vérifier les colonnes avec données manquantes",
            "Considérer l'imputation des valeurs manquantes"
        ])
    
    return diagnosis

def create_robust_satisfaction_visualizations(satisfaction_df: pd.DataFrame, 
                                            save_path: str = None) -> bool:
    """
    Crée des visualisations robustes même avec des données partielles.
    
    Args:
        satisfaction_df: DataFrame des features de satisfaction
        save_path: Chemin pour sauvegarder les graphiques
    
    Returns:
        bool: True si au moins une visualisation a été créée
    """
    if satisfaction_df.empty:
        print("❌ DataFrame vide - aucune visualisation possible")
        return False
    
    # Diagnostic préalable
    diagnosis = diagnose_satisfaction_data(satisfaction_df)
    print(f"📊 Statut des données: {diagnosis['status'].upper()}")
    
    if diagnosis['issues']:
        print("⚠️ Problèmes détectés:")
        for issue in diagnosis['issues'][:3]:  # Limiter l'affichage
            print(f"   - {issue}")
    
    # Identification des colonnes utilisables
    usable_cols = {}
    for col, analysis in diagnosis['column_analysis'].items():
        if analysis['completeness_pct'] > 0:  # Au moins quelques données
            usable_cols[col] = analysis
    
    if not usable_cols:
        print("❌ Aucune colonne avec des données utilisables")
        return False
    
    print(f"✅ {len(usable_cols)} colonnes utilisables identifiées")
    
    # Création des visualisations adaptatives
    n_plots = min(4, len(usable_cols))  # Maximum 4 graphiques
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()
    
    plot_count = 0
    
    # 1. Distribution des scores (si disponible)
    score_cols = [col for col in usable_cols if 'score' in col.lower()]
    if score_cols and plot_count < n_plots:
        col = score_cols[0]
        data = satisfaction_df[col].dropna()
        if len(data) > 0:
            axes[plot_count].hist(data, bins=min(20, len(data.unique())), 
                                alpha=0.7, color='gold', edgecolor='black')
            axes[plot_count].set_title(f'Distribution {col}\n({len(data)} valeurs)')
            axes[plot_count].axvline(data.mean(), color='red', linestyle='--', 
                                   label=f'Moyenne: {data.mean():.2f}')
            axes[plot_count].legend()
            plot_count += 1
    
    # 2. Distribution des comptages (si disponible)
    count_cols = [col for col in usable_cols if any(term in col.lower() for term in ['total', 'count', 'reviews'])]
    if count_cols and plot_count < n_plots:
        col = count_cols[0]
        data = satisfaction_df[col].dropna()
        if len(data) > 0:
            # Limiter l'affichage pour éviter les outliers
            max_display = min(data.quantile(0.95), 20)
            filtered_data = data[data <= max_display]
            
            axes[plot_count].hist(filtered_data, bins=min(15, len(filtered_data.unique())), 
                                alpha=0.7, color='lightcoral', edgecolor='black')
            axes[plot_count].set_title(f'Distribution {col}\n({len(data)} valeurs)')
            axes[plot_count].axvline(data.mean(), color='blue', linestyle='--',
                                   label=f'Moyenne: {data.mean():.1f}')
            axes[plot_count].legend()
            plot_count += 1
    
    # 3. Boxplot des colonnes numériques
    if plot_count < n_plots:
        numeric_cols = [col for col, analysis in usable_cols.items() 
                       if 'float' in analysis['dtype'] or 'int' in analysis['dtype']]
        if len(numeric_cols) > 0:
            display_cols = numeric_cols[:5]  # Maximum 5 colonnes
            data_for_box = [satisfaction_df[col].dropna() for col in display_cols]
            
            try:
                axes[plot_count].boxplot(data_for_box, 
                                       labels=[col[:12] + '...' if len(col) > 12 else col for col in display_cols])
                axes[plot_count].set_title('Distribution des métriques')
                axes[plot_count].tick_params(axis='x', rotation=45)
                plot_count += 1
            except Exception as e:
                print(f"⚠️ Erreur boxplot: {e}")
    
    # 4. Scatter plot ou autre visualisation
    if plot_count < n_plots and len(usable_cols) >= 2:
        cols = list(usable_cols.keys())[:2]
        col1, col2 = cols[0], cols[1]
        
        data1 = satisfaction_df[col1].dropna()
        data2 = satisfaction_df[col2].dropna()
        
        # Intersection des indices valides
        common_idx = data1.index.intersection(data2.index)
        if len(common_idx) > 0:
            x_data = satisfaction_df.loc[common_idx, col1]
            y_data = satisfaction_df.loc[common_idx, col2]
            
            axes[plot_count].scatter(x_data, y_data, alpha=0.6, s=30)
            axes[plot_count].set_xlabel(col1[:20] + '...' if len(col1) > 20 else col1)
            axes[plot_count].set_ylabel(col2[:20] + '...' if len(col2) > 20 else col2)
            axes[plot_count].set_title(f'Relation {col1} vs {col2}')
            plot_count += 1
    
    # Masquer les axes non utilisés
    for i in range(plot_count, 4):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.suptitle('📊 Analyse Satisfaction Client (Mode Robuste)', fontsize=14, y=0.98)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"💾 Visualisation sauvegardée: {save_path}")
    
    plt.show()
    
    # Affichage des statistiques
    if diagnosis['statistics']:
        print("\n📈 Statistiques disponibles:")
        for key, value in diagnosis['statistics'].items():
            if isinstance(value, float):
                print(f"   {key}: {value:.2f}")
            else:
                print(f"   {key}: {value:,}")
    
    return True

def suggest_satisfaction_fixes(satisfaction_df: pd.DataFrame, 
                              reviews_df: pd.DataFrame = None,
                              orders_df: pd.DataFrame = None) -> List[str]:
    """
    Suggère des corrections pour améliorer les données de satisfaction.
    
    Returns:
        List[str]: Liste des actions recommandées
    """
    suggestions = []
    
    if satisfaction_df.empty:
        suggestions.extend([
            "1. Vérifier que les DataFrames reviews et orders ne sont pas vides",
            "2. Vérifier la fonction create_satisfaction_features",
            "3. Vérifier les noms des colonnes (order_id, customer_id, review_score)"
        ])
        return suggestions
    
    diagnosis = diagnose_satisfaction_data(satisfaction_df, reviews_df, orders_df)
    
    if 'avg_review_score' not in satisfaction_df.columns:
        suggestions.append("Ajouter la colonne avg_review_score manquante")
    
    if 'total_reviews' not in satisfaction_df.columns:
        suggestions.append("Ajouter la colonne total_reviews manquante")
    
    # Vérifications spécifiques
    if 'avg_review_score' in satisfaction_df.columns:
        score_col = satisfaction_df['avg_review_score']
        if score_col.isna().all():
            suggestions.append("Investiguer pourquoi avg_review_score est entièrement vide")
        elif (score_col < 1).any() or (score_col > 5).any():
            suggestions.append("Vérifier la plage des scores (doivent être entre 1 et 5)")
    
    if len(satisfaction_df) < 1000:
        suggestions.append("Nombre de clients faible - vérifier les jointures de données")
    
    return suggestions

def print_satisfaction_report(satisfaction_df: pd.DataFrame) -> None:
    """
    Affiche un rapport complet sur les données de satisfaction.
    """
    print("📊 RAPPORT DE SATISFACTION CLIENT")
    print("=" * 50)
    
    if satisfaction_df.empty:
        print("❌ AUCUNE DONNÉE DISPONIBLE")
        print("\n💡 Actions recommandées:")
        for suggestion in suggest_satisfaction_fixes(satisfaction_df):
            print(f"   - {suggestion}")
        return
    
    diagnosis = diagnose_satisfaction_data(satisfaction_df)
    
    print(f"📈 Statut général: {diagnosis['status'].upper()}")
    print(f"👥 Nombre de clients: {len(satisfaction_df):,}")
    print(f"📊 Nombre de colonnes: {len(satisfaction_df.columns)}")
    
    print(f"\n📋 Colonnes disponibles:")
    for col in satisfaction_df.columns:
        if col in diagnosis['column_analysis']:
            analysis = diagnosis['column_analysis'][col]
            print(f"   • {col}: {analysis['completeness_pct']:.1f}% complet ({analysis['non_null']:,} valeurs)")
        else:
            print(f"   • {col}: (non analysé)")
    
    if diagnosis['statistics']:
        print(f"\n📊 Statistiques clés:")
        stats = diagnosis['statistics']
        if 'score_mean' in stats:
            print(f"   Score moyen: {stats['score_mean']:.2f}/5")
        if 'total_reviews' in stats:
            print(f"   Total avis: {stats['total_reviews']:,}")
        if 'clients_with_reviews' in stats:
            print(f"   Clients avec avis: {stats['clients_with_reviews']:,}")
    
    if diagnosis['issues']:
        print(f"\n⚠️ Problèmes détectés:")
        for issue in diagnosis['issues']:
            print(f"   - {issue}")
    
    if diagnosis['recommendations']:
        print(f"\n💡 Recommandations:")
        for rec in diagnosis['recommendations']:
            print(f"   - {rec}")
