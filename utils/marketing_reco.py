"""
Module : marketing_reco.py
Ce module regroupe la logique métier pour l'analyse des segments clients, la génération de recommandations marketing, l'analyse de valeur, la classification de croissance, la synthèse stratégique et la génération des KPIs pour la segmentation client Olist.
Toutes les fonctions sont documentées et testables.
"""

from typing import Any, Dict, List, Tuple

import pandas as pd

# 1. Analyse de la valeur client par segment


def analyse_valeur_segments(segments_df: pd.DataFrame) -> pd.DataFrame:
    """
    Calcule la valeur moyenne, totale, le pourcentage de valeur et le ratio valeur/clients pour chaque segment.
    """
    value_analysis = pd.DataFrame(index=sorted(segments_df["cluster"].unique()))
    value_analysis["nom"] = [
        (
            segments_df[segments_df["cluster"] == c]["cluster_name"].iloc[0]
            if not segments_df[segments_df["cluster"] == c].empty
            else f"Segment {c}"
        )
        for c in value_analysis.index
    ]
    value_analysis["taille"] = [
        len(segments_df[segments_df["cluster"] == c]) for c in value_analysis.index
    ]
    value_analysis["pourcentage_clients"] = (
        value_analysis["taille"] / len(segments_df) * 100
    )
    if "montant" in segments_df.columns:
        value_analysis["valeur_moyenne"] = [
            (
                segments_df[segments_df["cluster"] == c]["montant"].mean()
                if not segments_df[segments_df["cluster"] == c].empty
                else 0
            )
            for c in value_analysis.index
        ]
        value_analysis["valeur_totale"] = (
            value_analysis["taille"] * value_analysis["valeur_moyenne"]
        )
        total_val = value_analysis["valeur_totale"].sum()
        value_analysis["pourcentage_valeur"] = (
            value_analysis["valeur_totale"] / total_val * 100 if total_val != 0 else 0
        )
        value_analysis["ratio_valeur"] = (
            value_analysis["pourcentage_valeur"] / value_analysis["pourcentage_clients"]
        )
    else:
        value_analysis["valeur_moyenne"] = 0
        value_analysis["valeur_totale"] = 0
        value_analysis["pourcentage_valeur"] = 0
        value_analysis["ratio_valeur"] = 1
    return value_analysis


# 2. Analyse du potentiel de croissance par segment


def analyse_croissance_segments(value_analysis: pd.DataFrame) -> pd.DataFrame:
    """
    Classe les segments selon la matrice croissance/valeur et ajoute les colonnes 'performance' et 'potentiel'.
    """
    growth_analysis = value_analysis.copy()
    growth_analysis["performance"] = "Neutre"
    growth_analysis["potentiel"] = "Moyen"
    median_ratio = growth_analysis["ratio_valeur"].median()
    median_size = growth_analysis["pourcentage_clients"].median()
    for i, row in growth_analysis.iterrows():
        ratio = row["ratio_valeur"]
        size = row["pourcentage_clients"]
        if ratio > median_ratio and size > median_size:
            growth_analysis.loc[i, "performance"] = "Champions"
            growth_analysis.loc[i, "potentiel"] = "Élevé"
        elif ratio > median_ratio and size <= median_size:
            growth_analysis.loc[i, "performance"] = "Premium"
            growth_analysis.loc[i, "potentiel"] = "Croissance"
        elif ratio <= median_ratio and size > median_size:
            growth_analysis.loc[i, "performance"] = "Volume"
            growth_analysis.loc[i, "potentiel"] = "Optimisation"
        else:
            growth_analysis.loc[i, "performance"] = "Attention"
            growth_analysis.loc[i, "potentiel"] = "Développement"
    return growth_analysis


# 3. Génération des recommandations marketing par segment


def generer_recommandations_marketing(
    growth_analysis: pd.DataFrame, segments_df: pd.DataFrame
) -> Dict[str, Dict[str, Any]]:
    """
    Génère un dictionnaire de recommandations marketing personnalisées pour chaque segment.
    """
    marketing_strategies = {}
    for i, row in growth_analysis.iterrows():
        segment_name = row["nom"]
        performance = row["performance"]
        segment_data = segments_df[segments_df["cluster"] == i]
        segment_metrics = {}

        # Adaptation aux nouvelles variables "First Purchase"
        if "recency_days" in segments_df.columns:
            segment_metrics["recency_avg"] = segment_data["recency_days"].mean()
        elif "recence" in segments_df.columns:
            segment_metrics["recency_avg"] = segment_data["recence"].mean()

        if "order_value" in segments_df.columns:
            segment_metrics["order_value_avg"] = segment_data["order_value"].mean()
        elif "montant" in segments_df.columns:
            segment_metrics["order_value_avg"] = segment_data["montant"].mean()

        if "review_score_filled" in segments_df.columns:
            segment_metrics["satisfaction_avg"] = segment_data["review_score_filled"].mean()
        elif "satisfaction_moyenne" in segments_df.columns:
            segment_metrics["satisfaction_avg"] = segment_data["satisfaction_moyenne"].mean()

        # Variables spécifiques au contexte "First Purchase"
        if "state_encoded" in segments_df.columns:
            segment_metrics["geographic_diversity"] = segment_data["state_encoded"].nunique()
        if "purchase_month" in segments_df.columns:
            segment_metrics["seasonal_pattern"] = segment_data["purchase_month"].mode().iloc[0] if len(segment_data) > 0 else 6
        if "delivery_days" in segments_df.columns:
            segment_metrics["delivery_avg"] = segment_data["delivery_days"].mean()
        # Stratégie selon la performance
        if performance == "Champions":
            strategy = {
                "priorite": "🥇 PRIORITÉ MAXIMALE",
                "objectif": "Rétention et développement",
                "budget_recommande": "15-20% du budget marketing",
                "canaux": [
                    "Email premium",
                    "Programme VIP",
                    "Events exclusifs",
                    "Service premium",
                ],
                "messages": [
                    "Offres exclusives et privilèges VIP",
                    "Nouvelles collections en avant-première",
                    "Programme de parrainage avec rewards",
                    "Service client dédié",
                ],
                "frequence": "Hebdomadaire",
                "kpis": [
                    "LTV (Lifetime Value)",
                    "Taux de rétention",
                    "AOV (Average Order Value)",
                    "NPS",
                ],
                "actions": [
                    "Programme de fidélité premium avec niveaux",
                    "Offres personnalisées basées sur l'historique",
                    "Invitation à des événements exclusifs",
                    "Service client prioritaire",
                ],
            }
        elif performance == "Premium":
            strategy = {
                "priorite": "🥈 PRIORITÉ ÉLEVÉE",
                "objectif": "Augmentation fréquence d'achat",
                "budget_recommande": "10-15% du budget marketing",
                "canaux": [
                    "Email ciblé",
                    "Retargeting",
                    "Réseaux sociaux premium",
                    "Content marketing",
                ],
                "messages": [
                    "Produits complémentaires et cross-sell",
                    "Offres bundle et packages",
                    "Programme de fidélité attractif",
                    "Communications éducatives sur la valeur",
                ],
                "frequence": "Bi-hebdomadaire",
                "kpis": [
                    "Fréquence d'achat",
                    "Taux de conversion",
                    "Engagement",
                    "Cross-sell rate",
                ],
                "actions": [
                    "Recommandations produits intelligentes",
                    "Offres bundle personnalisées",
                    "Contenu éducatif sur les produits",
                    "Programme de fidélité adapté",
                ],
            }
        elif performance == "Volume":
            strategy = {
                "priorite": "🥉 PRIORITÉ MODÉRÉE",
                "objectif": "Optimisation de la marge",
                "budget_recommande": "20-25% du budget marketing",
                "canaux": ["Email automated", "Display", "SEO/SEM", "Réseaux sociaux"],
                "messages": [
                    "Mise en avant des produits premium",
                    "Éducation sur la qualité et valeur",
                    "Offres d'upselling ciblées",
                    "Témoignages et social proof",
                ],
                "frequence": "Mensuelle",
                "kpis": ["AOV", "Marge brute", "Upsell rate", "Customer satisfaction"],
                "actions": [
                    "Campagnes d'upselling systématiques",
                    "Éducation sur les bénéfices premium",
                    "Offres promotionnelles sur produits haute marge",
                    "Amélioration de l'expérience d'achat",
                ],
            }
        else:  # Attention
            strategy = {
                "priorite": "⚠️ DÉVELOPPEMENT REQUIS",
                "objectif": "Réactivation et engagement",
                "budget_recommande": "5-10% du budget marketing",
                "canaux": [
                    "Email re-engagement",
                    "Retargeting agressif",
                    "Promos spéciales",
                    "Enquêtes",
                ],
                "messages": [
                    "Offres de reconquête attractives",
                    "Enquêtes de satisfaction pour comprendre",
                    "Contenu éducatif sur les bénéfices",
                    "Témoignages clients satisfaits",
                ],
                "frequence": "Campaign-based",
                "kpis": [
                    "Taux de réactivation",
                    "Open rate",
                    "Click rate",
                    "Feedback score",
                ],
                "actions": [
                    "Campagnes de win-back avec offers spéciales",
                    "Enquêtes pour identifier les points de friction",
                    "Tests A/B sur messaging et offers",
                    "Programme d'onboarding amélioré",
                ],
            }
        marketing_strategies[segment_name] = strategy
    return marketing_strategies


# 4. Génération des KPIs par segment


def get_kpi_definitions() -> Dict[str, Any]:
    """
    Retourne la définition des KPIs primaires et secondaires par type de segment.
    """
    return {
        "Champions": {
            "kpis_primaires": {
                "LTV (Lifetime Value)": {
                    "definition": "Valeur vie client estimée sur 2 ans",
                    "calcul": "Montant moyen × Fréquence × 24 mois",
                    "objectif_min": "1500€",
                    "mesure": "Mensuelle",
                },
                "Taux de rétention": {
                    "definition": "% clients actifs après 12 mois",
                    "calcul": "Clients actifs M12 / Clients initiaux",
                    "objectif_min": "85%",
                    "mesure": "Trimestrielle",
                },
                "NPS (Net Promoter Score)": {
                    "definition": "Satisfaction et recommandation",
                    "calcul": "% Promoteurs - % Détracteurs",
                    "objectif_min": "50",
                    "mesure": "Trimestrielle",
                },
            },
            "kpis_secondaires": {
                "AOV (Average Order Value)": "Panier moyen par commande",
                "Fréquence d'achat": "Nombre commandes par trimestre",
                "Taux d'engagement VIP": "Participation événements exclusifs",
            },
        },
        "Premium": {
            "kpis_primaires": {
                "Fréquence d'achat": {
                    "definition": "Nombre de commandes par trimestre",
                    "calcul": "Commandes / (Clients × Trimestres)",
                    "objectif_min": "2.5",
                    "mesure": "Mensuelle",
                },
                "Taux de conversion cross-sell": {
                    "definition": "% commandes avec produits complémentaires",
                    "calcul": "Commandes multi-produits / Total commandes",
                    "objectif_min": "35%",
                    "mesure": "Mensuelle",
                },
                "Engagement contenu": {
                    "definition": "Interaction avec recommandations",
                    "calcul": "Clics recommandations / Emails envoyés",
                    "objectif_min": "15%",
                    "mesure": "Mensuelle",
                },
            },
            "kpis_secondaires": {
                "AOV évolution": "Croissance panier moyen",
                "Taux ouverture emails": "Engagement communications",
                "Score satisfaction": "Notes moyennes produits",
            },
        },
        "Volume": {
            "kpis_primaires": {
                "AOV (Average Order Value)": {
                    "definition": "Panier moyen par commande",
                    "calcul": "Chiffre affaires / Nombre commandes",
                    "objectif_min": "75€",
                    "mesure": "Mensuelle",
                },
                "Taux d'upselling": {
                    "definition": "% commandes avec montée en gamme",
                    "calcul": "Commandes premium / Total commandes",
                    "objectif_min": "25%",
                    "mesure": "Mensuelle",
                },
                "Marge brute": {
                    "definition": "Rentabilité par segment",
                    "calcul": "(CA - Coûts) / CA",
                    "objectif_min": "35%",
                    "mesure": "Mensuelle",
                },
            },
            "kpis_secondaires": {
                "Taux de conversion": "Visiteurs vers acheteurs",
                "Diversité produits": "Nombre catégories achetées",
                "Récurrence": "Fidélisation base clients",
            },
        },
        "Attention": {
            "kpis_primaires": {
                "Taux de réactivation": {
                    "definition": "% clients inactifs redevenus actifs",
                    "calcul": "Clients réactivés / Clients inactifs",
                    "objectif_min": "20%",
                    "mesure": "Mensuelle",
                },
                "Satisfaction post-support": {
                    "definition": "Amélioration expérience client",
                    "calcul": "Score satisfaction après intervention",
                    "objectif_min": "4.0/5",
                    "mesure": "Continue",
                },
                "Taux de conversion win-back": {
                    "definition": "Efficacité campagnes de reconquête",
                    "calcul": "Achats / Emails reconquête envoyés",
                    "objectif_min": "5%",
                    "mesure": "Par campagne",
                },
            },
            "kpis_secondaires": {
                "Open rate emails": "Engagement communications",
                "Click rate": "Intérêt pour les offres",
                "Feedback score": "Compréhension des freins",
            },
        },
    }


# 5. Génération des personas clients


def create_customer_personas(df, cluster_col='cluster', profile_col=None, key_vars=None):
    """
    Génère un dictionnaire de personas à partir d'un DataFrame clusterisé.
    - df : DataFrame avec les clients et leur cluster
    - cluster_col : nom de la colonne des clusters
    - profile_col : nom de la colonne de profil (optionnel)
    - key_vars : variables clés à inclure dans les métriques
    """
    personas = {}
    clusters = sorted(df[cluster_col].unique())
    total = len(df)
    for c in clusters:
        subset = df[df[cluster_col] == c]
        metrics = {
            'taille': len(subset),
            'pourcentage': 100 * len(subset) / total
        }
        # Ajout de quelques métriques clés si elles existent
        if key_vars:
            for var in key_vars:
                metrics[f'{var}_mean'] = subset[var].mean()
        # Exemple de nom et comportement fictif
        persona = {
            'nom': f"Persona {c}",
            'metrics': metrics,
            'comportement': {
                'activite': "À personnaliser",
                'fidelite': "À personnaliser",
                'valeur': "À personnaliser"
            }
        }
        personas[c] = persona
    return personas


# 6. Stratégies spécifiques "First Purchase"

def generer_strategies_first_purchase(segments_df: pd.DataFrame, cluster_col: str = "cluster") -> Dict[str, Dict[str, Any]]:
    """
    Génère des stratégies marketing adaptées au contexte "First Purchase"
    où chaque client n'a qu'une seule commande.

    Args:
        segments_df: DataFrame avec les segments clients
        cluster_col: Nom de la colonne contenant les clusters

    Returns:
        Dictionnaire des stratégies par segment
    """
    strategies = {}

    for cluster_id in segments_df[cluster_col].unique():
        segment_data = segments_df[segments_df[cluster_col] == cluster_id]

        # Calcul des métriques du segment
        metrics = {}
        if "recency_days" in segments_df.columns:
            metrics["recency_avg"] = segment_data["recency_days"].mean()
        if "order_value" in segments_df.columns:
            metrics["order_value_avg"] = segment_data["order_value"].mean()
        if "review_score_filled" in segments_df.columns:
            metrics["satisfaction_avg"] = segment_data["review_score_filled"].mean()

        # Détermination du profil du segment
        recency = metrics.get("recency_avg", 180)
        order_value = metrics.get("order_value_avg", 50)
        satisfaction = metrics.get("satisfaction_avg", 3.0)

        # Classification du segment
        if recency <= 60 and order_value >= 100:
            segment_type = "Premium_Newcomers"
        elif recency <= 90 and satisfaction >= 4.0:
            segment_type = "Satisfied_Recent"
        elif order_value >= 150:
            segment_type = "High_Value"
        elif recency >= 300:
            segment_type = "Dormant_Clients"
        else:
            segment_type = "Standard_Clients"

        # Stratégies par type de segment
        if segment_type == "Premium_Newcomers":
            strategy = {
                "nom": "Nouveaux Clients Premium",
                "priorite": "🥇 PRIORITÉ MAXIMALE",
                "objectif": "Réactivation premium pour 2e achat",
                "contexte": "Clients récents à forte valeur - potentiel élevé",
                "canaux": [
                    "Email personnalisé premium",
                    "Retargeting haute valeur",
                    "SMS exclusif",
                    "Appel commercial"
                ],
                "messages": [
                    "Offres exclusives basées sur premier achat",
                    "Recommandations produits complémentaires",
                    "Invitation programme VIP",
                    "Service client prioritaire"
                ],
                "frequence": "Hebdomadaire pendant 2 mois",
                "kpis": [
                    "Taux de 2e achat (objectif: 35%)",
                    "Délai de réactivation (objectif: <45 jours)",
                    "LTV estimée",
                    "Engagement email (objectif: >25%)"
                ],
                "budget_recommande": "30-35% du budget réactivation"
            }
        elif segment_type == "Satisfied_Recent":
            strategy = {
                "nom": "Clients Satisfaits Récents",
                "priorite": "🥈 PRIORITÉ ÉLEVÉE",
                "objectif": "Conversion en clients réguliers",
                "contexte": "Clients satisfaits de leur première expérience",
                "canaux": [
                    "Email de suivi satisfaction",
                    "Retargeting produits similaires",
                    "Réseaux sociaux",
                    "Programme de fidélité"
                ],
                "messages": [
                    "Merci pour votre confiance",
                    "Découvrez nos autres produits",
                    "Rejoignez notre communauté",
                    "Partagez votre expérience"
                ],
                "frequence": "Bi-hebdomadaire",
                "kpis": [
                    "Taux de 2e achat (objectif: 25%)",
                    "Engagement réseaux sociaux",
                    "Taux de recommandation",
                    "Score NPS"
                ],
                "budget_recommande": "20-25% du budget réactivation"
            }
        elif segment_type == "High_Value":
            strategy = {
                "nom": "Clients Haute Valeur",
                "priorite": "🥇 PRIORITÉ MAXIMALE",
                "objectif": "Maximiser la LTV",
                "contexte": "Clients ayant dépensé un montant élevé",
                "canaux": [
                    "Contact commercial direct",
                    "Email ultra-personnalisé",
                    "Invitation événements",
                    "Service VIP"
                ],
                "messages": [
                    "Offres premium exclusives",
                    "Accès anticipé nouveautés",
                    "Service personnalisé",
                    "Programme VIP sur-mesure"
                ],
                "frequence": "Contact mensuel personnalisé",
                "kpis": [
                    "Taux de 2e achat (objectif: 40%)",
                    "Montant 2e achat",
                    "Engagement VIP",
                    "Satisfaction service"
                ],
                "budget_recommande": "25-30% du budget réactivation"
            }
        elif segment_type == "Dormant_Clients":
            strategy = {
                "nom": "Clients Dormants",
                "priorite": "⚠️ RÉACTIVATION URGENTE",
                "objectif": "Win-back avec offres attractives",
                "contexte": "Clients anciens à réactiver",
                "canaux": [
                    "Email de reconquête",
                    "Retargeting agressif",
                    "Offres promotionnelles",
                    "Enquête de satisfaction"
                ],
                "messages": [
                    "Nous vous avons manqué",
                    "Offre spéciale de retour",
                    "Découvrez nos nouveautés",
                    "Aidez-nous à nous améliorer"
                ],
                "frequence": "Campagne intensive 3 semaines",
                "kpis": [
                    "Taux de réactivation (objectif: 15%)",
                    "Open rate email",
                    "Click rate offres",
                    "Feedback qualité"
                ],
                "budget_recommande": "10-15% du budget réactivation"
            }
        else:  # Standard_Clients
            strategy = {
                "nom": "Clients Standard",
                "priorite": "🥉 PRIORITÉ MODÉRÉE",
                "objectif": "Réactivation progressive",
                "contexte": "Clients moyens à développer",
                "canaux": [
                    "Email automatisé",
                    "Retargeting standard",
                    "Réseaux sociaux",
                    "Newsletter"
                ],
                "messages": [
                    "Offres saisonnières",
                    "Nouveautés produits",
                    "Conseils d'utilisation",
                    "Témoignages clients"
                ],
                "frequence": "Mensuelle",
                "kpis": [
                    "Taux de 2e achat (objectif: 20%)",
                    "Engagement newsletter",
                    "Taux de conversion",
                    "Coût d'acquisition"
                ],
                "budget_recommande": "15-20% du budget réactivation"
            }

        strategies[f"Segment_{cluster_id}"] = strategy

    return strategies


def generer_strategies_first_purchase(df_clustered, cluster_col='cluster'):
    """
    Génère des stratégies marketing spécifiques au contexte First Purchase.

    Args:
        df_clustered: DataFrame avec les données clusterisées
        cluster_col: Nom de la colonne cluster

    Returns:
        dict: Recommandations par segment
    """
    recommendations = {}

    # Analyse des clusters
    for cluster_id in df_clustered[cluster_col].unique():
        cluster_data = df_clustered[df_clustered[cluster_col] == cluster_id]
        size = len(cluster_data)

        # Calcul des métriques disponibles
        avg_value = cluster_data['order_value'].mean() if 'order_value' in cluster_data.columns else 0
        avg_recency = cluster_data['recency_days'].mean() if 'recency_days' in cluster_data.columns else 0

        # Détermination de la stratégie selon les métriques
        if 'order_value' in cluster_data.columns:
            median_value = df_clustered['order_value'].median()
            if avg_value > median_value:
                if avg_recency < 90:  # Récent et haute valeur
                    persona = "Premium Newcomers"
                    priorite = "🥇 PRIORITÉ MAXIMALE"
                    objectif = "Fidélisation premium et upselling"
                    strategies = [
                        "Programme VIP exclusif",
                        "Recommandations personnalisées premium",
                        "Service client prioritaire"
                    ]
                else:  # Anciens mais haute valeur
                    persona = "High-Value Dormants"
                    priorite = "🥈 PRIORITÉ ÉLEVÉE"
                    objectif = "Réactivation ciblée haute valeur"
                    strategies = [
                        "Campagne de réactivation premium",
                        "Offres exclusives personnalisées",
                        "Contact direct commercial"
                    ]
            else:
                if avg_recency < 90:  # Récent mais faible valeur
                    persona = "Budget Conscious"
                    priorite = "🥉 PRIORITÉ MODÉRÉE"
                    objectif = "Augmentation panier moyen"
                    strategies = [
                        "Offres de cross-selling",
                        "Promotions sur catégories complémentaires",
                        "Programme de fidélité points"
                    ]
                else:  # Anciens et faible valeur
                    persona = "Inactive Low-Value"
                    priorite = "🥉 PRIORITÉ MODÉRÉE"
                    objectif = "Réactivation économique"
                    strategies = [
                        "Campagnes promotionnelles agressives",
                        "Offres de retour client",
                        "Marketing automation basique"
                    ]
        else:
            # Fallback si pas de données de valeur
            persona = f"Segment {cluster_id}"
            priorite = "🥉 PRIORITÉ MODÉRÉE"
            objectif = "Analyse approfondie nécessaire"
            strategies = [
                "Collecte de données supplémentaires",
                "Tests A/B sur différentes approches",
                "Segmentation plus fine"
            ]

        recommendations[f'Segment_{cluster_id}'] = {
            'persona': persona,
            'priorite': priorite,
            'objectif': objectif,
            'strategies': strategies,
            'taille_segment': size,
            'valeur_moyenne': avg_value,
            'recence_moyenne': avg_recency
        }

    return recommendations
