#!/usr/bin/env python3
# coding: utf-8
"""
Module centralisé pour l'exploration, le chargement, la sélection de features,
la gestion des outliers et des variables temporelles.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.feature_selection import SelectKBest, f_regression, RFE
from .core import OUTLIER_IQR_MULTIPLIER
from pathlib import Path
import chardet

# --- Fonctions de data_loader.py ---
import sqlite3

def load_database(db_path):
    """
    Établit une connexion à la base de données SQLite.
    Args:
        db_path (str): Chemin vers le fichier .db
    Returns:
        sqlite3.Connection: connexion à la base
    """
    if not os.path.exists(db_path):
        raise FileNotFoundError(f"Le fichier de base de données {db_path} n'existe pas.")
    conn = sqlite3.connect(db_path)
    return conn

def get_table_names(conn):
    """
    Retourne la liste des tables dans la base SQLite.
    Args:
        conn (sqlite3.Connection): connexion à la base
    Returns:
        list: noms des tables
    """
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    return tables

def load_table(conn, table_name):
    """
    Charge une table SQLite dans un DataFrame pandas.
    Args:
        conn (sqlite3.Connection): connexion à la base
        table_name (str): nom de la table
    Returns:
        pd.DataFrame: table chargée
    """
    query = f"SELECT * FROM {table_name}"
    df = pd.read_sql_query(query, conn)
    return df

def detect_encoding(filepath):
    with open(filepath, 'rb') as f:
        result = chardet.detect(f.read(10000))
    return result['encoding']

def load_data(filepath, encoding=None, **kwargs):
    if not Path(filepath).exists():
        raise FileNotFoundError(f"Le fichier {filepath} n'existe pas.")
    file_ext = Path(filepath).suffix.lower()
    if encoding is None:
        encoding = detect_encoding(filepath)
        print(f"Encodage détecté: {encoding}")
    if file_ext in ['.csv', '.txt']:
        df = pd.read_csv(filepath, encoding=encoding, **kwargs)
    elif file_ext in ['.xlsx', '.xls']:
        df = pd.read_excel(filepath, **kwargs)
    else:
        raise ValueError(f"Extension de fichier non supportée: {file_ext}")
    print(f"Données chargées avec succès: {df.shape[0]} lignes × {df.shape[1]} colonnes")
    return df

def preview_data(df, rows=5, show_info=True, show_describe=False):
    print(f"Dimensions: {df.shape[0]} lignes × {df.shape[1]} colonnes")
    if show_info:
        print("\nInformations sur les types et valeurs manquantes:")
        buffer = []
        for col in df.columns:
            missing = df[col].isna().sum()
            missing_pct = missing / len(df) * 100
            buffer.append(f"{col}: {df[col].dtype} | Manquants: {missing} ({missing_pct:.2f}%)")
        n_cols = 2
        n_rows = (len(buffer) + n_cols - 1) // n_cols
        for i in range(n_rows):
            row_items = buffer[i*n_cols:(i+1)*n_cols]
            print("  ".join(row_items))
    if show_describe:
        print("\nStatistiques descriptives:")
        print(df.describe())
    print("\nAperçu des données:")
    return df.head(rows)

def standardize_column_names(df):
    df.columns = [col.lower().replace(' ', '_').replace('(', '').replace(')', '') for col in df.columns]
    return df

# --- Fonctions de eda_tools.py ---
def describe_missing(df):
    missing = pd.DataFrame({
        'colonne': df.columns,
        'type': df.dtypes,
        'nb_manquants': df.isna().sum(),
        'pct_manquants': (df.isna().sum() / len(df) * 100).round(2)
    })
    missing = missing.sort_values('pct_manquants', ascending=False).reset_index(drop=True)
    print(f"Résumé des valeurs manquantes ({len(df)} lignes au total):")
    print(f"- {missing['nb_manquants'].sum()} valeurs manquantes au total")
    print(f"- {missing[missing['nb_manquants'] > 0].shape[0]} colonnes avec au moins une valeur manquante")
    print(f"- {missing[missing['pct_manquants'] > 50].shape[0]} colonnes avec plus de 50% de valeurs manquantes")
    return missing

def plot_missing_heatmap(df, figsize=(14, 10), show=False):
    """
    Affiche une heatmap des valeurs manquantes d'un DataFrame.
    """
    plt.figure(figsize=figsize)
    sns.heatmap(df.isnull(), cbar=False, cmap="viridis")
    plt.title("Heatmap des valeurs manquantes")
    if show:
        plt.show()
    return plt.gcf()


def plot_correlation_matrix(df, method='pearson', figsize=(14, 12), annot=True, mask_upper=True,
                       cmap='coolwarm', style="modern", filename=None, save_dir=None, dpi=150):
    """
    Affiche et retourne la matrice de corrélation et la figure associée.
    Si filename est fourni, sauvegarde la figure dans le dossier spécifié.
    """
    corr_matrix = df.corr(method=method)
    mask = None
    if mask_upper:
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))

    plt.style.use('default' if style == "modern" else style)
    fig, ax = plt.subplots(figsize=figsize)
    sns.heatmap(corr_matrix, mask=mask, annot=annot, cmap=cmap, ax=ax, fmt='.2f', square=True, cbar=True)
    ax.set_title("Matrice de corrélation")
    plt.tight_layout()

    # Sauvegarde si demandé
    if filename is not None:
        if save_dir is not None:
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, filename)
        else:
            save_path = filename
        fig.savefig(save_path, dpi=dpi)
        print(f"Figure sauvegardée : {save_path}")

    return fig, corr_matrix


EXPORT_TYPES = [
    "figures", "maps", "metrics", "analysis", "features", "models", "predictions", "presentation"
]


def get_export_dir(base_reports_dir, export_type):
    """
    Retourne le chemin du sous-dossier d'export selon le type.
    Crée le dossier s'il n'existe pas.
    """
    if export_type not in EXPORT_TYPES:
        raise ValueError(f"Type d'export non supporté : {export_type}")
    export_dir = os.path.join(base_reports_dir, export_type)
    os.makedirs(export_dir, exist_ok=True)
    return export_dir


def generate_export_filename(notebook_name, export_number, base_name, ext="png"):
    return f"{notebook_name}_{export_number:02d}_{base_name}.{ext}"


def export_artifact(obj, notebook_name, export_number, base_name, export_type, base_reports_dir, ext="png", save_func=None, **kwargs):
    """
    Automatise la sauvegarde d'un artefact (figure, map, métrique, etc.) dans le bon dossier avec le bon nom.
    - obj : l'objet à sauvegarder (figure matplotlib, folium map, etc.)
    - notebook_name : nom du notebook
    - export_number : numéro d'export (ordre)
    - base_name : nom court du contenu
    - export_type : type d'export (figures, maps, ...)
    - base_reports_dir : dossier racine des exports (ex: REPORTS_DIR)
    - ext : extension (png, html, txt, ...)
    - save_func : fonction de sauvegarde personnalisée (par défaut .savefig pour matplotlib)
    - kwargs : arguments supplémentaires pour save_func
    """
    export_dir = get_export_dir(base_reports_dir, export_type)
    filename = generate_export_filename(notebook_name, export_number, base_name, ext)
    save_path = os.path.join(export_dir, filename)

    print(f"[DEBUG] Type de l'objet à exporter : {type(obj)}")
    print(f"[DEBUG] Chemin de sauvegarde prévu : {save_path}")
    try:
        if save_func is not None:
            save_func(obj, save_path, **kwargs)
        else:
            # Par défaut, matplotlib figure
            obj.savefig(save_path, **kwargs)
        print(f"Export sauvegardé : {save_path}")
    except Exception as e:
        print(f"[ERREUR EXPORT] Impossible de sauvegarder l'artefact : {e}")
    return save_path


def plot_feature_distributions(df, columns=None, figsize=(16, 12), bins=20, kde=True):
    if columns is None:
        columns = df.select_dtypes(include='number').columns
    n_cols = 2
    n_rows = (len(columns) + 1) // n_cols
    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)
    axes = axes.flatten()
    i = 0
    for i, col in enumerate(columns):
        sns.histplot(df[col].dropna(), bins=bins, kde=kde, ax=axes[i])
        axes[i].set_title(col)
    # Supprimer les axes inutilisés
    for j in range(i+1, len(axes)):
        fig.delaxes(axes[j])
    plt.tight_layout()
    return fig


# --- Fonctions de outliers.py ---
def detect_outliers_iqr(df, column, multiplier=None):
    if multiplier is None:
        multiplier = OUTLIER_IQR_MULTIPLIER
    if column not in df.columns:
        raise ValueError(f"La colonne '{column}' n'existe pas dans le DataFrame")
    if not np.issubdtype(df[column].dtype, np.number):
        raise TypeError(f"La colonne '{column}' n'est pas numérique")
    q1 = df[column].quantile(0.25)
    q3 = df[column].quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - multiplier * iqr
    upper_bound = q3 + multiplier * iqr
    outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]
    print(f"Détection d'outliers pour '{column}':")
    print(f"- Q1: {q1:.2f}, Q3: {q3:.2f}, IQR: {iqr:.2f}")
    print(f"- Borne inférieure: {lower_bound:.2f}, Borne supérieure: {upper_bound:.2f}")
    print(f"- {len(outliers)} outliers détectés ({len(outliers)/len(df)*100:.2f}% des données)")
    return outliers, (lower_bound, upper_bound)

def remove_outliers_iqr(df, column, multiplier=None, inplace=False):
    _, (lower_bound, upper_bound) = detect_outliers_iqr(df, column, multiplier)
    mask = (df[column] >= lower_bound) & (df[column] <= upper_bound)
    if inplace:
        df_result = df
        df_result.drop(df[~mask].index, inplace=True)
    else:
        df_result = df[mask].copy()
    print(f"DataFrame nettoyé: {len(df_result)} lignes conservées ({len(df_result)/len(df)*100:.2f}% des données originales)")
    return df_result

def detect_outliers_zscore(df, column, threshold=3.0):
    if column not in df.columns:
        raise ValueError(f"La colonne '{column}' n'existe pas dans le DataFrame")
    if not np.issubdtype(df[column].dtype, np.number):
        raise TypeError(f"La colonne '{column}' n'est pas numérique")
    z_scores = np.abs((df[column] - df[column].mean()) / df[column].std())
    outliers = df[z_scores > threshold]
    print(f"Détection d'outliers par Z-score pour '{column}':")
    print(f"- Moyenne: {df[column].mean():.2f}, Écart-type: {df[column].std():.2f}")
    print(f"- Seuil Z-score: {threshold}")
    print(f"- {len(outliers)} outliers détectés ({len(outliers)/len(df)*100:.2f}% des données)")
    return outliers

# --- Fonctions de feature_selection.py ---
def select_k_best(X, y, k=10):
    selector = SelectKBest(score_func=f_regression, k=k)
    selector.fit(X, y)
    selected_features = X.columns[selector.get_support()].tolist()
    return selected_features

def recursive_feature_elimination(model, X, y, n_features_to_select=None):
    if n_features_to_select is None:
        n_features_to_select = X.shape[1] // 2
    rfe = RFE(estimator=model, n_features_to_select=n_features_to_select)
    rfe.fit(X, y)
    selected_features = X.columns[rfe.support_].tolist()
    return selected_features

# --- Fonctions de vérification (colonnes, types, NaN) ---
def check_column_names(df_or_path, expected_columns_list=None):
    """
    Vérifie si les colonnes d'un DataFrame ou fichier CSV correspondent à une liste attendue (si fournie).
    Retourne un dict avec les colonnes présentes, manquantes et en trop.
    """
    if isinstance(df_or_path, str):
        df = pd.read_csv(df_or_path)
    else:
        df = df_or_path
    result = {
        'columns_found': list(df.columns),
        'missing_cols': [],
        'extra_cols': []
    }
    if expected_columns_list:
        result['missing_cols'] = [col for col in expected_columns_list if col not in df.columns]
        result['extra_cols'] = [col for col in df.columns if col not in expected_columns_list]
    return result

def check_data_types(df_or_path, expected_dtypes_dict=None):
    """
    Vérifie et retourne les types de données d'un DataFrame ou fichier CSV.
    Compare optionnellement avec un dictionnaire de types attendus. Retourne les incohérences.
    """
    if isinstance(df_or_path, str):
        df = pd.read_csv(df_or_path)
    else:
        df = df_or_path
    dtypes = df.dtypes.apply(str).to_dict()
    mismatches = {}
    if expected_dtypes_dict:
        for col, expected_dtype in expected_dtypes_dict.items():
            if col in df.columns:
                actual_dtype = str(df[col].dtype)
                if actual_dtype != expected_dtype:
                    mismatches[col] = {'expected': expected_dtype, 'actual': actual_dtype}
            else:
                mismatches[col] = {'expected': expected_dtype, 'actual': None}
    return {'dtypes': dtypes, 'mismatches': mismatches}

def check_nan_values(df_or_path, detailed_report=False):
    """
    Vérifie et retourne le nombre et le pourcentage de valeurs NaN par colonne pour un DataFrame ou fichier CSV.
    """
    if isinstance(df_or_path, str):
        df = pd.read_csv(df_or_path)
    else:
        df = df_or_path
    nan_counts = df.isnull().sum()
    nan_percentages = (df.isnull().sum() / len(df)) * 100
    nan_summary = pd.DataFrame({'NaN Count': nan_counts, 'NaN Percentage': nan_percentages})
    columns_with_nan = nan_summary[nan_summary['NaN Count'] > 0]
    result = {
        'columns_with_nan': columns_with_nan if detailed_report else list(columns_with_nan.index),
        'total_columns_with_nan': len(columns_with_nan),
        'mean_nan_percentage': columns_with_nan['NaN Percentage'].mean() if not columns_with_nan.empty else 0.0
    }
    return result

# --- Fonctions de time_features.py ---
def extract_datetime_features(df, column):
    df_copy = df.copy()
    df_copy[column] = pd.to_datetime(df_copy[column])
    df_copy['year'] = df_copy[column].dt.year
    df_copy['month'] = df_copy[column].dt.month
    df_copy['day'] = df_copy[column].dt.day
    df_copy['weekday'] = df_copy[column].dt.weekday
    df_copy['dayofyear'] = df_copy[column].dt.dayofyear
    df_copy['weekofyear'] = df_copy[column].dt.isocalendar().week.astype(int)
    df_copy['quarter'] = df_copy[column].dt.quarter
    return df_copy

def add_lag_features(df, column, lags):
    df_copy = df.copy()
    for lag in lags:
        df_copy[f'{column}_lag_{lag}'] = df_copy[column].shift(lag)
    return df_copy
