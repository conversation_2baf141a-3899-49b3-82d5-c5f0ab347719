#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module de sauvegarde et chargement de modèles et résultats.

Ce module fournit des fonctions pour sauvegarder et recharger des modèles,
résultats ou configurations.
"""

import os
import pickle
import json
import pandas as pd
import numpy as np
from pathlib import Path
import joblib
from datetime import datetime
import logging
from typing import Optional

from .core import REPORTS_DIR

logger = logging.getLogger(__name__)

def get_reports_subdir_path(category: str):
    """Retourne le chemin absolu du sous-dossier d'export dans reports/ (ex: figures, maps, ...). Crée le dossier si besoin."""
    subdir = Path(REPORTS_DIR) / category
    subdir.mkdir(parents=True, exist_ok=True)
    return subdir

def _resolve_export_dir(category: str, notebook_name_for_fallback: Optional[str] = None) -> Path:
    """
    Résout le répertoire d'exportation basé sur la catégorie (structure plate).
    notebook_name_for_fallback: non utilisé dans la version actuelle (structure plate).
    """
    export_dir = get_reports_subdir_path(category)
    return export_dir

def save_model(model, path=None, model_name=None, folder=None, category="models", use_joblib=True, notebook_name=None, export_number=None, base_name=None, ext=None):
    """
    Sauvegarde un modèle entraîné. 'notebook_name' est utilisé pour le fallback de _resolve_export_dir.
    """
    save_to_dir = None
    final_path_str = None

    if path is not None:
        final_path_obj = Path(path)
        save_to_dir = final_path_obj.parent
        # Pas besoin de reconstruire filename si path est fourni, il est complet.
    else:
        if folder is None:
            if not category:
                raise ValueError("L'argument 'category' est obligatoire si 'folder' n'est pas fourni.")
            save_to_dir = _resolve_export_dir(category, notebook_name_for_fallback=notebook_name)
        else:
            save_to_dir = Path(folder)
        # Générer le nom harmonisé
        if notebook_name and export_number is not None and base_name:
            from utils.data_tools import generate_export_filename
            ext_final = ext if ext else ("joblib" if use_joblib else "pkl")
            base_filename = generate_export_filename(notebook_name, export_number, base_name, ext_final)
        elif model_name:
            base_filename = model_name + (".joblib" if use_joblib else ".pkl")
        else:
            raise ValueError("Il faut fournir path OU notebook_name, export_number, base_name OU model_name")
        final_path_obj = save_to_dir / base_filename

    if save_to_dir:
        save_to_dir.mkdir(parents=True, exist_ok=True)
    final_path_str = str(final_path_obj)

    if use_joblib:
        joblib.dump(model, final_path_str)
    else:
        with open(final_path_str, 'wb') as f:
            pickle.dump(model, f)
    logger.info("Modèle sauvegardé: %s", final_path_str)
    return final_path_str

def load_model(path, use_joblib=None):
    """
    Charge un modèle sauvegardé.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"Le fichier {path} n'existe pas.")
    if use_joblib is None:
        use_joblib = path.endswith('.joblib')
    if use_joblib:
        model = joblib.load(path)
    else:
        with open(path, 'rb') as f:
            model = pickle.load(f)
    logger.info("Modèle chargé: %s", path)
    return model

def save_results(results, filename=None, folder=None, category=None, file_format='csv', notebook_name=None, export_number=None, base_name=None, ext=None):
    """
    Sauvegarde des résultats. 'notebook_name' est utilisé pour le fallback de _resolve_export_dir.
    La convention de nommage est la suivante : notebook_name_export_number_base_name.ext.
    """
    results_dir = None
    if folder is None:
        if not category:
            raise ValueError("L'argument 'category' (ex: 'metrics', 'predictions', 'clean_data') est obligatoire si 'folder' n'est pas fourni.")
        results_dir = _resolve_export_dir(category, notebook_name_for_fallback=notebook_name)
    else:
        results_dir = Path(folder)
        results_dir.mkdir(parents=True, exist_ok=True)

    # Générer le nom harmonisé si besoin
    if filename is None and notebook_name and export_number is not None and base_name:
        from utils.data_tools import generate_export_filename
        ext_final = ext if ext else file_format
        filename = generate_export_filename(notebook_name, export_number, base_name, ext_final)
    elif filename is None:
        raise ValueError("Il faut fournir filename OU notebook_name, export_number, base_name")
    clean_filename = filename.replace(' ', '_')

    path_without_ext = results_dir / clean_filename
    path_obj = None
    if file_format.lower() == 'csv':
        path_obj = path_without_ext.with_suffix(".csv")
    elif file_format.lower() == 'json':
        path_obj = path_without_ext.with_suffix(".json")
    elif file_format.lower() == 'excel':
        path_obj = path_without_ext.with_suffix(".xlsx")
    else:
        raise ValueError(f"Format '{file_format}' non reconnu. Utilisez 'csv', 'json' ou 'excel'.")

    path_str = str(path_obj)

    results_to_save = results
    if isinstance(results, str) and file_format.lower() == 'json':
        # Si results est déjà une chaîne (par exemple, une chaîne JSON pré-formatée)
        # et que le format est json, on l'écrit directement.
        with open(path_str, 'w', encoding='utf-8') as f:
            f.write(results)
        logger.info("Résultats (str) sauvegardés directement en JSON: %s", path_str)
        return path_str
    elif isinstance(results, dict):
        if file_format.lower() == 'json':
            with open(path_str, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, default=str)
            logger.info("Résultats (dict) sauvegardés en JSON: %s", path_str)
            return path_str
        else:
            try:
                results_to_save = pd.DataFrame.from_dict(results, orient='index')
                if results_to_save.shape[1] == 1 and results_to_save.columns[0] == 0:
                    results_to_save.columns = ['value']
                results_to_save = results_to_save.reset_index().rename(columns={'index': 'key'})
            except Exception as e:
                logger.warning("Conversion dict -> DataFrame échouée pour %s: %s. Tentative avec pd.Series.", file_format, e)
                results_to_save = pd.Series(results).to_frame(name='value').reset_index().rename(columns={'index': 'key'})
    elif not isinstance(results, (pd.DataFrame, pd.Series)):
        try:
            results_to_save = pd.DataFrame(results)
        except Exception as e:
            raise TypeError(f"Type de résultats {type(results)} non supporté pour la sauvegarde directe en {file_format}. Erreur: {e}") from e

    if file_format.lower() == 'csv':
        results_to_save.to_csv(path_str, index=False)
    elif file_format.lower() == 'json':
        results_to_save.to_json(path_str, orient='records', indent=4)
    elif file_format.lower() == 'excel':
        results_to_save.to_excel(path_str, index=False)

    logger.info("Résultats sauvegardés: %s", path_str)
    return path_str

def load_results(path):
    """
    Charge des résultats depuis un fichier.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"Le fichier {path} n'existe pas.")
    file_ext = os.path.splitext(path)[1].lower()
    results = None
    if file_ext == '.csv':
        results = pd.read_csv(path)
    elif file_ext == '.json':
        results = pd.read_json(path)
    elif file_ext in ['.xlsx', '.xls']:
        results = pd.read_excel(path)
    else:
        raise ValueError(f"Format de fichier '{file_ext}' non reconnu pour le chargement.")
    logger.info("Résultats chargés: %s", path)
    return results

def save_figure(fig, filename=None, category="figures", folder=None, notebook_name=None, export_number=None, base_name=None, ext="png", **kwargs):
    """
    Sauvegarde une figure dans reports/figures/ (ou autre catégorie). Le nom de fichier est normalisé.
    Utilise la fonction centralisée export_figure de clustering_visualization.py si notebook_name, export_number et base_name sont fournis.
    """
    if notebook_name and export_number is not None and base_name:
        from utils.clustering_visualization import export_figure
        return export_figure(fig, notebook_name, export_number, base_name, ext=ext, export_type=category, **kwargs)
    # Sinon, fallback sur la logique existante
    if filename is None:
        raise ValueError("Il faut fournir filename OU notebook_name, export_number, base_name")
    if folder is None:
        folder = _resolve_export_dir(category, notebook_name_for_fallback=notebook_name)
    else:
        folder = Path(folder)
        folder.mkdir(parents=True, exist_ok=True)
    path = Path(folder) / filename
    fig.savefig(str(path), dpi=kwargs.get('dpi', 300), bbox_inches='tight', **kwargs)
    logger.info("Figure sauvegardée: %s", str(path))
    return str(path)

def save_config(config, filename=None):
    """
    Sauvegarde une configuration dans un fichier JSON. (Chemin non spécifique au notebook par défaut)
    """
    config_dir = Path(REPORTS_DIR) / 'configs'
    config_dir.mkdir(parents=True, exist_ok=True)

    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"config_{timestamp}"
    if not filename.endswith(".json"):
        filename = f"{filename}.json"

    path_str = str(config_dir / filename)

    def json_serializable(obj):
        if isinstance(obj, np.integer): return int(obj)
        elif isinstance(obj, np.floating): return float(obj)
        elif isinstance(obj, np.ndarray): return obj.tolist()
        elif isinstance(obj, datetime): return obj.isoformat()
        elif isinstance(obj, Path): return str(obj)
        try:
            return str(obj)
        except Exception:
            return f"Objet non sérialisable de type {type(obj)}"

    with open(path_str, 'w', encoding='utf-8') as f:
        json.dump(config, f, default=json_serializable, indent=4)
    logger.info("Configuration sauvegardée: %s", path_str)
    return path_str

def load_config(path):
    """
    Charge une configuration depuis un fichier JSON.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"Le fichier {path} n'existe pas.")
    with open(path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    logger.info("Configuration chargée: %s", path)
    return config

def save_folium_map(map_obj, filename=None, category='maps', notebook_name=None, export_number=None, base_name=None, ext=None):
    """
    Sauvegarde une carte Folium dans reports/maps/. Le nom de fichier est harmonisé automatiquement si non fourni.
    """
    print("DEBUG EXPORT FOLIUM:", filename, notebook_name, export_number, base_name, ext)
    from utils.data_tools import generate_export_filename
    if not filename or filename.strip() == "":
        ext = ext or 'html'
        if notebook_name and export_number is not None and base_name:
            filename = generate_export_filename(notebook_name, export_number, base_name, ext)
        else:
            raise ValueError("Il faut fournir filename OU notebook_name, export_number, base_name")
    else:
        root, _ = os.path.splitext(filename)
        base = os.path.basename(root)
        ext = ext or 'html'
        filename = f"{base}.{ext}"
    export_dir = get_reports_subdir_path('maps')
    export_dir.mkdir(parents=True, exist_ok=True)
    save_path_str = str(export_dir / filename)
    map_obj.save(save_path_str)
    logger.info("Carte Folium sauvegardée: %s", save_path_str)
    return save_path_str


def save_plotly_map(fig_obj, filename=None, category='maps', plot_type='html', notebook_name=None, export_number=None, base_name=None, ext=None):
    """
    Sauvegarde une carte/figure Plotly dans reports/maps/. Le nom de fichier est harmonisé automatiquement si non fourni.
    """
    print("DEBUG EXPORT PLOTLY:", filename, notebook_name, export_number, base_name, ext)
    from utils.data_tools import generate_export_filename
    ext = ext or ('html' if plot_type == 'html' else 'png')
    if not filename or filename.strip() == "":
        if notebook_name and export_number is not None and base_name:
            filename = generate_export_filename(notebook_name, export_number, base_name, ext)
        else:
            raise ValueError("Il faut fournir filename OU notebook_name, export_number, base_name")
    else:
        root, _ = os.path.splitext(filename)
        base = os.path.basename(root)
        filename = f"{base}.{ext}"
    export_dir = get_reports_subdir_path('maps')
    export_dir.mkdir(parents=True, exist_ok=True)
    save_path_str = str(export_dir / filename)
    if plot_type == 'html':
        fig_obj.write_html(save_path_str)
    else:
        try:
            fig_obj.write_image(save_path_str)
        except ValueError as e:
            logger.error("Erreur lors de la sauvegarde de l'image Plotly (%s): %s", save_path_str, e)
            logger.error("Assurez-vous que 'kaleido' est installé ('pip install kaleido') pour l'export d'images statiques.")
            return None
    logger.info("Carte/Figure Plotly sauvegardée: %s", save_path_str)
    return save_path_str

def generate_co2_feature_importance(model_co2, X_co2):
    """
    Génère et sauvegarde l'importance des caractéristiques pour le modèle CO2.
    Args:
        model_co2: Modèle CO2 entraîné (ex: scikit-learn).
        X_co2 (pd.DataFrame): DataFrame des caractéristiques utilisées.
    """
    if hasattr(model_co2, 'feature_importances_'):
        importances = model_co2.feature_importances_
        feature_names = X_co2.columns
        feature_importance_df = pd.DataFrame({'feature': feature_names, 'importance': importances})
        feature_importance_df = feature_importance_df.sort_values(by='importance', ascending=False)
        print("Importance des caractéristiques pour le modèle CO2:")
        print(feature_importance_df)
        # Optionnel : sauvegarde automatique
        # feature_importance_df.to_csv('co2_feature_importance.csv', index=False)
        return feature_importance_df
    else:
        print("La méthode de génération d'importance des caractéristiques n'est pas disponible pour ce type de modèle CO2.")
        return None

def generate_energy_feature_importance(model_energy, X_energy):
    """
    Génère et sauvegarde l'importance des caractéristiques pour le modèle énergie.
    Args:
        model_energy: Modèle énergie entraîné (ex: scikit-learn).
        X_energy (pd.DataFrame): DataFrame des caractéristiques utilisées.
    """
    if hasattr(model_energy, 'feature_importances_'):
        importances = model_energy.feature_importances_
        feature_names = X_energy.columns
        feature_importance_df = pd.DataFrame({'feature': feature_names, 'importance': importances})
        feature_importance_df = feature_importance_df.sort_values(by='importance', ascending=False)
        print("Importance des caractéristiques pour le modèle Énergie:")
        print(feature_importance_df)
        # Optionnel : sauvegarde automatique
        # feature_importance_df.to_csv('energy_feature_importance.csv', index=False)
        return feature_importance_df
    else:
        print("La méthode de génération d'importance des caractéristiques n'est pas disponible pour ce type de modèle énergie.")
        return None
