from typing import Dict, List, Optional, Tuple, Any

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.figure import Figure
import logging

# Configuration du logger
logger = logging.getLogger(__name__)

# ============================================================================
# Fonctions héritées d'eda_tools.py - Gestion des valeurs manquantes
# ============================================================================

def handle_missing_values(
    df: pd.DataFrame,
    name: str | None = None,
    strategy: str = "default",
) -> pd.DataFrame:
    """Traite les valeurs manquantes d'un DataFrame selon une stratégie.

    Paramètres
    ----------
    df : pd.DataFrame
        Jeu de données à nettoyer.
    name : str | None, optional
        Nom du dataframe (pour les logs), by default None
    strategy : {"default", "drop", "fill"}, optional
        Stratégie de nettoyage :
        - "default" : heuristique intelligente (dates forward-fill, mediane, mode)
        - "drop"    : supprime les lignes contenant des NaN
        - "fill"    : impute *toutes* les colonnes numériques par médiane et les
                       catégorielles par mode

    Retour
    ------
    pd.DataFrame
        DataFrame nettoyé.
    """
    df_cleaned = df.copy()
    name = name or "<df>"

    missing_before = df_cleaned.isna().sum().sum()
    if missing_before == 0:
        logger.info("%s : aucune valeur manquante détectée", name)
        return df_cleaned

    logger.info(
        "Traitement des NaN pour %s – stratégie %s (%d NaN)",
        name,
        strategy,
        missing_before,
    )

    if strategy == "drop":
        return df_cleaned.dropna()

    if strategy in {"default", "fill"}:
        # Dates : forward-fill
        date_cols = df_cleaned.select_dtypes(include=["datetime", "datetimetz"]).columns
        for col in date_cols:
            if df_cleaned[col].isna().any():
                df_cleaned[col] = df_cleaned[col].ffill()

        # Numériques
        num_cols = df_cleaned.select_dtypes(include=[np.number]).columns
        for col in num_cols:
            if df_cleaned[col].isna().any():
                df_cleaned[col] = df_cleaned[col].fillna(df_cleaned[col].median())

        # Catégorielles
        cat_cols = df_cleaned.select_dtypes(include=["object", "category"]).columns
        for col in cat_cols:
            if df_cleaned[col].isna().any():
                mode_val = (
                    df_cleaned[col].mode().iloc[0]
                    if not df_cleaned[col].mode().empty
                    else "Unknown"
                )
                df_cleaned[col] = df_cleaned[col].fillna(mode_val)
    else:
        raise ValueError(f"Stratégie inconnue : {strategy}")

    logger.info(
        "%s : %d NaN restants après traitement", name, df_cleaned.isna().sum().sum()
    )
    return df_cleaned


def validate_business_constraints(
    orders_df: pd.DataFrame,
    payments_df: pd.DataFrame,
) -> Dict[str, Any]:
    """Valide quelques règles métier simples et retourne un rapport."""
    report = {"errors": [], "warnings": [], "info": []}

    # Validation basique des contraintes métier
    if 'order_purchase_timestamp' in orders_df.columns:
        # Vérification des dates de commande
        invalid_dates = orders_df[orders_df['order_purchase_timestamp'].isna()]
        if not invalid_dates.empty:
            report["errors"].append(f"Dates de commande manquantes: {len(invalid_dates)} lignes")

    if 'payment_value' in payments_df.columns:
        # Vérification des montants négatifs
        negative_payments = payments_df[payments_df['payment_value'] < 0]
        if not negative_payments.empty:
            report["errors"].append(f"Montants de paiement négatifs: {len(negative_payments)} lignes")

    return report

# ============================================================================
# Fonctions d'analyse exploratoire existantes
# ============================================================================

def analyze_segment_profiles(
    segments_df: pd.DataFrame,
    cluster_col: str = "cluster",
    numeric_cols: Optional[List[str]] = None,
    categorical_cols: Optional[List[str]] = None,
) -> Dict[int, Dict[str, float]]:
    """
    Analyse statistique détaillée des segments clients.

    Args:
        segments_df: DataFrame contenant les données segmentées
        cluster_col: Nom de la colonne contenant les labels de cluster
        numeric_cols: Liste des colonnes numériques à analyser
        categorical_cols: Liste des colonnes catégorielles à analyser

    Returns:
        Dictionnaire contenant les statistiques par segment
    """
    if numeric_cols is None:
        numeric_cols = segments_df.select_dtypes(include=[np.number]).columns.tolist()
        if cluster_col in numeric_cols:
            numeric_cols.remove(cluster_col)

    segment_stats = {}

    for cluster_id in segments_df[cluster_col].unique():
        cluster_data = segments_df[segments_df[cluster_col] == cluster_id]

        stats = {
            "size": len(cluster_data),
            "percentage": len(cluster_data) / len(segments_df) * 100,
        }

        # Statistiques numériques
        for col in numeric_cols:
            if col in cluster_data.columns:
                stats[f"{col}_mean"] = float(cluster_data[col].mean())
                stats[f"{col}_median"] = float(cluster_data[col].median())
                stats[f"{col}_std"] = float(cluster_data[col].std())
                stats[f"{col}_min"] = float(cluster_data[col].min())
                stats[f"{col}_max"] = float(cluster_data[col].max())

        # Statistiques catégorielles
        if categorical_cols:
            for col in categorical_cols:
                if col in cluster_data.columns:
                    value_counts = cluster_data[col].value_counts(normalize=True)
                    stats[f"{col}_top"] = value_counts.idxmax()
                    stats[f"{col}_top_pct"] = float(value_counts.max() * 100)

        segment_stats[cluster_id] = stats

    return segment_stats


def compare_segments(
    segments_df: pd.DataFrame,
    reference_segment: int,
    comparison_segment: int,
    features: List[str],
    cluster_col: str = "cluster",
) -> pd.DataFrame:
    """
    Compare deux segments sur les features spécifiées.

    Args:
        segments_df: DataFrame contenant les données segmentées
        reference_segment: ID du segment de référence
        comparison_segment: ID du segment à comparer
        features: Liste des features à comparer
        cluster_col: Nom de la colonne contenant les labels de cluster

    Returns:
        DataFrame avec les comparaisons statistiques
    """
    # Créer un DataFrame avec les moyennes par segment
    segment_means = segments_df.groupby(cluster_col)[features].mean().T

    # Extraire les valeurs pour les segments comparés
    ref_values = segment_means[reference_segment]
    comp_values = segment_means[comparison_segment]

    # Calculer les différences
    abs_diff = np.abs(ref_values - comp_values)
    rel_diff = (abs_diff / ref_values) * 100

    # Construire le DataFrame de résultat
    comparison = pd.DataFrame(
        {
            "feature": features,
            f"segment_{reference_segment}_mean": ref_values,
            f"segment_{comparison_segment}_mean": comp_values,
            "absolute_difference": abs_diff,
            "relative_difference": rel_diff,
        }
    )

    return comparison.reset_index(drop=True)


def plot_segment_comparison(
    segments_df: pd.DataFrame,
    features: List[str],
    cluster_col: str = "cluster",
    figsize: Tuple[int, int] = (12, 8),
) -> Figure:
    """
    Crée un graphique comparant les segments sur les features spécifiées.

    Args:
        segments_df: DataFrame contenant les données segmentées
        features: Liste des features à comparer
        cluster_col: Nom de la colonne contenant les labels de cluster
        figsize: Taille de la figure

    Returns:
        Figure matplotlib
    """
    fig, ax = plt.subplots(figsize=figsize)

    # Standardiser les données pour une comparaison équitable
    normalized = segments_df.copy()
    for feature in features:
        normalized[feature] = (
            normalized[feature] - normalized[feature].mean()
        ) / normalized[feature].std()

    # Calculer les moyennes par segment
    segment_means = normalized.groupby(cluster_col)[features].mean().T

    # Tracer le graphique
    segment_means.plot(kind="bar", ax=ax)

    ax.set_title("Comparaison des segments par feature")
    ax.set_ylabel("Valeur standardisée")
    ax.legend(title="Segment")

    return fig

# ============================================================================
# Fonctions de validation et d'analyse temporelle (héritées d'eda_tools.py)
# ============================================================================

def analyze_temporal_patterns(
    df: pd.DataFrame,
    date_column: str,
    value_column: Optional[str] = None,
    groupby_freq: str = "M"
) -> Dict[str, Any]:
    """Analyse les patterns temporels dans les données."""
    df_temp = df.copy()
    df_temp[date_column] = pd.to_datetime(df_temp[date_column])

    # Grouper par période
    if value_column:
        temporal_analysis = df_temp.groupby(pd.Grouper(key=date_column, freq=groupby_freq))[value_column].agg(['count', 'sum', 'mean'])
    else:
        temporal_analysis = df_temp.groupby(pd.Grouper(key=date_column, freq=groupby_freq)).size()

    return {"temporal_data": temporal_analysis, "freq": groupby_freq}


def visualize_and_detect_outliers(df: pd.DataFrame, column: str, method: str = "iqr") -> pd.DataFrame:
    """Détecte et visualise les outliers avec boxplot et histogramme."""
    from .data_tools import detect_outliers_iqr, detect_outliers_zscore

    if method == "iqr":
        outliers, _ = detect_outliers_iqr(df, column)
        return outliers
    elif method == "zscore":
        return detect_outliers_zscore(df, column)
    else:
        raise ValueError("Méthode non supportée. Utilisez 'iqr' ou 'zscore'")


# ============================================================================
# Fonctions d'analyse descriptive des données manquantes
# ============================================================================

def describe_missing_data(df: pd.DataFrame) -> pd.DataFrame:
    """Analyse complète des valeurs manquantes."""
    missing = pd.DataFrame({
        'colonne': df.columns,
        'type': df.dtypes,
        'nb_manquants': df.isna().sum(),
        'pct_manquants': (df.isna().sum() / len(df) * 100).round(2)
    })
    missing = missing.sort_values('pct_manquants', ascending=False).reset_index(drop=True)

    print(f"Résumé des valeurs manquantes ({len(df)} lignes au total):")
    print(f"- {missing['nb_manquants'].sum()} valeurs manquantes au total")
    print(f"- {missing[missing['nb_manquants'] > 0].shape[0]} colonnes avec au moins une valeur manquante")
    print(f"- {missing[missing['pct_manquants'] > 50].shape[0]} colonnes avec plus de 50% de valeurs manquantes")

    return missing


def plot_missing_heatmap(df: pd.DataFrame, figsize: Tuple[int, int] = (14, 10)) -> Figure:
    """Affiche une heatmap des valeurs manquantes."""
    fig, ax = plt.subplots(figsize=figsize)
    sns.heatmap(df.isnull(), cbar=False, cmap="viridis", ax=ax)
    ax.set_title("Heatmap des valeurs manquantes")
    return fig


def plot_correlation_matrix(df: pd.DataFrame, method: str = 'pearson',
                          figsize: Tuple[int, int] = (14, 12),
                          mask_upper: bool = True) -> Tuple[Figure, pd.DataFrame]:
    """Affiche et retourne la matrice de corrélation."""
    corr_matrix = df.corr(method=method)  # type: ignore
    mask = None
    if mask_upper:
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))

    fig, ax = plt.subplots(figsize=figsize)
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm',
                ax=ax, fmt='.2f', square=True, cbar=True)
    ax.set_title("Matrice de corrélation")
    plt.tight_layout()

    return fig, corr_matrix


def plot_feature_distributions(df: pd.DataFrame, columns: Optional[List[str]] = None,
                             figsize: Tuple[int, int] = (16, 12),
                             bins: int = 20) -> Figure:
    """Affiche les distributions des features numériques."""
    if columns is None:
        columns = df.select_dtypes(include='number').columns.tolist()

    n_cols = 2
    n_rows = (len(columns) + 1) // n_cols
    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)
    axes = axes.flatten()

    i = -1  # Initialize i in case columns is empty
    for i, col in enumerate(columns):
        sns.histplot(data=df, x=col, bins=bins, kde=True, ax=axes[i])
        axes[i].set_title(col)

    # Supprimer les axes vides
    for j in range(i+1, len(axes)):
        fig.delaxes(axes[j])

    plt.tight_layout()
    return fig

# ============================================================================
# Fonctions d'analyse de segments existantes (conservées)
# ============================================================================

def analyze_segments(
    segments_df: pd.DataFrame,
    cluster_col: str = "cluster",
    numeric_cols: Optional[List[str]] = None,
    categorical_cols: Optional[List[str]] = None,
) -> Dict[int, Dict[str, float]]:
    """
    Analyse statistique détaillée des segments clients.

    Args:
        segments_df: DataFrame contenant les données segmentées
        cluster_col: Nom de la colonne contenant les labels de cluster
        numeric_cols: Liste des colonnes numériques à analyser
        categorical_cols: Liste des colonnes catégorielles à analyser

    Returns:
        Dictionnaire contenant les statistiques par segment
    """
    if numeric_cols is None:
        numeric_cols = segments_df.select_dtypes(include=[np.number]).columns.tolist()
        if cluster_col in numeric_cols:
            numeric_cols.remove(cluster_col)

    segment_stats = {}

    for cluster_id in segments_df[cluster_col].unique():
        cluster_data = segments_df[segments_df[cluster_col] == cluster_id]

        stats = {
            "size": len(cluster_data),
            "percentage": len(cluster_data) / len(segments_df) * 100,
        }

        # Statistiques numériques
        for col in numeric_cols:
            if col in cluster_data.columns:
                stats[f"{col}_mean"] = float(cluster_data[col].mean())
                stats[f"{col}_median"] = float(cluster_data[col].median())
                stats[f"{col}_std"] = float(cluster_data[col].std())
                stats[f"{col}_min"] = float(cluster_data[col].min())
                stats[f"{col}_max"] = float(cluster_data[col].max())

        # Statistiques catégorielles
        if categorical_cols:
            for col in categorical_cols:
                if col in cluster_data.columns:
                    value_counts = cluster_data[col].value_counts(normalize=True)
                    stats[f"{col}_top"] = value_counts.idxmax()
                    stats[f"{col}_top_pct"] = float(value_counts.max() * 100)

        segment_stats[cluster_id] = stats

    return segment_stats


# ============================================================================
# Fonctions d'affichage avancé (héritées d'utils_general.py)
# ============================================================================

def display_side_by_side(dfs: List[pd.DataFrame], captions: Optional[List[str]] = None, widths: Optional[List[str]] = None):
    """
    Affiche plusieurs DataFrames côte à côte.

    Args:
        dfs (list): Liste de DataFrames à afficher
        captions (list, optional): Liste des titres pour chaque DataFrame. Par défaut None.
        widths (list, optional): Liste des largeurs pour chaque DataFrame. Par défaut None.
    """
    from IPython.display import display, HTML

    if captions is None:
        captions = [f'DataFrame {i+1}' for i in range(len(dfs))]

    if widths is None:
        widths = [f'{100/len(dfs)}%' for _ in range(len(dfs))]

    html = '<div style="display:flex">'

    for i, (df, caption, width) in enumerate(zip(dfs, captions, widths)):
        html += f'<div style="flex:{width}">'
        html += f'<h3>{caption}</h3>'
        html += df.to_html()
        html += '</div>'

    html += '</div>'

    display(HTML(html))
