#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module core - Configuration globale et initialisation du projet.

Ce module centralise :
1. Les constantes, chemins et paramètres globaux du projet
2. Les fonctions d'initialisation des notebooks
3. Les utilitaires de base (Colors, logging, etc.)
4. La gestion des dossiers et de l'environnement
"""

import os
import sys
import warnings
import random
import logging
import shutil
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Optional, Tuple, Dict, Any, Literal

# ============================================================================
# CONFIGURATION GLOBALE DU PROJET
# ============================================================================

# Chemin racine du projet (2 niveaux au-dessus de ce fichier)
PROJECT_ROOT = Path(os.path.dirname(os.path.abspath(__file__))).parent

# Chemins vers les données
DATA_DIR = PROJECT_ROOT / "data"
RAW_DATA_DIR = DATA_DIR / "raw"
PROCESSED_DATA_DIR = DATA_DIR / "clean"
FEATURES_DATA_DIR = DATA_DIR / "features"  # Dossier pour les features au même niveau que clean

# Chemin vers le fichier de données principal
MAIN_DATA_FILE = RAW_DATA_DIR / "olist.db"

# Chemins vers les dossiers de sortie
MODELS_DIR = PROJECT_ROOT / "models"
REPORTS_DIR = PROJECT_ROOT / "reports"
OUTPUTS_DIR = PROJECT_ROOT / "outputs"

# Graine aléatoire pour la reproductibilité
SEED = 42

# Configuration d'exportation par défaut
EXPORT_CONFIG = {
    "figures": True,  # Activer/désactiver la sauvegarde des figures
    "maps": True,     # Activer/désactiver la sauvegarde des cartes
    "models": True,   # Activer/désactiver la sauvegarde des modèles
    "data": True,     # Activer/désactiver la sauvegarde des données (CSV, etc.)
}
OVERWRITE_EXISTING_FILES = False # Comportement par défaut pour l'écrasement des fichiers

# Paramètres de division des données
TEST_SIZE = 0.2
VALIDATION_SIZE = 0.25  # 25% des données d'entraînement

# Colonnes cibles (adaptées au projet e-commerce)
TARGET_COLUMNS = ["customer_unique_id", "order_id"]

# Seuils pour la détection des outliers
OUTLIER_IQR_MULTIPLIER = 1.5

# Métriques d'évaluation
METRICS = ["silhouette", "inertia", "calinski_harabasz"]

# ============================================================================
# FONCTIONS DE CONFIGURATION DES DOSSIERS
# ============================================================================

def get_exports_paths():
    """
    Retourne les chemins d'export principaux du projet (figures, rapports, données nettoyées).
    """
    return {
        "reports": str(REPORTS_DIR),
        "figures": str(REPORTS_DIR / "figures"),
        "clean_data": str(PROCESSED_DATA_DIR / "clean_data.csv"),
    }


def create_directories():
    """Crée les dossiers de base nécessaires s'ils n'existent pas déjà."""
    # Dossiers principaux
    base_dirs = [
        DATA_DIR / "raw",
        DATA_DIR / "clean",
        REPORTS_DIR
    ]
    for directory in base_dirs:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"Dossier assuré : {directory}")

    # Déplacer le contenu de data/clean/features/ vers data/features/ s'il existe
    source_features_dir = DATA_DIR / "clean" / "features"
    target_features_dir = DATA_DIR / "features"

    if source_features_dir.exists():
        if not target_features_dir.exists():
            target_features_dir.mkdir(parents=True, exist_ok=True)
            print(f"Dossier créé : {target_features_dir}")

        files_moved = False
        for file_path in source_features_dir.glob('*'):
            destination_path = target_features_dir / file_path.name
            try:
                if file_path.is_file():
                    shutil.copy2(str(file_path), str(destination_path))
                elif file_path.is_dir():
                    shutil.copytree(str(file_path), str(destination_path), dirs_exist_ok=True)
                print(f"Déplacé/Copié : {file_path.name} vers {target_features_dir}")
                files_moved = True
            except Exception as e:
                print(f"Erreur lors du déplacement/copie de {file_path.name}: {e}")

        if files_moved:
            try:
                shutil.rmtree(source_features_dir)
                print(f"Dossier source supprimé : {source_features_dir}")
            except Exception as e:
                print(f"Erreur lors de la suppression de {source_features_dir}: {e}")
        else:
            print(f"Aucun fichier/dossier à déplacer depuis {source_features_dir}.")
    else:
        print(f"Le dossier source {source_features_dir} n'existe pas, rien à déplacer.")

# ============================================================================
# CLASSES ET FONCTIONS D'INITIALISATION DES NOTEBOOKS
# ============================================================================

logger = logging.getLogger(__name__)

# Initialiser les booléens de disponibilité des cartes à False par défaut
FOLIUM_AVAILABLE = False
PLOTLY_AVAILABLE = False
MAPS_AVAILABLE = False


class Colors:
    """Classe pour les codes couleur ANSI."""

    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    INFO = '\033[94m'

    @staticmethod
    def is_notebook():
        """Détecte si le code s'exécute dans un notebook Jupyter."""
        try:
            # Utilisation de la fonction get_ipython qui existe dans les environnements IPython/Jupyter
            shell = get_ipython().__class__.__name__  # type: ignore
            if shell == 'ZMQInteractiveShell':
                return True   # Jupyter notebook ou qtconsole
            elif shell == 'TerminalInteractiveShell':
                return False  # Terminal IPython
            else:
                return False  # Autre
        except (NameError, ImportError):
            return False      # Probablement pas dans un environnement IPython

    @staticmethod
    def colorize(message: str, color_code: str) -> str:
        """Applique une couleur à un message."""
        return f"{color_code}{message}{Colors.ENDC}"


def log_message(message: str, color: str = Colors.INFO, bold: bool = False, underline: bool = False) -> None:
    """
    Affiche un message avec une couleur et un style optionnels.

    Args:
        message: Message à afficher
        color: Code couleur ANSI
        bold: Applique le style gras
        underline: Applique le style souligné
    """
    style = ""
    if bold:
        style += Colors.BOLD
    if underline:
        style += Colors.UNDERLINE

    formatted_message = f"{style}{color}{message}{Colors.ENDC}"
    print(formatted_message)


def set_display_options(max_rows: int = 100, max_columns: int = 100, width: int = 1000, precision: int = 4) -> None:
    """
    Configure les options d'affichage de pandas pour une meilleure lisibilité.

    Args:
        max_rows: Nombre maximum de lignes à afficher
        max_columns: Nombre maximum de colonnes à afficher
        width: Largeur maximale d'affichage
        precision: Précision des nombres à virgule flottante
    """
    pd.set_option('display.max_rows', max_rows)
    pd.set_option('display.max_columns', max_columns)
    pd.set_option('display.width', width)
    pd.set_option('display.precision', precision)
    pd.set_option('display.float_format', lambda x: f'{x:.{precision}f}')

    print("Options d'affichage pandas configurées:")
    print(f"- max_rows: {max_rows}")
    print(f"- max_columns: {max_columns}")
    print(f"- width: {width}")
    print(f"- precision: {precision}")


def create_directory(path: str) -> None:
    """
    Crée un répertoire s'il n'existe pas déjà.

    Args:
        path: Chemin du répertoire à créer
    """
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Répertoire créé: {path}")
    else:
        print(f"Répertoire existant: {path}")


def setup_notebook() -> Dict[str, Any]:
    """Fonction de configuration des dossiers d'export."""
    from .save_load import get_reports_subdir_path

    export_dirs = {
        "base_export": None,
        "figures": get_reports_subdir_path("figures"),
        "maps": get_reports_subdir_path("maps"),
        "models": get_reports_subdir_path("models"),
    }
    logger.info("Chemin d'export des figures : %s", export_dirs['figures'])
    return export_dirs


def print_export_config() -> None:
    """Affiche la configuration d'export."""
    from .save_load import get_reports_subdir_path

    log_message(
        f"Export config: Figures: {get_reports_subdir_path('figures')}, "
        f"Maps: {get_reports_subdir_path('maps')}, "
        f"Models: {get_reports_subdir_path('models')}",
        Colors.INFO
    )


def setup_notebook_env(
    style: Literal["white", "dark", "whitegrid", "darkgrid", "ticks"] = "whitegrid",
    fig_size: Tuple[int, int] = (12, 8),
    random_seed: int = 42
) -> Dict[str, Any]:
    """
    Configuration avancée de l'environnement du notebook.

    Args:
        style: Style seaborn à appliquer
        fig_size: Taille par défaut des figures
        random_seed: Graine pour la reproductibilité

    Returns:
        Dictionnaire des chemins d'export configurés
    """
    logger.info("Configuration de l'environnement du notebook (setup_notebook_env)...")

    # Configuration de la graine aléatoire
    np.random.seed(random_seed)
    random.seed(random_seed)

    # Configuration matplotlib
    plt.rcParams['figure.figsize'] = fig_size
    plt.rcParams['font.size'] = 12

    # Configuration seaborn
    sns.set_style(style)
    sns.set_palette("husl")

    # Configuration des warnings
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=UserWarning)

    # Création des dossiers d'export
    created_export_dirs = setup_notebook()

    return created_export_dirs


def check_libraries() -> None:
    """Vérifie la disponibilité des bibliothèques."""
    global FOLIUM_AVAILABLE, PLOTLY_AVAILABLE, MAPS_AVAILABLE

    print("Vérification des bibliothèques disponibles:")
    print("=" * 50)

    # Bibliothèques de base
    try:
        print(f"- Python: {sys.version.split()[0]}")
        print(f"- NumPy: {np.__version__}")
        print(f"- Pandas: {pd.__version__}")
    except AttributeError:
        print("- Erreur lors de la vérification des versions")

    try:
        import matplotlib
        print(f"- Matplotlib: {matplotlib.__version__}")
    except ImportError:
        print("- Matplotlib: Non disponible")

    try:
        print(f"- Seaborn: {getattr(sns, '__version__', 'Version inconnue')}")
    except (ImportError, AttributeError):
        print("- Seaborn: Version non détectable")

    # Vérifier les bibliothèques optionnelles
    try:
        import sklearn
        print(f"- Scikit-learn: {sklearn.__version__}")
    except ImportError:
        print("- Scikit-learn: Non disponible")

    # Vérifier les bibliothèques de visualisation cartographique
    try:
        import folium  # noqa: F401
        print("- Folium: Disponible")
        FOLIUM_AVAILABLE = True
    except (ImportError, ModuleNotFoundError):
        print("- Folium: Non disponible")

    try:
        import plotly
        print(f"- Plotly: {plotly.__version__}")
        PLOTLY_AVAILABLE = True
    except ImportError:
        print("- Plotly: Non disponible")

    MAPS_AVAILABLE = FOLIUM_AVAILABLE or PLOTLY_AVAILABLE

    print("=" * 50)


def init_notebook(
    notebook_file_path: Optional[str] = None,
    style: Literal["white", "dark", "whitegrid", "darkgrid", "ticks"] = "whitegrid",
    figsize: Tuple[int, int] = (12, 8),
    random_seed: Optional[int] = None,
    setup: bool = True,
    auto_save: bool = True,
    check_deps: bool = True,
    overwrite_existing: bool = False
) -> None:
    """
    Initialise l'environnement d'un notebook avec toutes les configurations standards.

    Args:
        notebook_file_path: Chemin du fichier notebook
        style: Style seaborn à utiliser
        figsize: Taille par défaut des figures matplotlib
        random_seed: Graine pour la reproductibilité (défaut: config.SEED)
        setup: Activer la configuration des dossiers d'export
        auto_save: Activer la sauvegarde automatique
        check_deps: Vérifier les dépendances
        overwrite_existing: Autoriser l'écrasement des fichiers existants
    """
    # Utiliser la graine du fichier config si non spécifiée
    effective_seed = random_seed if random_seed is not None else SEED

    print("=" * 50)
    log_message("🚀 INITIALISATION DU NOTEBOOK", Colors.HEADER, bold=True)
    print("=" * 50)

    if notebook_file_path:
        log_message(f"Notebook: {notebook_file_path}", Colors.INFO)

    log_message(f"Graine aléatoire: {effective_seed}", Colors.INFO)
    log_message(f"Style seaborn: {style}", Colors.INFO)
    log_message(f"Taille des figures: {figsize}", Colors.INFO)

    # Vérification des dépendances
    if check_deps:
        check_libraries()

    # Affichage des informations sur les cartes
    if MAPS_AVAILABLE:
        log_message("Visualisations cartographiques interactives DISPONIBLES.", Colors.OKGREEN)
    else:
        log_message("Visualisations cartographiques interactives NON disponibles.", Colors.WARNING)
        log_message("Des visualisations statiques seront créées si possible. Exécutez './install_viz_deps.sh' pour les interactives.", Colors.WARNING)

    # Configuration de l'affichage
    set_display_options()

    # Configuration de l'environnement si demandé
    if setup:
        try:
            log_message("Appel de setup_notebook_env pour configurer les dossiers...", Colors.INFO)
            created_dirs = setup_notebook_env(
                style=style,
                fig_size=figsize,
                random_seed=effective_seed
            )

            if created_dirs and isinstance(created_dirs, dict):
                log_message("\nDossiers d'export configurés par setup_notebook_env:", Colors.HEADER)
                for dir_type, dir_path in created_dirs.items():
                    log_message(f"- {dir_type}: {dir_path}", Colors.OKGREEN)
            else:
                log_message("Configuration des dossiers terminée", Colors.INFO)

        except Exception as e:
            log_message(f"Erreur lors de la configuration des dossiers: {e}", Colors.FAIL)

    # Affichage de la configuration d'export
    print_export_config()

    # Configuration de la sauvegarde automatique
    if auto_save:
        log_message(f"Sauvegarde automatique des figures activée (écraser existants: {overwrite_existing}).", Colors.OKGREEN)
    else:
        log_message("Sauvegarde automatique désactivée.", Colors.WARNING)

    print("\n" + "="*50)
    print()


# ============================================================================
# INITIALISATION DU MODULE
# ============================================================================

# Exécuter la création des dossiers lors de l'import
create_directories()
