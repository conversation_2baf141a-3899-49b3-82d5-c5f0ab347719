-- Re<PERSON><PERSON><PERSON><PERSON> SQL pour le dashboard Olist
-- Auteur: <PERSON>e Nejad
-- Date: 28 mai 2024

-- Configuration initiale
PRAGMA foreign_keys = ON;

-- 1. Commandes récentes avec retard de livraison
WITH late_deliveries AS (
    SELECT
        o.order_id,
        o.customer_id,
        o.order_purchase_timestamp,
        o.order_delivered_customer_date,
        julianday(o.order_delivered_customer_date) - julianday(o.order_purchase_timestamp) as delivery_time_days
    FROM orders o
    WHERE o.order_status != 'canceled'
    AND o.order_purchase_timestamp >= date('now', '-3 months')
    AND julianday(o.order_delivered_customer_date) - julianday(o.order_purchase_timestamp) > 3
)
SELECT * FROM late_deliveries;

-- 2. Vendeurs avec CA > 100 000 Real
WITH seller_revenue AS (
    SELECT
        s.seller_id,
        s.seller_name,
        ROUND(SUM(oi.price), 2) as total_revenue
    FROM sellers s
    JOIN order_items oi ON s.seller_id = oi.seller_id
    JOIN orders o ON oi.order_id = o.order_id
    WHERE o.order_status = 'delivered'
    GROUP BY s.seller_id, s.seller_name
    HAVING total_revenue > 100000
)
SELECT * FROM seller_revenue;

-- 3. Nouveaux vendeurs très engagés
WITH new_active_sellers AS (
    SELECT
        s.seller_id,
        s.seller_name,
        COUNT(DISTINCT oi.product_id) as total_products_sold
    FROM sellers s
    JOIN order_items oi ON s.seller_id = oi.seller_id
    WHERE s.seller_join_date >= date('now', '-3 months')
    GROUP BY s.seller_id, s.seller_name
    HAVING total_products_sold > 30
)
SELECT * FROM new_active_sellers;

-- 4. Codes postaux avec les pires reviews
WITH postal_code_reviews AS (
    SELECT
        c.customer_zip_code_prefix,
        COUNT(r.review_id) as total_reviews,
        ROUND(AVG(r.review_score), 2) as avg_review_score
    FROM customers c
    JOIN orders o ON c.customer_id = o.customer_id
    JOIN order_reviews r ON o.order_id = r.order_id
    WHERE o.order_purchase_timestamp >= date('now', '-12 months')
    GROUP BY c.customer_zip_code_prefix
    HAVING total_reviews > 30
    ORDER BY avg_review_score ASC
    LIMIT 5
)
SELECT * FROM postal_code_reviews;
