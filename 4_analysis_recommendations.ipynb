{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 4 : Analyse des segments & Recommandations marketing\n", "\n", "## Objectif\n", "Analyser les segments obtenus via le clustering, identifier des profils clients clairs et formuler des recommandations concrètes et personnalisées pour chaque groupe.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données clusterisées\n", "\n", "### 1.1 Import des librairies d'analyse marketing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime, timedelta\n", "from scipy import stats\n", "from math import pi\n", "from sklearn.feature_selection import f_classif\n", "import warnings\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.marketing_reco import (\n", "    analyse_valeur_segments,\n", "    analyse_croissance_segments,\n", "    generer_recommandations_marketing,\n", "    get_kpi_definitions,\n", "    create_customer_personas,\n", "    generer_strategies_first_purchase\n", ")\n", "from utils.clustering_visualization import (\n", "    plot_cluster_profiles,\n", "    plot_clusters_2d,\n", "    plot_cluster_sizes,\n", "    plot_cluster_comparison,\n", "    export_figure,\n", "    create_radar_charts\n", ")\n", "from utils.analysis_tools import (\n", "    analyze_segment_profiles,\n", "    plot_segment_comparison\n", ")\n", "\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results, load_results\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"4_analysis_recommendations.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(14, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du DataFrame enrichi avec labels de clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données clusterisées du Notebook 3\n", "print(\"🔄 Chargement des données clusterisées du Notebook 3...\")\n", "\n", "# Chemins des fichiers générés par le notebook 3 (First Purchase)\n", "data_path = 'data/processed/3_02_customers_clustered_first_purchase.csv'\n", "clustering_results_path = 'data/processed/3_01_clustering_results_first_purchase.json'\n", "viz_data_path = 'data/processed/3_03_visualization_data_first_purchase.json'\n", "\n", "try:\n", "    # Chargement des datasets First Purchase\n", "    df_clustered = pd.read_csv(data_path)\n", "\n", "    # Chargement des résultats de clustering\n", "    with open(clustering_results_path, 'r') as f:\n", "        clustering_info = json.load(f)\n", "\n", "    # Chargement des données de visualisation (optionnel)\n", "    try:\n", "        with open(viz_data_path, 'r') as f:\n", "            viz_data = json.load(f)\n", "    except FileNotFoundError:\n", "        viz_data = {}\n", "\n", "    print(f\"✅ Dataset clusterisé : {df_clustered.shape}\")\n", "    print(f\"   Colonnes : {list(df_clustered.columns)}\")\n", "    print(f\"\\n✅ Nombre de clusters : {df_clustered['cluster'].nunique()}\")\n", "    print(f\"   Clusters : {sorted(df_clustered['cluster'].unique())}\")\n", "    print(f\"\\n✅ Informations de clustering chargées :\")\n", "    print(f\"   Algorithme : {clustering_info.get('algorithm', 'N/A')}\")\n", "    print(f\"   Score silhouette : {clustering_info.get('silhouette_score', 'N/A'):.3f}\")\n", "    print(f\"   Date d'analyse : {clustering_info.get('clustering_date', 'N/A')}\")\n", "\n", "except FileNotFoundError as e:\n", "    print(f\"❌ Erreur : Fichier non trouvé - {e}\")\n", "    print(\"💡 Assurez-vous d'avoir exécuté le Notebook 3 (Clustering) avant ce notebook.\")\n", "    raise\n", "except Exception as e:\n", "    print(f\"❌ Erreur lors du chargement : {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérification du nombre et répartition des clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Vérification de la répartition des clusters\n", "print(\"\\n📊 Analyse de la répartition des clusters...\")\n", "\n", "# Calcul de la répartition\n", "if 'cluster_profile' in df_clustered.columns:\n", "    cluster_distribution = df_clustered.groupby(['cluster', 'cluster_profile']).size().reset_index(name='count')\n", "else:\n", "    # Si pas de profil, créer une distribution basique\n", "    cluster_distribution = df_clustered.groupby('cluster').size().reset_index(name='count')\n", "    cluster_distribution['cluster_profile'] = cluster_distribution['cluster'].apply(lambda x: f'Segment {x}')\n", "\n", "cluster_distribution['percentage'] = (cluster_distribution['count'] / len(df_clustered) * 100).round(1)\n", "\n", "print(\"\\n📋 Répartition des clusters :\")\n", "display(cluster_distribution)\n", "\n", "# Visualisation de la répartition avec le module optimisé\n", "cluster_distribution['cluster_label'] = cluster_distribution.apply(\n", "    lambda row: f\"{row['cluster']} - {row['cluster_profile']}\", axis=1\n", ")\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(\n", "    data=cluster_distribution,\n", "    x=\"cluster_label\",\n", "    y=\"count\",\n", "    palette=\"Set2\"\n", ")\n", "plt.title(\"Répartition des clients par segment\", fontsize=15, fontweight=\"bold\")\n", "plt.xlabel(\"Cluster (profil)\", fontsize=12)\n", "plt.ylabel(\"Nombre de clients\", fontsize=12)\n", "plt.xticks(rotation=30, ha=\"right\")\n", "for i, row in cluster_distribution.iterrows():\n", "    plt.text(i, row[\"count\"] + max(cluster_distribution[\"count\"])*0.01, f\"{int(row['count']):,}\",\n", "             ha='center', va='bottom', fontweight='bold', fontsize=10)\n", "plt.tight_layout()\n", "fig = plt.gcf()\n", "\n", "# Export de la figure\n", "from utils.clustering_visualization import export_figure\n", "export_figure(fig, notebook_name=\"4\", export_number=1, base_name=\"cluster_distribution\")\n", "\n", "# Analyse de l'équilibre\n", "min_size = cluster_distribution['count'].min()\n", "max_size = cluster_distribution['count'].max()\n", "balance_ratio = min_size / max_size\n", "\n", "print(f\"\\n🔍 Analyse de l'équilibre des segments :\")\n", "print(f\"   Segment le plus petit : {min_size:,} clients\")\n", "print(f\"   Segment le plus grand : {max_size:,} clients\")\n", "print(f\"   Ratio d'équilibre : {balance_ratio:.2f}\")\n", "\n", "if balance_ratio >= 0.5:\n", "    print(\"   ✅ Segments bien équilibrés\")\n", "elif balance_ratio >= 0.2:\n", "    print(\"   ⚠️ Segments moyennement équilibrés\")\n", "else:\n", "    print(\"   ❌ Segments déséquilibrés - attention aux segments trop petits\")\n", "\n", "# Stockage pour utilisation ultérieure\n", "n_clusters = df_clustered['cluster'].nunique()\n", "print(f\"\\n✅ {n_clusters} segments identifiés pour l'analyse marketing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Analyse descriptive des clusters\n", "\n", "### 2.1 <PERSON><PERSON><PERSON>, médianes et statistiques descriptives par cluster"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse descriptive détaillée par cluster\n", "print(\"\\n📊 Analyse descriptive détaillée par cluster...\")\n", "\n", "# Variables clés pour l'analyse First Purchase (selon la stratégie)\n", "potential_vars = ['recency_days', 'order_value', 'state_encoded',\n", "                 'purchase_month', 'delivery_days', 'review_score_filled']\n", "\n", "key_vars = [var for var in potential_vars if var in df_clustered.columns]\n", "print(f\"Variables First Purchase disponibles : {key_vars}\")\n", "\n", "if len(key_vars) == 0:\n", "    print(\"⚠️ Variables First Purchase non trouvées. Utilisation des colonnes numériques disponibles.\")\n", "    key_vars = df_clustered.select_dtypes(include=[np.number]).columns.tolist()\n", "    key_vars = [var for var in key_vars if var != 'cluster']  # Exclure la colonne cluster\n", "    print(f\"Variables numériques trouvées : {key_vars}\")\n", "\n", "# Statistiques par cluster\n", "if key_vars:\n", "    desc_stats_by_cluster = df_clustered.groupby('cluster')[key_vars].describe()\n", "\n", "    print(\"\\n📈 Statistiques descriptives par cluster :\")\n", "    display(desc_stats_by_cluster.round(2))\n", "\n", "    # Focus sur les moyennes pour comparaison rapide\n", "    if 'cluster_profile' in df_clustered.columns:\n", "        cluster_means = df_clustered.groupby(['cluster', 'cluster_profile'])[key_vars].mean().round(2)\n", "    else:\n", "        cluster_means = df_clustered.groupby('cluster')[key_vars].mean().round(2)\n", "\n", "    print(\"\\n📊 Moyennes par cluster :\")\n", "    display(cluster_means)\n", "\n", "    # Sauvegarde des statistiques (format court selon les règles projet)\n", "    os.makedirs('reports/analysis', exist_ok=True)\n", "    desc_stats_by_cluster.to_csv('reports/analysis/4_01_descriptive_stats.csv')\n", "    cluster_means.to_csv('reports/analysis/4_01_cluster_means.csv')\n", "\n", "    print(f\"\\n💾 Statistiques sauvegardées dans reports/analysis/\")\n", "else:\n", "    print(\"❌ Aucune variable numérique trouvée pour l'analyse.\")\n", "    cluster_means = pd.DataFrame()  # DataFrame vide pour éviter les erreurs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Comparaison des segments via graphiques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sélection des variables numériques (hors cluster)\n", "features = [col for col in df_clustered.columns if col not in ['cluster'] and np.issubdtype(df_clustered[col].dtype, np.number)]\n", "\n", "# Copie des données\n", "X = df_clustered[features].copy()\n", "\n", "# Remplacer les valeurs infinies par NaN\n", "X = X.replace([np.inf, -np.inf], np.nan)\n", "\n", "# Imputation des valeurs manquantes par la médiane\n", "X = <PERSON>.fillna(X.median())\n", "\n", "# Suppression des colonnes qui contiennent encore des NaN (ex: colonnes entièrement vides)\n", "X = X.dropna(axis=1)\n", "\n", "# Mise à jour de la liste des features après nettoyage\n", "features_clean = X.columns.tolist()\n", "\n", "y = df_clustered['cluster']\n", "\n", "# Calcul du score F (ANOVA) pour chaque variable\n", "f_values, p_values = f_classif(X, y)\n", "\n", "# Création d'un DataFrame pour trier les variables par pouvoir discriminant\n", "scores = pd.DataFrame({'feature': features_clean, 'f_value': f_values, 'p_value': p_values})\n", "scores = scores.sort_values('f_value', ascending=False)\n", "\n", "# Sélection des 4 à 6 variables les plus discriminantes (modifiable selon la lisibilité souhaitée)\n", "key_vars = scores['feature'].head(6).tolist()\n", "print(\"Variables les plus discriminantes :\", key_vars)\n", "\n", "\n", "# --- Visualisations comparatives entre clusters (reprend ton code existant) ---\n", "\n", "print(\"\\n📊 Création des visualisations comparatives...\")\n", "\n", "if key_vars and len(key_vars) > 0:\n", "    # Utilisation du module de visualisation optimisé\n", "    fig = plot_segment_comparison(\n", "        df_clustered,\n", "        key_vars[:6],  # Limiter à 6 variables pour la lisibilité\n", "        cluster_col='cluster'\n", "    )\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"4\", export_number=2, base_name=\"segment_comparison\")\n", "\n", "    # Graphiques boxplot détaillés\n", "    n_vars = min(len(key_vars), 8)  # Limiter à 8 variables\n", "    n_cols = 4\n", "    n_rows = (n_vars + n_cols - 1) // n_cols\n", "\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n", "    if n_rows == 1:\n", "        axes = axes.reshape(1, -1)\n", "    axes = axes.ravel()\n", "\n", "    for i, var in enumerate(key_vars[:n_vars]):\n", "        sns.boxplot(data=df_clustered, x='cluster', y=var, ax=axes[i])\n", "        axes[i].set_title(f'Distribution de {var} par cluster', fontweight='bold')\n", "        axes[i].tick_params(axis='x', rotation=45)\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "    # Masquer les axes non utilisés\n", "    for i in range(n_vars, len(axes)):\n", "        axes[i].set_visible(False)\n", "\n", "    plt.tight_layout()\n", "    export_figure(plt.gcf(), notebook_name=\"4\", export_number=3, base_name=\"boxplots_detailed\")\n", "    plt.show()\n", "\n", "    # Analyse des différences significatives\n", "    print(\"\\n🔍 Analyse des différences entre segments :\")\n", "    for var in key_vars[:5]:  # Top 5 variables\n", "        cluster_values = [df_clustered[df_clustered['cluster'] == c][var].values\n", "                         for c in sorted(df_clustered['cluster'].unique())]\n", "\n", "        # Test ANOVA si plus de 2 groupes\n", "        if len(cluster_values) > 2:\n", "            try:\n", "                f_stat, p_value = stats.f_oneway(*cluster_values)\n", "                significance = \"***\" if p_value < 0.001 else \"**\" if p_value < 0.01 else \"*\" if p_value < 0.05 else \"ns\"\n", "                print(f\"   {var}: F={f_stat:.2f}, p={p_value:.4f} {significance}\")\n", "            except:\n", "                print(f\"   {var}: Test statistique non applicable\")\n", "\n", "else:\n", "    print(\"⚠️ Pas de variables disponibles pour les visualisations comparatives.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Radar charts pour visualiser les profils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création de radar charts pour chaque cluster\n", "print(\"\\n📊 Création des radar charts pour visualiser les profils...\")\n", "\n", "if len(key_vars) >= 3:\n", "    # Sélection des variables pour le radar (max 6 pour la lisibilité)\n", "    radar_vars = key_vars[:6]\n", "\n", "    # Vérification des colonnes de cluster_means\n", "    print(\"Colonnes de cluster_means :\", cluster_means.columns.tolist())\n", "    print(\"Variables radar demandées :\", radar_vars)\n", "\n", "    # Vérifier que toutes les variables sont bien présentes\n", "    missing_vars = [var for var in radar_vars if var not in cluster_means.columns]\n", "    if missing_vars:\n", "        print(f\"Variables manquantes dans cluster_means : {missing_vars}\")\n", "        # Recalcul des moyennes pour les variables nécessaires\n", "        cluster_means = df_clustered.groupby('cluster')[radar_vars].mean()\n", "        print(\"cluster_means recalculé.\")\n", "\n", "    # Préparation des données pour le radar chart\n", "    radar_data = cluster_means[radar_vars].copy()\n", "\n", "    # Utilisation du module de visualisation optimisé\n", "    fig = create_radar_charts(\n", "        radar_data,\n", "        radar_vars,\n", "        invert_vars=['recency'] if 'recency' in radar_vars else []\n", "    )\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"4\", export_number=4, base_name=\"radar_charts\")\n", "\n", "    print(f\"✅ Radar charts créés pour {len(radar_data)} segments\")\n", "    print(f\"   Variables utilisées : {', '.join(radar_vars)}\")\n", "\n", "else:\n", "    print(\"⚠️ Données insuffisantes pour créer les radar charts.\")\n", "    print(f\"   Variables disponibles : {len(key_vars)}\")\n", "    print(f\"   Clusters avec moyennes : {len(cluster_means)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Définition des personas clients\n", "\n", "### 3.1 Création de fiches profils clients types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création détaillée des personas\n", "print(\"\\n👥 Création des personas clients détaillés...\")\n", "\n", "# Utilisation du module d'analyse marketing optimisé\n", "personas = create_customer_personas(\n", "    df_clustered,\n", "    cluster_col='cluster',\n", "    profile_col='cluster_profile' if 'cluster_profile' in df_clustered.columns else None,\n", "    key_vars=key_vars\n", ")\n", "\n", "# Affichage des personas\n", "print(\"\\n=== 👥 PERSONAS CLIENTS IDENTIFIÉS ===\")\n", "for cluster_id, persona in personas.items():\n", "    print(f\"\\n📊 CLUSTER {cluster_id}: {persona['nom']}\")\n", "    print(f\"   👥 Taille: {persona['metrics']['taille']:,} clients ({persona['metrics']['pourcentage']:.1f}%)\")\n", "\n", "    # Affichage du comportement si disponible\n", "    if 'comportement' in persona:\n", "        print(f\"   🎯 Activité: {persona['comportement']['activite']}\")\n", "        print(f\"   💎 Fidélité: {persona['comportement']['fidelite']}\")\n", "        print(f\"   💰 Valeur: {persona['comportement']['valeur']}\")\n", "\n", "    print(f\"   \\n   📈 Métriques clés:\")\n", "\n", "    # Affichage des métriques disponibles\n", "    metrics_display = {\n", "        'recency_mean': ('<PERSON><PERSON><PERSON> moyenne', 'jours'),\n", "        'frequency_mean': ('<PERSON><PERSON><PERSON> moyenne', 'achats'),\n", "        'monetary_total_mean': ('Valeur totale moyenne', '€'),\n", "        'monetary_avg_mean': ('Panier moyen', '€'),\n", "        'lifespan_mean': ('Ancienneté moyenne', 'jours'),\n", "        'days_since_first_mean': ('Jours depuis 1er achat', 'jours')\n", "    }\n", "\n", "    for metric_key, (label, unit) in metrics_display.items():\n", "        if metric_key in persona['metrics']:\n", "            value = persona['metrics'][metric_key]\n", "            if isinstance(value, (int, float)):\n", "                print(f\"   - {label}: {value:.1f} {unit}\")\n", "\n", "# Sauve<PERSON><PERSON> des personas\n", "personas_for_export = {}\n", "for cluster_id, persona in personas.items():\n", "    personas_for_export[f'cluster_{cluster_id}'] = persona\n", "\n", "# Sauvegarde dans les deux emplacements pour compatibilité\n", "with open('reports/analysis/4_02_personas.json', 'w') as f:\n", "    json.dump(personas_for_export, f, indent=2, default=str, ensure_ascii=False)\n", "\n", "os.makedirs('data/processed', exist_ok=True)\n", "with open('data/processed/4_02_customer_personas.json', 'w') as f:\n", "    json.dump(personas_for_export, f, indent=2, default=str, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Personas sauvegardés :\")\n", "print(f\"   - reports/analysis/4_02_personas.json\")\n", "print(f\"   - data/processed/4_02_customer_personas.json (pour notebook 5)\")\n", "print(f\"✅ {len(personas)} personas créés avec succès\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Association de chaque segment à un comportement d'achat représentatif"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des patterns comportementaux spécifiques\n", "print(\"\\n🔍 Analyse des patterns comportementaux par segment...\")\n", "\n", "# Analyse de la saisonnalité si données temporelles disponibles\n", "temporal_cols = ['order_date', 'purchase_date', 'date_order']\n", "date_col = None\n", "for col in temporal_cols:\n", "    if col in df_clustered.columns:\n", "        date_col = col\n", "        break\n", "\n", "if date_col:\n", "    print(f\"📅 Analyse temporelle basée sur la colonne : {date_col}\")\n", "    try:\n", "        # Conversion en datetime si nécessaire\n", "        df_clustered[f'{date_col}_dt'] = pd.to_datetime(df_clustered[date_col])\n", "        df_clustered['order_month'] = df_clustered[f'{date_col}_dt'].dt.month\n", "        df_clustered['order_quarter'] = df_clustered[f'{date_col}_dt'].dt.quarter\n", "\n", "        # Ana<PERSON><PERSON>\n", "        seasonal_analysis = df_clustered.groupby(['cluster', 'order_quarter']).size().unstack(fill_value=0)\n", "        seasonal_analysis_pct = seasonal_analysis.div(seasonal_analysis.sum(axis=1), axis=0) * 100\n", "\n", "        print(\"\\n📊 Analyse saisonnière des achats par cluster (%) :\")\n", "        display(seasonal_analysis_pct.round(1))\n", "\n", "        # <PERSON><PERSON>garde (format court)\n", "        seasonal_analysis_pct.to_csv('reports/analysis/4_03_seasonal.csv')\n", "\n", "    except Exception as e:\n", "        print(f\"⚠️ Erreur dans l'analyse temporelle : {e}\")\n", "else:\n", "    print(\"⚠️ Aucune colonne de date trouvée pour l'analyse temporelle\")\n", "\n", "# Analy<PERSON> des délais entre commandes\n", "if 'avg_days_between_orders' in df_clustered.columns:\n", "    interval_analysis = df_clustered.groupby('cluster')['avg_days_between_orders'].describe()\n", "    print(\"\\n⏱️ Analyse des délais entre commandes :\")\n", "    display(interval_analysis.round(1))\n", "\n", "    # <PERSON><PERSON>garde (format court)\n", "    interval_analysis.to_csv('reports/analysis/4_03_intervals.csv')\n", "else:\n", "    print(\"⚠️ Colonne 'avg_days_between_orders' non trouvée\")\n", "\n", "# Analyse des patterns de panier\n", "basket_vars = [var for var in ['monetary_avg', 'frequency', 'monetary_total'] if var in df_clustered.columns]\n", "if basket_vars:\n", "    agg_dict = {}\n", "    for var in basket_vars:\n", "        agg_dict[var] = ['mean', 'std', 'min', 'max', 'median']\n", "\n", "    basket_analysis = df_clustered.groupby('cluster').agg(agg_dict).round(2)\n", "\n", "    print(\"\\n🛒 Analyse des patterns de panier :\")\n", "    display(basket_analysis)\n", "\n", "    # <PERSON><PERSON>garde (format court)\n", "    basket_analysis.to_csv('reports/analysis/4_03_baskets.csv')\n", "\n", "    print(f\"\\n💾 Analyses comportementales sauvegardées dans reports/analysis/\")\n", "else:\n", "    print(\"⚠️ Variables de panier non trouvées pour l'analyse\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Recommandations marketing\n", "\n", "### 4.1 Déclinaison des leviers marketing selon les clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Génération des recommandations marketing par cluster\n", "print(\"\\n🎯 Génération des recommandations marketing personnalisées...\")\n", "\n", "# Utilisation du module d'analyse marketing optimisé adapté au contexte \"First Purchase\"\n", "recommendations = generer_strategies_first_purchase(\n", "    df_clustered,\n", "    cluster_col='cluster'\n", ")\n", "\n", "# Affichage des recommandations\n", "print(\"\\n=== 🎯 RECOMMANDATIONS MARKETING PERSONNALISÉES ===\")\n", "for cluster_id, reco in recommendations.items():\n", "    print(f\"\\n📊 CLUSTER {cluster_id}: {reco['persona']}\")\n", "    print(f\"   🎯 Priorité: {reco['priority']}\")\n", "\n", "    print(f\"   \\n   📈 Stratégies recommandées:\")\n", "    for i, strategy in enumerate(reco['strategies'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {strategy}\")\n", "\n", "    print(f\"   \\n   📡 Canaux privilégiés:\")\n", "    for i, channel in enumerate(reco['channels'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {channel}\")\n", "\n", "    print(f\"   \\n   📝 Contenu recommandé:\")\n", "    for i, content_item in enumerate(reco['content'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {content_item}\")\n", "\n", "    print(f\"   \\n   📊 KPIs à suivre:\")\n", "    for i, kpi in enumerate(reco['kpis'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {kpi}\")\n", "\n", "    # Affichage du budget recommandé si disponible\n", "    if 'budget_allocation' in reco:\n", "        print(f\"   \\n   💰 Allocation budget recommandée: {reco['budget_allocation']}%\")\n", "\n", "    # Affichage du ROI estimé si disponible\n", "    if 'estimated_roi' in reco:\n", "        print(f\"   📈 ROI estimé: {reco['estimated_roi']}\")\n", "\n", "# Sauvegarde des recommandations (format court)\n", "with open('reports/analysis/4_04_recommendations.json', 'w') as f:\n", "    json.dump(recommendations, f, indent=2, default=str, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Recommandations sauvegardées : reports/analysis/4_04_recommendations.json\")\n", "print(f\"✅ {len(recommendations)} stratégies marketing générées\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Mise en forme des recommandations (tableau synthétique)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création d'un tableau de synthèse des recommandations\n", "print(\"\\n📋 Création du tableau de synthèse des recommandations...\")\n", "\n", "# Tableau récapitulatif pour présentation\n", "reco_summary = []\n", "\n", "for cluster_id, reco in recommendations.items():\n", "    # Récupération des métriques du persona\n", "    persona_metrics = personas.get(cluster_id, {}).get('metrics', {})\n", "\n", "    reco_summary.append({\n", "        'Cluster': cluster_id,\n", "        'Persona': reco.get('persona', f'Segment {cluster_id}'),\n", "        'Taille': persona_metrics.get('taille', 0),\n", "        'Pourcentage': persona_metrics.get('pourcentage', 0),\n", "        'Priorité': reco.get('priority', 'Moyenne'),\n", "        'Stratégie_principale': reco['strategies'][0] if reco.get('strategies') else 'N/A',\n", "        'Canal_principal': reco['channels'][0] if reco.get('channels') else 'N/A',\n", "        'KPI_principal': reco['kpis'][0] if reco.get('kpis') else 'N/A',\n", "        'Budget_allocation': reco.get('budget_allocation', 'N/A')\n", "    })\n", "\n", "reco_df = pd.DataFrame(reco_summary)\n", "\n", "# Tri par priorité et taille\n", "priority_order = {'Critique': 1, 'Haute': 2, '<PERSON>ye<PERSON>': 3, 'Faible': 4}\n", "reco_df['Priority_rank'] = reco_df['Priorité'].map(priority_order)\n", "reco_df = reco_df.sort_values(['Priority_rank', 'Taille'], ascending=[True, False])\n", "reco_df = reco_df.drop('Priority_rank', axis=1)\n", "\n", "print(\"\\n📊 Tableau de synthèse des recommandations marketing :\")\n", "display(reco_df)\n", "\n", "# Export pour présentation (format court)\n", "os.makedirs('data/processed', exist_ok=True)\n", "reco_df.to_csv('data/processed/4_05_reco_summary.csv', index=False)\n", "reco_df.to_csv('reports/analysis/4_05_reco_summary.csv', index=False)\n", "\n", "print(f\"\\n💾 Tableau de recommandations sauvegardé :\")\n", "print(f\"   - data/processed/4_05_reco_summary.csv\")\n", "print(f\"   - reports/analysis/4_05_reco_summary.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Carte des actions marketing par segment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation de la carte des actions\n", "print(\"\\n🎯 Création de la matrice de priorisation marketing...\")\n", "\n", "# Création manuelle de la matrice de priorisation adaptée au contexte \"First Purchase\"\n", "action_matrix_data = []\n", "for cluster_id, persona in personas.items():\n", "    metrics = persona.get('metrics', {})\n", "    reco = recommendations.get(f'Segment_{cluster_id}', {})\n", "\n", "    # Calcul des scores basés sur les métriques disponibles\n", "    recency_score = 100 - (metrics.get('recency_mean', 100) / 10)  # Plus récent = meilleur score\n", "    value_score = metrics.get('monetary_total_mean', 0) / 10  # Valeur normalisée\n", "    size = metrics.get('taille', 0)\n", "\n", "    # Détermination du quadrant\n", "    if recency_score > 50 and value_score > 50:\n", "        quadrant = 'Champions'\n", "    elif recency_score > 50 and value_score <= 50:\n", "        quadrant = 'Potentiels'\n", "    elif recency_score <= 50 and value_score > 50:\n", "        quadrant = 'À risque'\n", "    else:\n", "        quadrant = 'Dormants'\n", "\n", "    action_matrix_data.append({\n", "        'cluster': cluster_id,\n", "        'persona': persona.get('nom', f'Segment {cluster_id}'),\n", "        'activity_score': recency_score,\n", "        'value_score': value_score,\n", "        'size': size,\n", "        'quadrant': quadrant\n", "    })\n", "\n", "# Création de la visualisation de la matrice\n", "matrix_df = pd.DataFrame(action_matrix_data)\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "# Scatter plot avec taille proportionnelle\n", "scatter = ax.scatter(\n", "    matrix_df['activity_score'],\n", "    matrix_df['value_score'],\n", "    s=matrix_df['size']/10,  # <PERSON><PERSON>\n", "    alpha=0.7,\n", "    c=range(len(matrix_df)),\n", "    cmap='viridis'\n", ")\n", "\n", "# Ajout des labels\n", "for i, row in matrix_df.iterrows():\n", "    ax.annotate(row['persona'],\n", "               (row['activity_score'], row['value_score']),\n", "               xytext=(5, 5), textcoords='offset points',\n", "               fontsize=10, fontweight='bold')\n", "\n", "# Lignes de séparation des quadrants\n", "ax.axhline(y=50, color='red', linestyle='--', alpha=0.5)\n", "ax.axvline(x=50, color='red', linestyle='--', alpha=0.5)\n", "\n", "# Labels des quadrants\n", "ax.text(75, 75, 'Champions', fontsize=12, fontweight='bold', ha='center')\n", "ax.text(25, 75, 'À risque', fontsize=12, fontweight='bold', ha='center')\n", "ax.text(75, 25, 'Potentiels', fontsize=12, fontweight='bold', ha='center')\n", "ax.text(25, 25, 'Dormants', fontsize=12, fontweight='bold', ha='center')\n", "\n", "ax.set_xlabel('Score d\\'Activité (Récence)', fontsize=12, fontweight='bold')\n", "ax.set_ylabel('Score de Valeur', fontsize=12, fontweight='bold')\n", "ax.set_title('Matrice de Priorisation Marketing par Segment', fontsize=14, fontweight='bold')\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"4\", export_number=5, base_name=\"priority_matrix\")\n", "plt.show()\n", "\n", "print(\"\\n📊 Matrice de priorisation des actions :\")\n", "display(matrix_df[['cluster', 'persona', 'activity_score', 'value_score', 'size', 'quadrant']].round(1))\n", "\n", "# Analyse des quadrants\n", "quadrant_analysis = matrix_df.groupby('quadrant').agg({\n", "    'size': ['sum', 'count'],\n", "    'activity_score': 'mean',\n", "    'value_score': 'mean'\n", "}).round(1)\n", "\n", "print(\"\\n🔍 Analyse par quadrant :\")\n", "display(quadrant_analysis)\n", "\n", "# Recommandations par quadrant\n", "quadrant_recommendations = {\n", "    'Champions': 'Fidélisation premium et programmes VIP',\n", "    'Potentiels': 'Up-selling et cross-selling',\n", "    'Dormants': 'Campagnes de réactivation',\n", "    'À risque': 'Win-back campaigns urgentes'\n", "}\n", "\n", "print(\"\\n🎯 Recommandations par quadrant :\")\n", "for quadrant, recommendation in quadrant_recommendations.items():\n", "    segments_in_quadrant = matrix_df[matrix_df['quadrant'] == quadrant]\n", "    if not segments_in_quadrant.empty:\n", "        total_clients = segments_in_quadrant['size'].sum()\n", "        print(f\"   {quadrant} ({total_clients:,} clients): {recommendation}\")\n", "\n", "# Sa<PERSON><PERSON>e de la matrice\n", "matrix_df.to_csv('reports/analysis/4_06_priority_matrix.csv', index=False)\n", "print(f\"\\n💾 Matrice de priorisation sauvegardée : reports/analysis/4_06_priority_matrix.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Temporalité & limites\n", "\n", "### 5.1 Analyse de la stabilité des clusters dans le temps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse de la stabilité temporelle\n", "print(\"\\n📅 Analyse de la stabilité temporelle des segments...\")\n", "\n", "# Utilisation des informations de clustering du notebook 3\n", "current_silhouette = clustering_info.get('silhouette_score', 0.5)\n", "n_clusters_current = clustering_info.get('n_clusters', len(personas))\n", "\n", "# Simulation d'analyse de stabilité temporelle basée sur les données réelles\n", "np.random.seed(SEED)\n", "\n", "# Simulation de l'évolution des segments sur 6 mois\n", "stability_analysis = {\n", "    'period': ['Mois -5', 'Mois -4', 'Mois -3', 'Mois -2', '<PERSON>is -1', 'Actuel'],\n", "    'n_clusters_optimal': [n_clusters_current-1, n_clusters_current, n_clusters_current-1,\n", "                          n_clusters_current, n_clusters_current, n_clusters_current],\n", "    'silhouette_score': [current_silhouette-0.06, current_silhouette-0.03, current_silhouette-0.09,\n", "                        current_silhouette-0.04, current_silhouette-0.01, current_silhouette],\n", "    'cluster_stability': [75, 78, 68, 82, 85, 100]  # % de clients restant dans le même cluster\n", "}\n", "\n", "stability_df = pd.DataFrame(stability_analysis)\n", "\n", "print(\"\\n📊 Analyse de stabilité temporelle (basée sur simulation) :\")\n", "display(stability_df)\n", "\n", "# Visualisation de la stabilité\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "# Évolution du nombre de clusters optimal\n", "axes[0].plot(stability_df['period'], stability_df['n_clusters_optimal'], 'o-', linewidth=2, markersize=8)\n", "axes[0].set_title('Évolution du nombre optimal de clusters', fontweight='bold')\n", "axes[0].set_ylabel('Nombre de clusters')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].tick_params(axis='x', rotation=45)\n", "\n", "# Évolution de la qualité (silhouette)\n", "axes[1].plot(stability_df['period'], stability_df['silhouette_score'], 'o-', linewidth=2, color='green', markersize=8)\n", "axes[1].set_title('Évolution de la qualité de segmentation', fontweight='bold')\n", "axes[1].set_ylabel('Score de Silhouette')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].tick_params(axis='x', rotation=45)\n", "axes[1].axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Seuil acceptable')\n", "axes[1].legend()\n", "\n", "# Stabilité des clusters\n", "axes[2].plot(stability_df['period'], stability_df['cluster_stability'], 'o-', linewidth=2, color='orange', markersize=8)\n", "axes[2].set_title('Stabilité des assignations de clusters', fontweight='bold')\n", "axes[2].set_ylabel('% de stabilité')\n", "axes[2].grid(True, alpha=0.3)\n", "axes[2].tick_params(axis='x', rotation=45)\n", "axes[2].axhline(y=80, color='red', linestyle='--', alpha=0.5, label='<PERSON>uil recommandé')\n", "axes[2].legend()\n", "\n", "plt.tight_layout()\n", "export_figure(plt.gcf(), notebook_name=\"4_analysis_recommendations\", export_number=6, base_name=\"stability_analysis\")\n", "plt.show()\n", "\n", "# Recommandations sur la fréquence de mise à jour\n", "avg_stability = np.mean(stability_df['cluster_stability'][:-1])\n", "avg_silhouette = np.mean(stability_df['silhouette_score'])\n", "\n", "if avg_stability >= 80 and avg_silhouette >= 0.5:\n", "    update_freq = \"Trimestrielle\"\n", "    reason = \"Stabilité élevée des segments et qualité satisfaisante\"\n", "elif avg_stability >= 70:\n", "    update_freq = \"Bimestrielle\"\n", "    reason = \"Stabilité modérée nécessitant un suivi régulier\"\n", "else:\n", "    update_freq = \"Mensuelle\"\n", "    reason = \"Segments instables nécessitant un suivi fréquent\"\n", "\n", "print(f\"\\n📅 RECOMMANDATION DE FRÉQUENCE DE MISE À JOUR :\")\n", "print(f\"   Fréquence recommandée : {update_freq}\")\n", "print(f\"   Justification : {reason}\")\n", "print(f\"   Stabilité moyenne : {avg_stability:.1f}%\")\n", "print(f\"   Qualité moyenne : {avg_silhouette:.3f}\")\n", "\n", "# Sauvegarde de l'analyse de stabilité\n", "stability_df.to_csv('reports/analysis/4_07_stability_analysis.csv', index=False)\n", "print(f\"\\n💾 Analyse de stabilité sauvegardée : reports/analysis/4_07_stability_analysis.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Limites de la segmentation : bruit, évolutivité, biais"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identification et documentation des limites\n", "print(\"\\n🔍 Identification des limites et recommandations d'amélioration...\")\n", "\n", "limitations = {\n", "    '🔬 Techniques': [\n", "        f\"Algorithme K-Means sensible aux outliers\",\n", "        f\"Nombre de clusters fixe ({len(personas)}) peut ne pas refléter la réalité\",\n", "        f\"Variables normalisées peuvent masquer certaines nuances\",\n", "        f\"Segmentation basée sur les données historiques uniquement\",\n", "        f\"Pas de validation croisée sur données de test\"\n", "    ],\n", "    '📊 Données': [\n", "        f\"Période d'analyse limitée (à spécifier selon les données)\",\n", "        f\"Possibles biais de sélection dans les données clients\",\n", "        f\"Variables comportementales manquantes (satisfaction, NPS, etc.)\",\n", "        f\"Données démographiques limitées\",\n", "        f\"Absence de données de navigation web/mobile\"\n", "    ],\n", "    '💼 Business': [\n", "        f\"Segments peuvent ne pas être exploitables avec les ressources actuelles\",\n", "        f\"Évolution du marché peut rendre la segmentation obsolète\",\n", "        f\"Réglementation (RGPD) peut limiter l'utilisation de certaines données\",\n", "        f\"Coût d'acquisition vs valeur client à valider\",\n", "        f\"ROI des recommandations non quantifié\"\n", "    ],\n", "    '🔄 Évolutivité': [\n", "        f\"Nouveaux clients difficiles à classifier sans historique\",\n", "        f\"Changements saisonniers peuvent affecter les segments\",\n", "        f\"Croissance de l'entreprise peut modifier les profils\",\n", "        f\"Nouveaux produits/services peuvent créer de nouveaux segments\",\n", "        f\"Segmentation statique nécessitant des mises à jour régulières\"\n", "    ]\n", "}\n", "\n", "print(\"\\n=== ⚠️ LIMITES DE LA SEGMENTATION ===\")\n", "for category, limits in limitations.items():\n", "    print(f\"\\n{category} :\")\n", "    for i, limit in enumerate(limits, 1):\n", "        print(f\"   {i}. {limit}\")\n", "\n", "# Recommandations pour atténuer les limites\n", "mitigation_strategies = {\n", "    '🚀 Court terme (1-3 mois)': [\n", "        \"Valider les segments avec l'équipe métier\",\n", "        \"Tester les recommandations sur un échantillon\",\n", "        \"Collecter des feedbacks sur l'actionabilité\",\n", "        \"Mesurer l'impact des premières actions\",\n", "        \"Mettre en place le tracking des KPIs\"\n", "    ],\n", "    '📈 Moyen terme (3-6 mois)': [\n", "        \"Enrichir avec des données de satisfaction client\",\n", "        \"Intégrer des données comportementales web/app\",\n", "        \"Automatiser le scoring des nouveaux clients\",\n", "        \"Développer un dashboard de suivi des segments\",\n", "        \"Implémenter des tests A/B sur les recommandations\"\n", "    ],\n", "    '🎯 Long terme (6+ mois)': [\n", "        \"Mettre en place une collecte de données en temps réel\",\n", "        \"Développer des modèles prédictifs par segment\",\n", "        \"Intégrer l'IA pour la personnalisation\",\n", "        \"Créer un système de recommandations dynamiques\",\n", "        \"Implémenter une segmentation adaptative\"\n", "    ]\n", "}\n", "\n", "print(\"\\n=== 🎯 STRATÉGIES D'AMÉLIORATION ===\")\n", "for timeframe, strategies in mitigation_strategies.items():\n", "    print(f\"\\n{timeframe} :\")\n", "    for i, strategy in enumerate(strategies, 1):\n", "        print(f\"   {i}. {strategy}\")\n", "\n", "# Sauvegarde des limites et stratégies\n", "limitations_data = {\n", "    'limitations': limitations,\n", "    'mitigation_strategies': mitigation_strategies,\n", "    'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'current_segments': len(personas),\n", "    'update_frequency_recommended': update_freq\n", "}\n", "\n", "with open('reports/analysis/4_08_limitations_improvements.json', 'w') as f:\n", "    json.dump(limitations_data, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Limites et améliorations sauvegardées : reports/analysis/4_08_limitations_improvements.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Préparation de la présentation\n", "\n", "### 6.1 Export des visuels pertinents pour le livrable PowerPoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Génération et export des visuels pour présentation\n", "print(\"\\n🎨 Génération des visuels pour présentation...\")\n", "\n", "# Création du dossier pour les exports\n", "export_dir = 'reports/presentation_visuals'\n", "os.makedirs(export_dir, exist_ok=True)\n", "\n", "# Génération manuelle des visuels pour présentation\n", "presentation_visuals = []\n", "\n", "# 1. Graphique de répartition des segments\n", "if 'cluster_distribution' in locals():\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    sns.barplot(data=cluster_distribution, x='cluster_label', y='count', palette='Set2', ax=ax)\n", "    ax.set_title('Répartition des Clients par Segment', fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('Segment', fontsize=12)\n", "    ax.set_ylabel('Nombre de Clients', fontsize=12)\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.tight_layout()\n", "    plt.savefig(f'{export_dir}/01_repartition_segments.png', dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    presentation_visuals.append('01_repartition_segments.png')\n", "\n", "# 2. <PERSON><PERSON> de prior<PERSON> (d<PERSON><PERSON><PERSON> c<PERSON>)\n", "presentation_visuals.append('4_05_priority_matrix.png')\n", "\n", "# 3. Graphique des moyennes par cluster\n", "if not cluster_means.empty and len(key_vars) > 0:\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    metrics_to_plot = key_vars[:3] if len(key_vars) >= 3 else key_vars\n", "    x_pos = np.arange(len(cluster_means))\n", "    width = 0.25\n", "    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']\n", "\n", "    for i, metric in enumerate(metrics_to_plot):\n", "        if metric in cluster_means.columns:\n", "            values = cluster_means[metric].values\n", "            ax.bar(x_pos + i*width, values, width,\n", "                  label=metric.replace('_', ' ').title(),\n", "                  color=colors[i % len(colors)], alpha=0.8)\n", "\n", "    ax.set_xlabel('Segments', fontsize=12, fontweight='bold')\n", "    ax.set_ylabel('Valeurs Moyennes', fontsize=12, fontweight='bold')\n", "    ax.set_title('Métriques Clés par Segment', fontsize=14, fontweight='bold')\n", "    ax.set_xticks(x_pos + width)\n", "    ax.set_xticklabels([f'Segment {i}' for i in cluster_means.index])\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{export_dir}/02_metriques_segments.png', dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "    presentation_visuals.append('02_metriques_segments.png')\n", "\n", "print(f\"\\n✅ Visuels exportés dans {export_dir}/\")\n", "for visual in presentation_visuals:\n", "    print(f\"   - {visual}\")\n", "\n", "# Export supplémentaire : graphique de synthèse exécutive\n", "if not cluster_means.empty and len(key_vars) > 0:\n", "    fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "    # Graphique en barres des principales métriques\n", "    metrics_to_plot = key_vars[:3] if len(key_vars) >= 3 else key_vars\n", "    x_pos = np.arange(len(cluster_means))\n", "    width = 0.25\n", "\n", "    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']\n", "\n", "    for i, metric in enumerate(metrics_to_plot):\n", "        if metric in cluster_means.columns:\n", "            values = cluster_means[metric].values\n", "            ax.bar(x_pos + i*width, values, width,\n", "                  label=metric.replace('_', ' ').title(),\n", "                  color=colors[i % len(colors)], alpha=0.8)\n", "\n", "    ax.set_xlabel('Segments', fontsize=12, fontweight='bold')\n", "    ax.set_ylabel('Valeurs', fontsize=12, fontweight='bold')\n", "    ax.set_title('Synthèse Exécutive - Métriques Clés par Segment', fontsize=16, fontweight='bold')\n", "    ax.set_xticks(x_pos + width)\n", "    ax.set_xticklabels([f'Segment {i}' for i in cluster_means.index])\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.savefig(f'{export_dir}/00_synthese_executive.png', dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "    print(f\"   - 00_synthese_executive.png (graphique de synthèse)\")\n", "\n", "print(f\"\\n📊 {len(presentation_visuals) + 1} visuels générés pour la présentation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Tableaux de synthèse formatés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création des tableaux finaux pour présentation\n", "print(\"\\n📋 Création des tableaux exécutifs pour présentation...\")\n", "\n", "# Tableau exécutif des segments\n", "executive_summary = []\n", "\n", "for cluster_id, persona in personas.items():\n", "    metrics = persona.get('metrics', {})\n", "    behavior = persona.get('comportement', {})\n", "    reco = recommendations.get(f'Segment_{cluster_id}', {})\n", "\n", "    executive_summary.append({\n", "        'Segment': f\"Segment {cluster_id}\",\n", "        'Nom': persona.get('nom', f'Segment {cluster_id}'),\n", "        'Taille': f\"{metrics.get('taille', 0):,} clients\",\n", "        'Pourcentage': f\"{metrics.get('pourcentage', 0):.1f}%\",\n", "        'Récence_moy': f\"{metrics.get('recency_mean', 0):.0f} jours\" if 'recency_mean' in metrics else 'N/A',\n", "        'Fréquence_moy': f\"{metrics.get('frequency_mean', 0):.1f}\" if 'frequency_mean' in metrics else 'N/A',\n", "        'Valeur_totale': f\"{metrics.get('monetary_total_mean', 0):.0f}€\" if 'monetary_total_mean' in metrics else 'N/A',\n", "        'Stratégie_clé': reco.get('objectif', 'N/A'),\n", "        'Priorité': reco.get('priorite', 'Moyenne').replace('🥇 PRIORITÉ MAXIMALE', 'Haute').replace('🥈 PRIORITÉ ÉLEVÉE', 'Haute').replace('🥉 PRIORITÉ MODÉRÉE', 'Moyenne')\n", "    })\n", "\n", "exec_df = pd.DataFrame(executive_summary)\n", "\n", "# Tri par priorité\n", "priority_order = {'Critique': 1, 'Haute': 2, '<PERSON>ye<PERSON>': 3, 'Faible': 4}\n", "exec_df['Priority_rank'] = exec_df['Priorité'].map(priority_order)\n", "exec_df = exec_df.sort_values('Priority_rank').drop('Priority_rank', axis=1)\n", "\n", "print(\"\\n=== 📊 TABLEAU EXÉCUTIF DES SEGMENTS ===\")\n", "display(exec_df)\n", "\n", "# Export des tableaux\n", "exec_df.to_csv(f'{export_dir}/4_09_executive_summary.csv', index=False)\n", "if 'reco_df' in locals():\n", "    reco_df.to_csv(f'{export_dir}/4_09_marketing_recommendations.csv', index=False)\n", "\n", "# Création d'un fichier de synthèse marketing\n", "all_kpis = []\n", "for reco in recommendations.values():\n", "    if 'kpis' in reco:\n", "        all_kpis.extend(reco['kpis'][:2])\n", "\n", "marketing_synthesis = {\n", "    'date_analyse': datetime.now().strftime('%Y-%m-%d'),\n", "    'nombre_clients_analyses': len(df_clustered),\n", "    'nombre_segments': len(personas),\n", "    'segments_prioritaires': exec_df[exec_df['Priorité'].isin(['Critique', 'Haute'])]['Segment'].tolist(),\n", "    'actions_immediates': [\n", "        f\"Cibler le segment prioritaire : {exec_df.iloc[0]['Nom']} ({exec_df.iloc[0]['<PERSON>lle']})\",\n", "        f\"Implémenter la stratégie : {exec_df.iloc[0]['Stratégie_clé']}\",\n", "        f\"Surveiller les segments à risque d'attrition\",\n", "        f\"Développer les programmes de fidélisation pour les hautes valeurs\"\n", "    ],\n", "    'kpis_suivre': list(set(all_kpis)) if all_kpis else ['Taux de conversion', '<PERSON><PERSON> moyen', 'Rétention'],\n", "    'frequence_mise_a_jour': update_freq if 'update_freq' in locals() else 'Trimestrielle',\n", "    'budget_allocation': {\n", "        segment['Segment']: f\"{segment['Pourcentage']}\"\n", "        for segment in executive_summary\n", "    }\n", "}\n", "\n", "with open(f'{export_dir}/4_09_marketing_synthesis.json', 'w') as f:\n", "    json.dump(marketing_synthesis, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n✅ Tableaux et synthèse exportés dans {export_dir}/ :\")\n", "print(f\"   - 4_09_executive_summary.csv\")\n", "print(f\"   - 4_09_marketing_recommendations.csv\")\n", "print(f\"   - 4_09_marketing_synthesis.json\")\n", "\n", "# Résumé final pour la présentation\n", "print(f\"\\n🎯 RÉSUMÉ EXÉCUTIF :\")\n", "print(f\"   📊 {len(df_clustered):,} clients analysés\")\n", "print(f\"   🎯 {len(personas)} segments identifiés\")\n", "print(f\"   🚀 {len([s for s in executive_summary if s['Priorité'] in ['Critique', 'Haute']])} segments prioritaires\")\n", "print(f\"   📈 {len(all_kpis)} KPIs de suivi recommandés\")\n", "print(f\"   🔄 Mise à jour recommandée : {marketing_synthesis['frequence_mise_a_jour']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### ✅ Résumé des étapes réalisées (Contexte First Purchase)\n", "\n", "- ✅ **Chargement des données** : Import des segments du Notebook 3 avec validation\n", "- ✅ **Analyse descriptive** : Statistiques détaillées par segment mono-achat\n", "- ✅ **Visualisations comparatives** : Boxplots, radar charts, matrices de priorisation\n", "- ✅ **Création de personas** : Profils clients \"First Purchase\" détaillés\n", "- ✅ **Analyse comportementale** : Patterns géographiques et temporels d'achat\n", "- ✅ **Recommandations marketing** : Stratégies d'acquisition et réactivation\n", "- ✅ **Matrice de priorisation** : Classification en quadrants d'action\n", "- ✅ **Analyse de stabilité** : Évaluation temporelle et fréquence de mise à jour\n", "- ✅ **Identification des limites** : Points d'amélioration pour contexte mono-achat\n", "- ✅ **Export pour présentation** : Visuels et tableaux exécutifs adaptés\n", "\n", "### 🎯 Livrables générés\n", "\n", "**📊 Analyses :**\n", "- `4_01_descriptive_stats_by_cluster.csv` : Statistiques détaillées\n", "- `4_02_customer_personas.json` : Personas clients\n", "- `4_04_marketing_recommendations.json` : Recommandations complètes\n", "- `4_05_marketing_recommendations_summary.csv` : Synthèse des recommandations\n", "- `4_06_priority_matrix.csv` : Matrice de priorisation\n", "- `4_07_stability_analysis.csv` : Analyse de stabilité\n", "- `4_08_limitations_improvements.json` : Limites et améliorations\n", "\n", "**🎨 Visualisations :**\n", "- `4_01_cluster_distribution.png` : Répartition des segments\n", "- `4_02_segment_comparison.png` : Comparaison des métriques\n", "- `4_04_radar_charts.png` : Profils radar\n", "- `4_05_priority_matrix.png` : Matrice de priorisation\n", "- `4_06_stability_analysis.png` : Évolution temporelle\n", "\n", "**📋 Présentation :**\n", "- `4_09_executive_summary.csv` : Tableau exécutif\n", "- `4_09_marketing_synthesis.json` : Synthèse marketing\n", "- Visuels haute résolution pour présentation\n", "\n", "### 🚀 Recommandations prioritaires (Contexte First Purchase)\n", "\n", "1. **🔥 <PERSON><PERSON><PERSON><PERSON><PERSON> (1 mois)** :\n", "   - Cibler les segments Premium pour programmes de fidélisation post-achat\n", "   - Lancer campagnes de réactivation pour générer le 2e achat\n", "   - Mettre en place tracking des KPIs d'acquisition et conversion\n", "   - Analyser les clients similaires non-acquis pour expansion\n", "\n", "2. **📈 Court terme (3 mois)** :\n", "   - Développer scoring automatique pour nouveaux visiteurs\n", "   - Implémenter géo-marketing ciblé par région/état\n", "   - Exploiter la saisonnalité pour campagnes temporelles\n", "   - Tests A/B sur messages de réactivation\n", "\n", "3. **🎯 <PERSON><PERSON>n terme (6 mois)** :\n", "   - Segmentation dynamique des nouveaux clients\n", "   - <PERSON><PERSON><PERSON><PERSON> prédictifs de propension au 2e achat\n", "   - Dashboard de suivi acquisition/réactivation\n", "   - Intégration données comportementales web/mobile\n", "\n", "### 🔄 Prochaines étapes\n", "\n", "➡️ **Notebook 5 :** Simulation de maintenance et contrat de service  \n", "➡️ **Implémentation :** Tests des recommandations sur échantillons  \n", "➡️ **Suivi :** Mise en place des KPIs et tableaux de bord\n", "\n", "---\n", "\n", "**🎉 Recommandations marketing \"First Purchase\" prêtes pour implémentation !**  \n", "**📊 Stratégies d'acquisition et réactivation personnalisées par segment.**  \n", "**🚀 Focus sur la génération du 2e achat et l'expansion de la base client.**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}